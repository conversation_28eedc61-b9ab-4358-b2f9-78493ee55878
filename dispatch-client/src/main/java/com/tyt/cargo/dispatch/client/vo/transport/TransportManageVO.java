package com.tyt.cargo.dispatch.client.vo.transport;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2022/11/11
 */
@Data
public class TransportManageVO implements Serializable {

    private static final long serialVersionUID = -1L;


    /*
       优车字段
     */
    private Integer excellentGoods;
    /**
     * 货源id
     */
    private Long srcMsgId;
    /**
     * 货主账号
     */
    private String uploadCellPhone;

    /**
     * 工单状态：0-待处理，1-处理中，2-已结单
     */
    private Integer workOrderStatus;

    /**
     * 首次响应时间
     */
    private Date responseTime;

    /**
     * 首次响应时间
     */
    private String responseTimeStr;

    /**
     * 结单时间
     */
    private Date endTime;

    /**
     * 结单时间
     */
    private String endTimeStr;

    /**
     * 维护人
     */
    private String maintainerName;

    /**
     * 价值货源类型：1-新客首单货源，2-优车2.0货源
     */
    private Integer valueType;

    /**
     * 价值货源类型名称
     */
    private String valueTypeName;

    /**
     * 是否跟进：0-否，1-是
     */
    private Integer followUp;

    /**
     * 是否首单：0-否，1-是
     */
    private Integer firstOrder;

    /**
     * 是否前3单：0-否，1-是
     */
    private Integer topThreeOrder;

    /**
     * 查看3次以上人数
     */
    private Integer viewThreeNum;

    /**
     * 是否加价：0-否，1-是
     */
    private Integer addPrice;

    /**
     * 加价时间
     */
    private Date addPriceTime;

    /**
     * 最新跟进时间
     */
    private Date followUpTime;

    /**
     * 市场跟进人员
     */
    private String marketFollowUpUser;

    /**
     * 跟进人
     */
    private String followUpUser;

    /**
     * 是否直客：0-否，1是
     */
    private Integer directCustomer;

    /**
     * 是否价值用户：0-否，1-是
     */
    private Integer valuableUser;

    /**
     * 是否有司机出价：0-否，1-是
     */
    private Integer driverGivePrice;

    /**
     * 指派类型：1-专车指派，2-好差货指
     */
    private Integer assignType;

    /**
     * 专车好货质量分
     */
    private BigDecimal limGoodModelScore;

    /**
     * 司机最新出价
     */
    private BigDecimal driverLatestPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 发布时间
     */
    private String publishTime;
    /**
     * 撤销时间
     */
    private String cancelTime;
    /**
     * 成交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date doneTime;

    /**
     * 出发地
     */
    private String startPoint;
    /**
     * 出发地详细地址
     */
    private String startDetailAdd;
    /**
     * 目的地
     */
    private String destPoint;
    /**
     * 目的地详细地址
     */
    private String destDetailAdd;
    /**
     * 货物名称
     */
    private String taskContent;
    /**
     * 重量 单位吨
     */
    private String weight;
    /**
     * 长
     */
    private String length;
    /**
     * 宽
     */
    private String wide;
    /**
     * 高
     */
    private String high;

    /**
     * 货源查看次数
     */
    private Integer viewNum;

    /**
     * 是否添加沟通记录
     */
    private Boolean haveContactRecord;

    /**
     * 运距
     */
    private Integer distance;

    /**
     * 装车时间
     */
    private String loadingTime;

    /**
     * 卸车时间
     */
    private String unloadTime;

    /**
     * 调车数量
     */
    private Integer shuntingQuantity;
    /**
     * 订金（信息费）
     */
    private BigDecimal infoFee;
    /**
     * 订金是否退还 （0不退还；1退还）
     */
    private Integer refundFlag;

    /**
     * 用车类型：1-整车，2-零担
     */
    private String useCarType;

    /**
     * 驾驶此类货物
     */
    private String driverDriving;

    /**
     * 运费
     */
    private String price;
    /**
     * 货源类型 1：电议  2：一口价
     */
    private Integer publishType;
    /**
     * 货主出价
     */
    private BigDecimal ownerFreight;

    /**
     * 发货人名称
     */
    private String publishUserName;
    /**
     * 调度人名称
     */
    private String dispatcherName;

    /**
     * 货主id
     */
    private Long userId;
    /**
     * 发货状态(0:已过期  1：发布中  4：成交  5：发布中)
     */
    private Integer status;

    /**
     * 给货货主手机号
     */
    private String giveGoodsPhone;

    /**
     * 给货货主名称
     */
    private String giveGoodsName;

    /**
     * 修改时间
     */
    private String mtime;

    /**
     * 货源来源（仅用来区分是否是由货主小程序发货，除了货主小程序外其他均显示为调度发货）
     */
    private String ownerSourceType;

    /**
     * 代调六期需求
     */
    private String revokeReason;//撤销原因

    private String revokeReasonNew;//撤销原因（新）

    private String specificReason;// 具体原因

    private String remark;// 备注
    /**
     * 6350 来源筛选  1：货主 2：代调 3：小程序 4：运满满货源
     */
    private int sourceType;

    /**
     * 技术服务费
     */
    private BigDecimal tecServiceFee;


    /**
     * 优车运价货源 1:是；null：否
     */
    private Integer goodCarPriceTransport;

    private String labelJson;

    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 开票平台ID
     */
    private Long invoiceSubjectId;

    /**
     * 专车货源是否可大厅抢单 0：否；1：是
     */
    private Integer declareInPublic;

    /**
     * 开票货源指派车方联系方式
     */
    private String assignCarTel;

    /**
     * 货名备注
     */
    private String machineRemark;

    /**
     * 货源未成交原因
     */
    private String notDealReason;

    /**
     * 跟进人对应的组
     */
    private String followUpUserDepartment;

    /**
     * 跟进次数
     */
    private Integer followUpTimes;

    /**
     * 派单时间
     */
    private Date dispatchTime;

    /**
     * 成交司机id
     */
    private Long doneUserId;

    /**
     * 成交车型
     */
    private String goodTypeName;

    /**
     * 成交时长（首发、成交）
     */
    private String donePeriod;

    /**
     * 跟进优先级：1高/2中高/3中/4低，默认全部
     */
    private Integer followPriority;

    /**
     * 签约合作商id
     */
    private Long cargoOwnerId;

    /**
     * 签约合作商名称
     */
    private String cargoOwnerName;

    /**
     * 价值货源是否可指派 1:是；0:否
     */
    private Integer valuableTransportCanDesignate;

}
