package com.tyt.cargo.dispatch.client.form.transport;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class SaveWayBillReq {
    //车方id
    @NotNull(message = "用户id不能为空")
    private Long userId;
    //货源id
    @NotNull(message = "货源id不能为空")
    private Long goodsId;
    //车方联系电话
    @NotNull(message = "联系电话不能为空")
    private String telephone;
    //运前信息费（单位元）
    private Long agencyMoney;
    //Integer 1详情页面的支付
    private Integer carOwnerPayType;
    //运费（单位元）
    @NotNull(message = "运费不能为空")
    private Integer carriageFee;
    private Long tecServiceFee;

    /**
     * 是否是由价值货源列表指派（1： 是，null、0：否）
     */
    private Integer valuableTransport;
}
