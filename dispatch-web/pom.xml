<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.tyt.cargo</groupId>
    <artifactId>dispatch-web</artifactId>
    <version>0.0.2-SNAPSHOT</version>

    <name>dispatch-web</name>
    <description>dispatch-web</description>

    <properties>
        <cargo-cloud.version>0.0.2-SNAPSHOT</cargo-cloud.version>
    </properties>

    <parent>
        <groupId>com.tyt.cargo</groupId>
        <artifactId>dispatch</artifactId>
        <version>0.0.2-SNAPSHOT</version>
    </parent>

    <dependencies>

        <!-- ==================== 基础依赖 ==================== -->

        <dependency>
            <groupId>com.tyt.cargo</groupId>
            <artifactId>cargo-web</artifactId>
            <version>${cargo-cloud.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tyt.cargo</groupId>
            <artifactId>cargo-redis</artifactId>
            <version>${cargo-cloud.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tyt.cargo</groupId>
            <artifactId>cargo-db</artifactId>
            <version>${cargo-cloud.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tyt.cargo</groupId>
            <artifactId>dispatch-client</artifactId>
            <version>${cargo-cloud.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tyt.cargo</groupId>
            <artifactId>cargo-model</artifactId>
            <version>${cargo-cloud.version}</version>
        </dependency>

        <!-- 消息中心基础 -->
        <dependency>
            <groupId>com.tyt.messagecenter</groupId>
            <artifactId>message-center-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!-- ==================== spring boot ==================== -->

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.vdurmont/emoji-java -->
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
        </dependency>

        <!-- "org.springframework.security:spring-security-jwt:1.0.9.RELEASE", -->

        <!-- org.springframework.boot:spring-boot-configuration-processor -->

        <!-- ==================== 其他 ==================== -->
        <!-- https://mvnrepository.com/artifact/com.huaban/jieba-analysis -->
        <dependency>
            <groupId>com.huaban</groupId>
            <artifactId>jieba-analysis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-retrofit-spring-boot-starter</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-spring-boot2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tfc</groupId>
            <artifactId>word-search</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.5</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.janeluo</groupId>
            <artifactId>ikanalyzer</artifactId>
            <version>2012_u6</version>
        </dependency>


    </dependencies>

    <build>
        <finalName>dispatch-web</finalName>

        <plugins>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${mybatis-generator.version}</version>
                <configuration>
                    <configurationFile>src/main/resources/generator/tk-mybatis-generator.xml</configurationFile>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>

                <dependencies>
                    <dependency>
                        <groupId>org.mybatis.generator</groupId>
                        <artifactId>mybatis-generator-core</artifactId>
                        <version>${mybatis-generator.version}</version>
                    </dependency>

                    <!-- https://mvnrepository.com/artifact/tk.mybatis/mapper-generator -->
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper-generator</artifactId>
                        <version>1.1.5</version>
                    </dependency>

                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${generator-mysql-connector.version}</version>
                    </dependency>

                    <dependency>
                        <groupId>com.softwareloop</groupId>
                        <artifactId>mybatis-generator-lombok-plugin</artifactId>
                        <version>${mybatis-generator-lombok.version}</version>
                    </dependency>

                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.tyt.cargo.dispatch.web.DispatchWebApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
