package com.tyt.cargo.dispatch.web.util;

import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.WordDictionary;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class GoodsUnique {

    //快速查找容器
    private static Hashtable<String,String> user_dict_ht = new Hashtable();

    //分词器
    private static WordDictionary wordDictionary = WordDictionary.getInstance();
    private static JiebaSegmenter segmenter = new JiebaSegmenter();

    /**
     * 清除字典
     */
    public static void resetDictionary() {
        log.info("resetDictionary_start ...... ");
        wordDictionary.freqs.clear();
        wordDictionary.loadDict();

        user_dict_ht.clear();

        log.info("resetDictionary_done ...... ");
    }

    /**
     * 加载自定义词典，文件目前是从数据库读取，并生成的
     * 第一列是名称，第二列是出现频率，第三列是校正名称
     * @param path
     */
    public static void loadMyDict(String path) {
        log.info("start_loadDictionary : " + path);

        Path dictPath = Paths.get(path);

        wordDictionary.loadUserDict(dictPath);

        //2，内存查找表也加载
        try {
            try (BufferedReader br = Files.newBufferedReader(dictPath, StandardCharsets.UTF_8)) {
                while (br.ready()) {
                    String line = br.readLine();
                    String[] tokens = line.split("[\t ]+");
                    if (tokens.length >= 2) {
                        String word = tokens[0];
                        if (tokens.length == 3) {
                            user_dict_ht.put(word, tokens[2]);
                        } else {
                            user_dict_ht.put(word, word);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Load user dict Error!" + dictPath.toString(), ex);
        }
    }

    //判断字符串是否是整数
    private static boolean isNumeric(String str) {
        Pattern pt = Pattern.compile("\\d+");
        return pt.matcher(str).matches();
    }

    // byte数组转十六进制字符串
    public static String getHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for(byte b : bytes) {
            String hex = Integer.toHexString(b & 0xFF);
            if(hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex);
        }
        return  sb.toString();
    }

    // md5摘要
    public static String getMD5String(String str) {
        String hexStr = "";
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(str.getBytes("utf-8"));
            hexStr = getHexString(digest);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return  hexStr;
    }

    // 正则匹配所有子字符串
    public static  List<String> getSubStrings(String line, String regex) {
        List<String> strs = new ArrayList<String>();
        Pattern pt = Pattern.compile(regex);
        Matcher match = pt.matcher(line);
        while (match.find()) {
            strs.add(match.group());
        }

        return  strs;
    }

    //分词判断
    public static String conform_word(String line) {
        List<String>  last_str = new ArrayList<>();
        List<String> type_str = new ArrayList<>();
        if(line.isEmpty()) { return ""; }
        boolean isContain = false;
        System.out.println("line = "+line );

        List<String> segList = segmenter.sentenceProcess(line);
        Pattern pattern = Pattern.compile("[0-9]*[A-Z]*[0-9]+[A-Z]*");
        int len=segList.size();
        for (int i=0;i<len;i++) {
            System.out.println("word = "+segList.get(i) );


            if(pattern.matcher(segList.get(i)).matches() && segList.get(i).length()>1){
                if(( i<(len-1)  &&  user_dict_ht.containsKey(segList.get(i+1)))|| ( i>0  && user_dict_ht.containsKey(segList.get(i-1))) ){
                    type_str.add(segList.get(i));
                }

                continue;
            }


            if(user_dict_ht.containsKey(segList.get(i))) {
                isContain = true;
                last_str.add(user_dict_ht.get(segList.get(i)));
            }
        }

        // 无关键词，直接返回
        if(!isContain){
            return  "";
        }

        //多个型号，暂时不考虑
        if(type_str.size()>1){
            return "";
        }

        String type=type_str.size()==1 ?type_str.get(0):"";
        String result="";
        int lastLen=last_str.size();
        for(int i=0; i<lastLen;i++ ){
            if(i==lastLen-1){
                result+=(type+last_str.get(i));
            }else {
                result+=last_str.get(i);
            }

        }
        return  result;
    }

    //提取关键信息
    // 成功，返回提取的字符串；
    // 如果包含多台设备，或提取结果为空，表示失败
    // 失败，返回原输入字符串！！
    public static String extractKeyInfo(String input) {
        String content_name = "";
        if (input.isEmpty()) { return  content_name; }

        String text = input.trim();
        //型号里的-  / ""

        text=text.replaceAll("-","").replaceAll("/","").replaceAll(" ","");

        // 1、处理数量单位
        // 删除所有带单位的数字
        text = text.replaceAll("[长宽高]?\\d+(?:\\.\\d+)?[米元][长宽高]?", "")
                .replaceAll("\\d+[千百万]元","");
        // 删除"多少台"车
        text = text.replaceAll("[一二三多四五六七八九]台平?板?车|\\d+台平?板?车|[一1]台", "");
        // 提取多少台(货)，先台后吨
        String reg_tai = "(?:\\d+|[两三四五六七八九十二多])台";
        List<String> tai_list = getSubStrings(text, reg_tai);
        if(tai_list.size() == 1) {
            String tai_str = tai_list.get(0);
            tai_str = tai_str.replaceFirst("两台", "2台")
                    .replaceFirst("三台","3台")
                    .replaceFirst("四台","4台");
            content_name += tai_str;
        }
        // 提取多少吨(货)
        String reg_dun = "重?\\d+(?:\\.\\d+)?吨重?";
        List<String> dun_list = getSubStrings(text, reg_dun);
        if(dun_list.size() == 1) {
            content_name += dun_list.get(0);
        }
        // 如果是多台多吨，直接返回
        if(tai_list.size() > 1 || dun_list.size() > 1) {
            return text;
        }

        // 删除多少台/多少吨
        text = text.replaceAll(reg_tai, "").replaceAll(reg_dun, "");

        // 2, 删除无用的数字
        // 删除形如"11.5*3.6*3"、"159****0637" 和浮点数
        text = text.replaceAll("\\d+(\\.\\d+)?[\\*×xXⅩ✘]+\\d+(\\.\\d+)?\\*\\d+(\\.\\d+)?", "")
                .replaceAll("\\d+[\\*×xXⅩ✘]+\\d+","")
                .replaceAll("\\d+(\\.\\d+){1}", "");
        // 3，还原缩略词
        // 包含[0-9]+挖 但是不包含“挖机”的，把挖字替换成挖机
        if(text.matches("(.*)\\d+挖(.*)") && !text.contains("挖机")) {
            text = text.replaceAll("挖","挖机");
        }
        // 包含[0-9]+铲 但是不包含“铲车”的，把铲字替换成铲车
        if(text.matches("(.*)\\d+[铲产](.*)") && !text.contains("铲车")) {
            text = text.replaceAll("[铲产]","铲车");
        }

        // 判断是否在词表来过滤
        String left_str = conform_word(text);
        if(left_str.length() > 0) {
            content_name += left_str;
        } else {
            // 不能在词表确认，重新赋值为输入字符串
            content_name = input.trim();
        }

        return  content_name;
    }

    //组合了出发地、目的地和提取的货物信息
    public static String getKeyInfoWithTSLine(String start, String dest, String task_content) {
        String composite = start + dest + extractKeyInfo(task_content);
        return  getMD5String(composite);
    }

    /**
     * 把line拆成词组返回
     * @param line
     * @return
     */
    public static  List<String> getListkeys(String line){
        if(null==line ||line.isEmpty()){
            return null;
        }
        List<String> segList = segmenter.sentenceProcess(line);
        return segList;

    }


    public static void main(String[] args) {

        //首先，加载关键词
        //loadMyDict();
        assert (user_dict_ht.size()>0);
        //开始提取
        String[] sentences =
                new String[] {
                        "200挖掘机",

                };
        long startTime=System.currentTimeMillis();
        for (String sentence: sentences) {
            //   String info = GoodsUnique.getKeyInfoWithTSLine("333","666", sentence);
            List<String> res=getListkeys(sentence);
            for(String key :res){
                System.out.println("源货源内容： "+sentence+" === 提取关键词： " +key+"=="+user_dict_ht.get(key));
            }
            //System.out.println("源货源内容： "+sentence+" === 提取关键词： " +res.toString() );
        }
        long endTime=System.currentTimeMillis();
        Pattern pattern = Pattern.compile("[0-9]*[A-Z]*[0-9]+[A-Z]*");
        System.out.println(endTime-startTime+" uiiwewe 99"+ pattern.matcher("HNF200C").matches()  );
    }
}


