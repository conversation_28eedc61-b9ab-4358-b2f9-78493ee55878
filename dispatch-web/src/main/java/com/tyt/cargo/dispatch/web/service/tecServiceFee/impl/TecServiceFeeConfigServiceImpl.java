package com.tyt.cargo.dispatch.web.service.tecServiceFee.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.cargo.core.util.CityUtil;
import com.tyt.cargo.core.util.EntityUtils;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.db.model.PageParameter;
import com.tyt.cargo.db.tool.CustomPageHelper;
import com.tyt.cargo.dispatch.client.api.TransportTecserviceFeeClient;
import com.tyt.cargo.dispatch.client.constant.PlatApiConstant;
import com.tyt.cargo.dispatch.client.constant.RemoteApiConstant;
import com.tyt.cargo.dispatch.client.dto.CheckIsNeedFreeTecServiceFeeVO;
import com.tyt.cargo.dispatch.client.dto.DrawCommissionReq;
import com.tyt.cargo.dispatch.client.enums.PriceSourceEnum;
import com.tyt.cargo.dispatch.client.enums.PublishTypeEnum;
import com.tyt.cargo.dispatch.client.enums.SourceTypeEnum;
import com.tyt.cargo.dispatch.client.vo.custom.MeetCommissionRulesResult;
import com.tyt.cargo.dispatch.client.vo.plat.CommissionTypeBean;
import com.tyt.cargo.dispatch.client.vo.remote.CarryPriceReq;
import com.tyt.cargo.dispatch.client.vo.transport.GoodModelResult;
import com.tyt.cargo.dispatch.client.vo.transport.SuggestPriceReq;
import com.tyt.cargo.dispatch.web.bean.tecServiceFee.*;
import com.tyt.cargo.dispatch.web.enums.ExcellentEnum;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.*;
import com.tyt.cargo.dispatch.web.mybatis.mapper.base.*;
import com.tyt.cargo.dispatch.web.service.base.impl.DispatchBaseServiceImpl;
import com.tyt.cargo.dispatch.web.service.bi.BiResponse;
import com.tyt.cargo.dispatch.web.service.bi.NewBiService;
import com.tyt.cargo.dispatch.web.service.plat.PlatHttpService;
import com.tyt.cargo.dispatch.web.service.remote.CarryPriceService;
import com.tyt.cargo.dispatch.web.service.tecServiceFee.TecServiceFeeConfigService;
import com.tyt.cargo.redis.service.tyt.TytConfigService;
import com.tyt.cargo.web.model.plat.TytResultMsgBean;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TecServiceFeeConfigServiceImpl extends DispatchBaseServiceImpl implements TecServiceFeeConfigService {

    @Value("${spring.profiles.active}")
    private String profile;

    @Autowired
    TytTransportTecServiceFeeMapper tytTransportTecServiceFeeMapper;

    @Autowired
    TytTecServiceFeeConfigMapper tytTecServiceFeeConfigMapper;

    @Autowired
    TytTecServiceFeeStageConfigMapper tytTecServiceFeeStageConfigMapper;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private PlatHttpService platHttpService;

    @Autowired
    private CarryPriceService carryPriceService;

    @Autowired
    private NewBiService newBiService;

    @Autowired
    private TransportTecserviceFeeClient transportTecserviceFeeClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private TytTecServiceFeeProportionConfigMapper tytTecServiceFeeProportionConfigMapper;

    @Autowired
    private TytTecServiceFeeDiscountConfigMapper tytTecServiceFeeDiscountConfigMapper;

    public static final String COMMISSION_STAGE_CONFIG_AB_NUM_CACHE = "commissionStageConfigABNumCache";

    public static final String COMMISSION_PROPORTION_TRANSPORT_NUM = "commissionProportionTransportNum";

    @Override
    public TytTecServiceFeeConfigToComputResult computeTecServiceFeeBtTransportData(TytTransport tytTransport, TytTransportMain oldTransportMain, MeetCommissionRulesResult meetCommissionRules) {
        //调用BI接口判断该货源是否抽佣，如果抽佣则通过配置计算技术服务费，并给该货源打上抽佣货源标识
        boolean isCommissionTransport = false;
        if (tytTransport.getRefundFlag() == null) {
            return null;
        }

        Long firstPublishTimeMinute = null;
        if (oldTransportMain != null && oldTransportMain.getId() != null && oldTransportMain.getCtime() != null) {
            //判断oldTransportMain.getCtime()是不是今天
            LocalDate ctimeLocalDate = oldTransportMain.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now()) && oldTransportMain.getStatus() != null && oldTransportMain.getStatus() != 4) {
                firstPublishTimeMinute = Duration.between(oldTransportMain.getCtime().toInstant(), new Date().toInstant()).toMinutes();
            } else {
                //不是当天，则时间差为0
                firstPublishTimeMinute = 0L;
            }
        }

        TytTecServiceFeeConfig tytTecServiceFeeConfig = new TytTecServiceFeeConfig();

        //判断货源是否为专车
        boolean isSpecialCar = false;
        if (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 2) {
            isSpecialCar = true;
        }

        //判断货源是否为YMM
        boolean isYMM = false;
        if (tytTransport.getSourceType().equals(SourceTypeEnum.ymm.getCode())) {
            isYMM = true;
        }

        //判断货源是否为优车1.0
        boolean isExcellentGoods = false;
        if (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 1) {
            isExcellentGoods = true;
        }

        if (isSpecialCar) {
            tytTecServiceFeeConfig.setApplyTransportType(1);
            if (tytTransport.getCargoOwnerId() == null || tytTransport.getCargoOwnerId() == 1 || tytTransport.getCargoOwnerId() == 0) {
                //专车签约合作商为平台
                tytTecServiceFeeConfig.setSpecialCarCooperativeType(1);
            } else {
                tytTecServiceFeeConfig.setSpecialCarCooperativeType(2);
            }
        } else if (isYMM) {
            tytTecServiceFeeConfig.setApplyTransportType(5);
        } else if (isExcellentGoods) {
            tytTecServiceFeeConfig.setApplyTransportType(3);
        } else {
            //普通货源
            tytTecServiceFeeConfig.setApplyTransportType(2);
        }

        tytTecServiceFeeConfig.setRefundFlagType(tytTransport.getRefundFlag() == 0 ? 2 : 1);
        if (tytTransport.getPublishType() == PublishTypeEnum.tel.getCode().shortValue()) {
            if (StringUtils.isNotBlank(tytTransport.getPrice()) && !tytTransport.getPrice().equals("0")) {
                //电议有价
                tytTecServiceFeeConfig.setPricePublishType(1);
            } else {
                //电议无价
                tytTecServiceFeeConfig.setPricePublishType(2);
            }
        } else {
            //一口价
            tytTecServiceFeeConfig.setPricePublishType(3);
        }

        boolean isGoodCarPriceTransport = false;
        if (StringUtils.isNotBlank(tytTransport.getLabelJson())) {
            JSONObject jsonObject = JSONObject.parseObject(tytTransport.getLabelJson());
            if (jsonObject.containsKey("goodCarPriceTransport")) {
                isGoodCarPriceTransport = true;
            }
        }

        isCommissionTransport = getMeetCommissionRules(tytTransport, meetCommissionRules, isGoodCarPriceTransport, isCommissionTransport);

        if (!isCommissionTransport) {
            return null;
        }

        //无价货源优车指导价
        String goodCarPriceTransportCarryPrice = null;

        //默认技术服务费
        Integer defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee", 50);
        String priceString = tytTransport.getPrice();
        if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
            //使用优车指导价
            priceString = makePriceByGoodCarPriceTransportCarryPrice(tytTransport);
            if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
                //用户没有手填运费并且也没有通过BI接口获取到建议运费
                defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee_no_price", 20);
            } else {
                goodCarPriceTransportCarryPrice = priceString;
            }
        }
        log.info("抽佣金额计算-本次发货计算抽佣金额时用的运费为：{}", priceString);

        //抽佣金额计算抹零方式 0：对10向下取整；1:向下取整
        Integer tecServiceFeeRoundingType = tytConfigService.getIntValue("tec_service_fee_rounding_type", 0);

        //调用BI接口获取抽佣分数
        BigDecimal commissionScore = new BigDecimal("-1");
        if (StringUtils.isNotBlank(tytTransport.getPrice()) && new BigDecimal(tytTransport.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            commissionScore = checkCommissionScore(tytTransport);
            if (commissionScore == null || commissionScore.compareTo(BigDecimal.ZERO) < 0) {
                commissionScore = new BigDecimal("-1");
            }
        }
        log.info("抽佣金额计算-根据货源信息获取抽佣分数BI返回结果：{}", commissionScore);

        BigDecimal checkScore;
        if (StringUtils.isNotBlank(tytTransport.getPrice()) && new BigDecimal(tytTransport.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            checkScore = commissionScore;
        } else {
            GoodModelResult goodModelResult = checkInstantGrab(tytTransport);
            if (goodModelResult != null && goodModelResult.getScore() != null) {
                checkScore = goodModelResult.getScore();
            } else {
                checkScore = null;
            }
        }
        log.info("抽佣金额计算-根据分数判断落在哪个阶梯：{}", checkScore);

        TytTecServiceFeeConfig tecServiceFeeConfig = tytTecServiceFeeConfigMapper.getByConditionsUseToCompute(tytTecServiceFeeConfig);
        if (tecServiceFeeConfig != null) {
            TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = new TytTecServiceFeeConfigToComputResult();
            BeanUtils.copyProperties(tecServiceFeeConfig, tytTecServiceFeeConfigToComputResult);
            tytTecServiceFeeConfigToComputResult.setUseCommissionScoreStageConfig(true);
            tytTecServiceFeeConfigToComputResult.setCommissionScore(commissionScore);
            tytTecServiceFeeConfigToComputResult.setGoodCarPriceTransportCarryPrice(goodCarPriceTransportCarryPrice);

            //满足条件一的时间，如果不满足则为null
            Date matchConditionEarliestTime = null;
            if (oldTransportMain != null && oldTransportMain.getSrcMsgId() != null) {
                matchConditionEarliestTime = getMatchConditionLastTime(oldTransportMain
                        , tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount(), tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount());
            } else {
                if ((tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount() == 0)
                        || (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount() != null && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount() == 0)) {
                    //当日新发货源并且查看拨打配置有0的，则满足条件一的时间为当前发货时间
                    matchConditionEarliestTime = new Date();
                }
            }

            //未满足条件一的情况下是否符合直接免佣条件
            boolean overTimeToFree = false;
            if (matchConditionEarliestTime == null) {
                overTimeToFree = checkIsNeedFreeTecServiceFeeByTime(tytTecServiceFeeConfigToComputResult, firstPublishTimeMinute);
                if (overTimeToFree) {
                    List<Integer> needFreeTecTypeList = new ArrayList<>();
                    needFreeTecTypeList.add(5);
                    tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                }
            }

            boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(tytTransport, isGoodCarPriceTransport, tytTecServiceFeeConfigToComputResult);
            boolean freeTec = overTimeToFree || matchConditionToFree;

            //根据是否使用抽佣分数获取不同类型的分段配置
            List<TytTecServiceFeeStageConfig> stageConfigs = tytTecServiceFeeStageConfigMapper.getByConfigIdAndType(tecServiceFeeConfig.getId(), 2);

            if (CollectionUtils.isNotEmpty(stageConfigs) && StringUtils.isNotBlank(priceString) && new BigDecimal(tytTransport.getPrice()).compareTo(BigDecimal.ZERO) > 0 && checkScore != null) {
                BigDecimal tecServiceFee = new BigDecimal(defaultTecServiceFee);
                BigDecimal tecServiceFeeAfterDiscount = new BigDecimal(defaultTecServiceFee);
                BigDecimal price = new BigDecimal(priceString);
                for (TytTecServiceFeeStageConfig stageConfig : stageConfigs) {
                    if (stageConfig.getPriceMin() != null && stageConfig.getPriceMax() != null
                            && checkScore.compareTo(stageConfig.getPriceMin()) > 0 && checkScore.compareTo(stageConfig.getPriceMax()) <= 0) {

                        //TODO 取百分比用新方法
                        List<TytTecServiceFeeProportionConfig> proportionConfig = tytTecServiceFeeProportionConfigMapper.getTytTecServiceFeeProportionConfigByStageId(stageConfig.getId());
                        int transportProportionNum = ThreadLocalRandom.current().nextInt(100, 200);
                        if (oldTransportMain != null && oldTransportMain.getSrcMsgId() != null) {
                            String transportNumString = stringRedisTemplate.opsForValue().get(COMMISSION_PROPORTION_TRANSPORT_NUM + ":" + oldTransportMain.getSrcMsgId());
                            if (StringUtils.isNotBlank(transportNumString) && StringUtils.isNumeric(transportNumString.trim())) {
                                transportProportionNum = Integer.parseInt(transportNumString);
                            }
                        }
                        tytTecServiceFeeConfigToComputResult.setTransportProportionNum(transportProportionNum);
                        TytTecServiceFeeProportionConfig serviceFeeProportionConfig = null;
                        BigDecimal tecServiceFeeRate = BigDecimal.ZERO;
                        if (CollectionUtils.isNotEmpty(proportionConfig)) {
                            serviceFeeProportionConfig = proportionConfig.get(transportProportionNum % proportionConfig.size());
                            if (serviceFeeProportionConfig.getTecServiceFeeRate() != null && serviceFeeProportionConfig.getTecServiceFeeRate().compareTo(BigDecimal.ZERO) > 0) {
                                tecServiceFeeRate = serviceFeeProportionConfig.getTecServiceFeeRate();
                            }
                        }

                        //在配置的运费高低区间内，做开右闭
                        BigDecimal tecServiceFeeSnap = price.multiply(tecServiceFeeRate.movePointLeft(2)).setScale(2, RoundingMode.DOWN);


                        if (stageConfig.getTecServiceFeeMin() != null && stageConfig.getTecServiceFeeMax() != null) {
                            if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMin()) < 0) {
                                tecServiceFeeSnap = stageConfig.getTecServiceFeeMin();
                            } else if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMax()) > 0) {
                                tecServiceFeeSnap = stageConfig.getTecServiceFeeMax();
                            }
                        }

                        //算最终折扣前抹零，并记录折前价格
                        if (tecServiceFeeRoundingType == 0) {
                            tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                        } else {
                            tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                        }
                        BigDecimal tecServiceFeeAfterDiscountSnap = tecServiceFeeSnap;

                        TytTecServiceFeeDiscountConfig tytTecServiceFeeDiscountConfig = null;
                        //最终打折节点，记录折前价
                        if (matchConditionEarliestTime != null && serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                            tecServiceFeeAfterDiscountSnap = new BigDecimal(tecServiceFeeSnap.toString());

                            //TODO 取折扣用新方法
                            long matchConditionEarliestTimeBetweenNowMin = Duration.between(matchConditionEarliestTime.toInstant(), new Date().toInstant()).toMinutes();
                            tytTecServiceFeeDiscountConfig = tytTecServiceFeeDiscountConfigMapper.getTytTecServiceFeeDiscountConfigByProportionIdAndMin(serviceFeeProportionConfig.getId(), matchConditionEarliestTimeBetweenNowMin);
                            if (tytTecServiceFeeDiscountConfig != null && tytTecServiceFeeDiscountConfig.getDiscount() != null) {
                                tecServiceFeeSnap = tecServiceFeeSnap.multiply(tytTecServiceFeeDiscountConfig.getDiscount().movePointLeft(1)).setScale(2, RoundingMode.DOWN);
                                if (tytTecServiceFeeDiscountConfig.getDiscount().compareTo(BigDecimal.ZERO) == 0) {
                                    if (tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList() != null) {
                                        tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList().add(5);
                                    } else {
                                        List<Integer> needFreeTecTypeList = new ArrayList<>();
                                        needFreeTecTypeList.add(5);
                                        tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                                    }
                                    freeTec = true;
                                }
                            }
                        }

                        //算完最终折后价后再次抹零
                        if (tecServiceFeeRoundingType == 0) {
                            tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                        } else {
                            tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                        }

                        tecServiceFee = tecServiceFeeSnap;
                        tecServiceFeeAfterDiscount = tecServiceFeeAfterDiscountSnap;

                        tytTecServiceFeeConfigToComputResult.setPriceMax(stageConfig.getPriceMax());
                        tytTecServiceFeeConfigToComputResult.setPriceMin(stageConfig.getPriceMin());
                        tytTecServiceFeeConfigToComputResult.setTecServiceFeeMax(stageConfig.getTecServiceFeeMax());
                        tytTecServiceFeeConfigToComputResult.setTecServiceFeeMin(stageConfig.getTecServiceFeeMin());
                        tytTecServiceFeeConfigToComputResult.setTecServiceFeeRate(tecServiceFeeRate);
                        if (tytTecServiceFeeDiscountConfig != null) {
                            tytTecServiceFeeConfigToComputResult.setDiscountTime(tytTecServiceFeeDiscountConfig.getDiscountTime());
                            tytTecServiceFeeConfigToComputResult.setDiscount(tytTecServiceFeeDiscountConfig.getDiscount());
                        }

                        if (serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                            List<TytTecServiceFeeDiscountConfig> tytTecServiceFeeDiscountConfigByProportionId = tytTecServiceFeeDiscountConfigMapper.getTytTecServiceFeeDiscountConfigByProportionId(serviceFeeProportionConfig.getId());
                            if (CollectionUtils.isNotEmpty(tytTecServiceFeeDiscountConfigByProportionId)) {
                                tytTecServiceFeeConfigToComputResult.setDiscountConfig(JSON.toJSONString(tytTecServiceFeeDiscountConfigByProportionId));

                                //构造BI要的整体折扣信息例如：60,8.0,120,5.0,1440,0.0
                                StringBuilder allDiscount = new StringBuilder();
                                for (TytTecServiceFeeDiscountConfig tecServiceFeeDiscountConfig : tytTecServiceFeeDiscountConfigByProportionId) {
                                    allDiscount.append(tecServiceFeeDiscountConfig.getDiscountTime()).append(",").append(tecServiceFeeDiscountConfig.getDiscount()).append(",");
                                }
                                if (allDiscount.length() > 0) {
                                    allDiscount.deleteCharAt(allDiscount.length() - 1);
                                }
                                tytTecServiceFeeConfigToComputResult.setAllDiscount(allDiscount.toString());
                            }
                        }
                    }
                }
                if (freeTec) {
                    //超时免佣
                    tecServiceFee = new BigDecimal(0);
                }
                tytTecServiceFeeConfigToComputResult.setTecServiceFee(tecServiceFee);
                tytTecServiceFeeConfigToComputResult.setTecServiceFeeAfterDiscount(tecServiceFeeAfterDiscount);
            } else {
                tytTecServiceFeeConfigToComputResult.setTecServiceFeeAfterDiscount(new BigDecimal(defaultTecServiceFee));
                if (freeTec) {
                    //超时免佣
                    tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(0));
                } else {
                    tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
                }
            }
            return tytTecServiceFeeConfigToComputResult;
        } else {
            TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = new TytTecServiceFeeConfigToComputResult();
            tytTecServiceFeeConfigToComputResult.setCarMemberType(0);
            tytTecServiceFeeConfigToComputResult.setPrivacyPhoneType(1);
            tytTecServiceFeeConfigToComputResult.setFreeTecServiceFeeType(1);
            tytTecServiceFeeConfigToComputResult.setFreeTecServiceFeeTime(60);
            tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
            tytTecServiceFeeConfigToComputResult.setUseCommissionScoreStageConfig(true);
            tytTecServiceFeeConfigToComputResult.setCommissionScore(commissionScore);
            tytTecServiceFeeConfigToComputResult.setTecServiceFeeAfterDiscount(new BigDecimal(defaultTecServiceFee));

            if (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType() != null
                    && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime() != null
                    && firstPublishTimeMinute != null
                    && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType() == 1
                    && firstPublishTimeMinute > tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime()) {
                tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(0));
                List<Integer> needFreeTecTypeList = new ArrayList<>();
                needFreeTecTypeList.add(5);
                tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(needFreeTecTypeList);
            }

            boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(tytTransport, isGoodCarPriceTransport, tytTecServiceFeeConfigToComputResult);
            if (matchConditionToFree) {
                tytTecServiceFeeConfigToComputResult.setTecServiceFee(new BigDecimal(0));
            }

            return tytTecServiceFeeConfigToComputResult;
        }
    }

    @Override
    public boolean checkIsNeedFreeTecServiceFeeByTransport(TytTransport tytTransport, boolean isGoodCarPriceTransport, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult) {
        CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = checkIsNeedFreeTecSericeFeeByTransport(tytTransport.getUserId(), tytTransport.getStartCity(), isGoodCarPriceTransport);
        if (checkIsNeedFreeTecServiceFeeVO != null && checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec() != null && checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
            if (CollectionUtils.isNotEmpty(tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList())) {
                tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList().addAll(checkIsNeedFreeTecServiceFeeVO.getNeedFreeTecTypeList());
            } else {
                tytTecServiceFeeConfigToComputResult.setNeedFreeTecTypeList(checkIsNeedFreeTecServiceFeeVO.getNeedFreeTecTypeList());
            }
            return true;
        }
        return false;
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(Long transportUserId, String startCity, boolean isGoodCarPriceTransport) {
        try {
            Response<CheckIsNeedFreeTecServiceFeeVO> response = transportTecserviceFeeClient.checkIsNeedFreeTecSericeFeeByTransport(transportUserId, startCity, isGoodCarPriceTransport).execute();
            if (response.isSuccessful()) {
                log.info("判断是否符合货源直接免佣条件,transportUserId:{},startCity:{},isGoodCarPriceTransport:{},返回结果:{}", transportUserId, startCity, isGoodCarPriceTransport, JSON.toJSONString(response.body()));
                return response.body();
            }
        } catch (IOException e) {
            log.info("判断是否符合货源直接免佣条件接口失败,请求参数：", e);
        }
        return new CheckIsNeedFreeTecServiceFeeVO(false, null, null);
    }

    public boolean getMeetCommissionRules(TytTransport tytTransport, MeetCommissionRulesResult meetCommissionRules, boolean isGoodCarPriceTransport, boolean isCommissionTransport) {
        DrawCommissionReq drawCommissionReq = new DrawCommissionReq();
        drawCommissionReq.setSourceType(tytTransport.getSourceType());
        drawCommissionReq.setExcellentGoods(tytTransport.getExcellentGoods());
        drawCommissionReq.setGoodCarPriceTransport(isGoodCarPriceTransport ? 1 : 0);
        drawCommissionReq.setPublishType(Integer.parseInt(String.valueOf(tytTransport.getPublishType())));
        drawCommissionReq.setHavePrice(StringUtils.isNotBlank(tytTransport.getPrice()) && !tytTransport.getPrice().equals("0") ? 1 : 0);
        drawCommissionReq.setRefundFlag(tytTransport.getRefundFlag());
        drawCommissionReq.setCommissionTime(new Date());
        drawCommissionReq.setCommissionSource(0);
        drawCommissionReq.setUserId(tytTransport.getUserId());
        drawCommissionReq.setInvoiceTransport(tytTransport.getInvoiceTransport());

        BigDecimal score = null;
        if (drawCommissionReq.getHavePrice() == 0) {
            GoodModelResult goodModelResult = checkInstantGrab(tytTransport);
            if (goodModelResult != null && goodModelResult.getScore() != null) {
                log.info("无价货源使用好货模型分数判断是否抽佣：{}", goodModelResult.getScore());
                score = goodModelResult.getScore();
            }
        } else {
            BigDecimal commissionScore = checkCommissionScore(tytTransport);
            log.info("有价货源使用好货运价模型分数判断是否抽佣：{}", commissionScore);
            score = commissionScore;
        }
        if (score == null) {
            return isCommissionTransport;
        }
        drawCommissionReq.setGoodsModelScore(score);

        try {
            TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.check_commission, EntityUtils.entityToMap(drawCommissionReq), null);
            log.info("checkCommission:{}", JSONObject.toJSONString(tytResultMsgBean));
            if (tytResultMsgBean.isSuccess() && tytResultMsgBean.getData() != null) {
                CommissionTypeBean commissionTypeBean = JSON.parseObject(tytResultMsgBean.getData().toString(), CommissionTypeBean.class);
                if (commissionTypeBean != null ) {
                    meetCommissionRules.setMeetCommissionRules(commissionTypeBean.getMeetCommissionRules());
                    if (commissionTypeBean.getDrawCommission() == 1) {
                        isCommissionTransport = true;
                    }
                }
            }
        } catch (Exception e) {
            log.info("checkCommission error，获取货源是否抽佣接口异常:", e);
        }
        return isCommissionTransport;
    }

    @Override
    public void deleteFreeTecServiceFeeLogBySrcMsgId(Long srcMsgId) {
        tytTransportTecServiceFeeMapper.deleteFreeTecServiceFeeLogBySrcMsgId(srcMsgId);
    }

    @Override
    public void addFreeTecServiceFeeLog(TytFreeTecServiceFeeLog tytFreeTecServiceFeeLog) {
        tytTransportTecServiceFeeMapper.addFreeTecServiceFeeLog(tytFreeTecServiceFeeLog);
    }


    @SneakyThrows
    @Override
    public GoodModelResult checkInstantGrab(TytTransport transport) {

        String runType = "1";
        if ("online".equals(profile)) {
            runType = "0";
        } else if ("release".equals(profile)) {
            runType = "3";
        } else if ("test".equals(profile)) {
            runType = "2";
        }

        Map<String, Object> params = new HashMap<>();
        params.put("api_sign", RemoteApiConstant.api_sign);
        params.put("api_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        params.put("run_type", runType);
        params.put("user_id", transport.getUserId());
        params.put("start_city", transport.getStartCity());
        params.put("dest_city", transport.getDestCity());
        if(transport.getPrice() != null){
            params.put("price", transport.getPrice());
        }
        if (transport.getDistance() != null) {
            String distance = CityUtil.toCoordStr(transport.getDistance());
            params.put("distance", distance);
        }
        if(transport.getWeight() != null) {
            params.put("weight", transport.getWeight());
        }
        if(transport.getLength() != null){
            params.put("length", transport.getLength());
        }
        if(transport.getWide() != null) {
            params.put("width", transport.getWide());
        }
        if(transport.getHigh() != null) {
            params.put("height", transport.getHigh());
        }
        params.put("info_fee", transport.getInfoFee());
        params.put("refund_flag", transport.getRefundFlag());
        if (Objects.nonNull(transport.getStartLatitude())) {
            params.put("start_latitude", transport.getStartLatitude());
        }
        if (Objects.nonNull(transport.getStartLongitude())) {
            params.put("start_longitude", transport.getStartLongitude());
        }
        if (Objects.nonNull(transport.getSourceType())) {
            params.put("source_type", transport.getSourceType());
        }
        if (StringUtils.isNotBlank(transport.getStartDetailAdd())) {
            params.put("start_detail_add", transport.getStartDetailAdd());
        }
        log.info("调用 bi 好货模型 req: {}", params);
        BiResponse<GoodModelResult> response =
                newBiService.goodsModel(params).execute().body();
        log.info("调用 bi 好货模型 req: {}, 返回数据: {}", params, response);
        return Optional.ofNullable(response).map(BiResponse::getData).orElse(new GoodModelResult());
    }

    @SneakyThrows
    @Override
    public BigDecimal checkCommissionScore(TytTransport transport) {

        String runType = "1";
        if ("online".equals(profile)) {
            runType = "0";
        } else if ("release".equals(profile)) {
            runType = "3";
        } else if ("test".equals(profile)) {
            runType = "2";
        }

        Map<String, Object> params = new HashMap<>();
        params.put("api_sign", RemoteApiConstant.api_sign);
        params.put("api_time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        params.put("run_type", runType);
        params.put("user_id", transport.getUserId());
        params.put("start_city", transport.getStartCity());
        params.put("dest_city", transport.getDestCity());
        if(transport.getPrice() != null){
            params.put("price", transport.getPrice());
        }
        if (transport.getDistance() != null) {
            String distance = CityUtil.toCoordStr(transport.getDistance());
            params.put("distance", distance);
        }
        if(transport.getWeight() != null) {
            params.put("weight", transport.getWeight());
        }
        if(transport.getLength() != null){
            params.put("length", transport.getLength());
        }
        if(transport.getWide() != null) {
            params.put("width", transport.getWide());
        }
        if(transport.getHigh() != null) {
            params.put("height", transport.getHigh());
        }
        params.put("refund_flag", transport.getRefundFlag());
        params.put("info_fee", transport.getInfoFee());
        log.info("获取抽佣分数调用BI接口 请求参数：{}", JSONObject.toJSONString(params));
        BiResponse<GoodModelResult> response =
                newBiService.goodsModelPrice(params).execute().body();
        log.info("调用 bi 抽佣分数 req: {}, 返回数据: {}", params, response);
        GoodModelResult goodModelResult = Optional.ofNullable(response).map(BiResponse::getData).orElse(new GoodModelResult());
        if (goodModelResult.getScore() != null) {
            return goodModelResult.getScore();
        }
        return null;
    }


    @Override
    public boolean checkIsNeedFreeTecServiceFeeByTime(TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult, Long firstPublishTimeMinute) {
        //新超时免佣配置存在,看看是不是旧货源重发
        if (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime() != null && firstPublishTimeMinute != null) {
            if (tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime() <= 0) {
                return true;
            }
            return firstPublishTimeMinute > Long.valueOf(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime());
        }
        return false;
    }

    private Date getMatchConditionLastTime(TytTransportMain oldTransport, Integer freeTecServiceFeeViewCount, Integer freeTecServiceFeeCallCount) {
        if ((freeTecServiceFeeViewCount != null && freeTecServiceFeeViewCount == 0) || (freeTecServiceFeeCallCount != null && freeTecServiceFeeCallCount == 0)) {
            LocalDate ctimeLocalDate = oldTransport.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now())) {
                return oldTransport.getCtime();
            } else {
                return new Date();
            }
        }
        //今日货源重发，并且条件一没有配置0的
        Date matchConditionLastViewTime = null;
        Date matchConditionLastCallTime = null;

        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime tomorrowStart = todayStart.plusDays(1);
        Date todayStartDate = asDate(todayStart);
        Date tomorrowStartDate = asDate(tomorrowStart);

        if (freeTecServiceFeeViewCount != null) {
            //获取符合条件的最近一次查看时间
            matchConditionLastViewTime = tytTecServiceFeeConfigMapper.getMatchConditionLastViewTime(oldTransport.getSrcMsgId(), freeTecServiceFeeViewCount - 1, todayStartDate, tomorrowStartDate);
        }
        if (freeTecServiceFeeCallCount != null) {
            //获取符合条件的最近一次通话时间
            matchConditionLastCallTime = tytTecServiceFeeConfigMapper.getMatchConditionLastCallTime(oldTransport.getSrcMsgId(), freeTecServiceFeeCallCount - 1, todayStartDate, tomorrowStartDate);
        }
        //取这两个时间最早的那个作为满足条件一的时间
        return getEarliestDate(matchConditionLastViewTime, matchConditionLastCallTime);
    }

    private Date getEarliestDate(Date matchConditionLastViewTime, Date matchConditionLastCallTime) {
        return Optional.ofNullable(matchConditionLastViewTime)
                .map(viewTime -> Optional.ofNullable(matchConditionLastCallTime).filter(callTime -> !viewTime.before(callTime)).orElse(viewTime))
                .orElse(matchConditionLastCallTime);
    }

    public Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    @Override
    public PageData<TytTecServiceFeeConfigVO> getTytTecServiceFeeConfigListByConditions(TytTecServiceFeeConfigReq req) {
        CustomPageHelper customPageHelper = CustomPageHelper.startPage(new PageParameter(req.getPageNum(), req.getPageSize()));
        List<TytTecServiceFeeConfig> byConditions = tytTecServiceFeeConfigMapper.getByConditions(req);
        List<TytTecServiceFeeConfigVO> resultNewList = byConditions.stream()
                .map(config -> {
                    TytTecServiceFeeConfigVO vo = new TytTecServiceFeeConfigVO();
                    BeanUtils.copyProperties(config, vo);
                    return vo;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(resultNewList)) {
            for (TytTecServiceFeeConfigVO tytTecServiceFeeConfigVO : resultNewList) {
                tytTecServiceFeeConfigVO.setServiceFeeStageConfigs(getTytTecServiceFeeStageConfigByConfigId(tytTecServiceFeeConfigVO.getId()));
            }
        }
        return customPageHelper.endPage(resultNewList);
    }

    @Override
    public List<TytTecServiceFeeStageConfig> getTytTecServiceFeeStageConfigByConfigId(Long configId) {
        List<TytTecServiceFeeStageConfig> tytTecServiceFeeStageConfigs = tytTecServiceFeeStageConfigMapper.getByConfigId(configId);
        if (CollectionUtils.isNotEmpty(tytTecServiceFeeStageConfigs)) {
            for (TytTecServiceFeeStageConfig tytTecServiceFeeStageConfig : tytTecServiceFeeStageConfigs) {
                getTytTecServiceFeeProportionConfigByStage(tytTecServiceFeeStageConfig);
            }
            return tytTecServiceFeeStageConfigs;
        } else {
            return new ArrayList<>();
        }
    }

    private List<TytTecServiceFeeProportionConfig> getTytTecServiceFeeProportionConfigByStage(TytTecServiceFeeStageConfig tytTecServiceFeeStageConfig) {
        List<TytTecServiceFeeProportionConfig> tytTecServiceFeeProportionConfigList = tytTecServiceFeeProportionConfigMapper.getTytTecServiceFeeProportionConfigByStageId(tytTecServiceFeeStageConfig.getId());
        if (CollectionUtils.isNotEmpty(tytTecServiceFeeProportionConfigList)) {
            for (TytTecServiceFeeProportionConfig serviceFeeProportionConfig : tytTecServiceFeeProportionConfigList) {
                getTytTecServiceFeeDiscountConfigByProportion(serviceFeeProportionConfig);
            }
            tytTecServiceFeeStageConfig.setTytTecServiceFeeProportionConfigList(tytTecServiceFeeProportionConfigList);
            return tytTecServiceFeeProportionConfigList;
        } else {
            tytTecServiceFeeStageConfig.setTytTecServiceFeeProportionConfigList(new ArrayList<>());
            return new ArrayList<>();
        }
    }

    private List<TytTecServiceFeeDiscountConfig> getTytTecServiceFeeDiscountConfigByProportion(TytTecServiceFeeProportionConfig serviceFeeProportionConfig) {
        List<TytTecServiceFeeDiscountConfig> tytTecServiceFeeDiscountConfigList = tytTecServiceFeeDiscountConfigMapper.getTytTecServiceFeeDiscountConfigByProportionId(serviceFeeProportionConfig.getId());
        if (CollectionUtils.isNotEmpty(tytTecServiceFeeDiscountConfigList)) {
            serviceFeeProportionConfig.setTytTecServiceFeeDiscountConfigList(tytTecServiceFeeDiscountConfigList);
            return tytTecServiceFeeDiscountConfigList;
        } else {
            serviceFeeProportionConfig.setTytTecServiceFeeDiscountConfigList(new ArrayList<>());
            return new ArrayList<>();
        }
    }

    @Override
    public TytTecServiceFeeConfigVO getTytTecServiceFeeConfigInfoByConfigId(Long configId) {
        TytTecServiceFeeConfigVO tytTecServiceFeeConfigVO = null;
        TytTecServiceFeeConfig tytTecServiceFeeConfig = tytTecServiceFeeConfigMapper.getById(configId);
        if (tytTecServiceFeeConfig != null && tytTecServiceFeeConfig.getId() != null) {
            tytTecServiceFeeConfigVO = new TytTecServiceFeeConfigVO();
            BeanUtils.copyProperties(tytTecServiceFeeConfig, tytTecServiceFeeConfigVO);
            List<TytTecServiceFeeStageConfig> stageConfigs = getTytTecServiceFeeStageConfigByConfigId(configId);
            if (CollectionUtils.isNotEmpty(stageConfigs)) {
                tytTecServiceFeeConfigVO.setServiceFeeStageConfigs(stageConfigs);
            } else {
                tytTecServiceFeeConfigVO.setServiceFeeStageConfigs(new ArrayList<>());
            }
        }
        return tytTecServiceFeeConfigVO;
    }

    @Override
    @Transactional
    public void addNewTecServiceFeeConfig(TytTecServiceFeeConfigVO req) {
        TytTecServiceFeeConfig tytTecServiceFeeConfig = new TytTecServiceFeeConfig();
        BeanUtils.copyProperties(req, tytTecServiceFeeConfig);
        TytInternalEmployee sessionEmployee = getSessionEmployee();
        tytTecServiceFeeConfig.setCreateTime(new Date());
        tytTecServiceFeeConfig.setCreateUserId(sessionEmployee.getId());
        tytTecServiceFeeConfig.setCreateUserName(sessionEmployee.getRealName());
        tytTecServiceFeeConfig.setModifyTime(new Date());
        tytTecServiceFeeConfig.setModifyUserId(sessionEmployee.getId());
        tytTecServiceFeeConfig.setModifyUserName(sessionEmployee.getRealName());
        tytTecServiceFeeConfig.setCarMemberType(0);
        tytTecServiceFeeConfig.setFreeTecServiceFeeType(1);


        tytTecServiceFeeConfigMapper.insert(tytTecServiceFeeConfig);
        madeConfigThreeLevel(req, tytTecServiceFeeConfig);
    }

    public void deleteConfigThreeLevel(Long configId) {
        tytTecServiceFeeStageConfigMapper.delete(configId);
        tytTecServiceFeeProportionConfigMapper.deleteByConfigId(configId);
        tytTecServiceFeeDiscountConfigMapper.deleteByConfigId(configId);
    }

    public void madeConfigThreeLevel(TytTecServiceFeeConfigVO req, TytTecServiceFeeConfig tytTecServiceFeeConfig) {
        if (CollectionUtils.isNotEmpty(req.getServiceFeeStageConfigs())) {
            for (TytTecServiceFeeStageConfig serviceFeeStageConfig : req.getServiceFeeStageConfigs()) {
                serviceFeeStageConfig.setConfigId(tytTecServiceFeeConfig.getId());
                serviceFeeStageConfig.setCreateTime(new Date());
                serviceFeeStageConfig.setModifyTime(new Date());
                if (serviceFeeStageConfig.getType() == null) {
                    serviceFeeStageConfig.setType(1);
                }
                tytTecServiceFeeStageConfigMapper.insert(serviceFeeStageConfig);

                if (CollectionUtils.isNotEmpty(serviceFeeStageConfig.getTytTecServiceFeeProportionConfigList())) {
                    for (TytTecServiceFeeProportionConfig serviceFeeProportionConfig : serviceFeeStageConfig.getTytTecServiceFeeProportionConfigList()) {
                        serviceFeeProportionConfig.setConfigId(tytTecServiceFeeConfig.getId());
                        serviceFeeProportionConfig.setStageId(serviceFeeStageConfig.getId());
                        serviceFeeProportionConfig.setCreateTime(new Date());
                        serviceFeeProportionConfig.setModifyTime(new Date());
                        tytTecServiceFeeProportionConfigMapper.insert(serviceFeeProportionConfig);

                        if (CollectionUtils.isNotEmpty(serviceFeeProportionConfig.getTytTecServiceFeeDiscountConfigList())) {
                            for (TytTecServiceFeeDiscountConfig serviceFeeDiscountConfig : serviceFeeProportionConfig.getTytTecServiceFeeDiscountConfigList()) {
                                serviceFeeDiscountConfig.setConfigId(tytTecServiceFeeConfig.getId());
                                serviceFeeDiscountConfig.setStageId(serviceFeeStageConfig.getId());
                                serviceFeeDiscountConfig.setProportionId(serviceFeeProportionConfig.getId());
                                serviceFeeDiscountConfig.setCreateTime(new Date());
                                serviceFeeDiscountConfig.setModifyTime(new Date());
                                tytTecServiceFeeDiscountConfigMapper.insert(serviceFeeDiscountConfig);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public boolean IsExistSameTecServiceFeeConfig(TytTecServiceFeeConfigVO req) {
        int count = tytTecServiceFeeConfigMapper.getSameTecServiceFeeConfig(req);
        return count != 0;
    }

    @Override
    @Transactional
    public void deleteTecServiceFeeConfigByConfigId(Long configId) {
        TytTecServiceFeeConfig tytTecServiceFeeConfig = new TytTecServiceFeeConfig();
        tytTecServiceFeeConfig.setId(configId);
        tytTecServiceFeeConfigMapper.delete(tytTecServiceFeeConfig);
        deleteConfigThreeLevel(tytTecServiceFeeConfig.getId());
    }

    @Override
    @Transactional
    public void updateTecServiceFeeConfig(TytTecServiceFeeConfigVO req) {
        TytInternalEmployee sessionEmployee = getSessionEmployee();
        req.setModifyTime(new Date());
        req.setModifyUserId(sessionEmployee.getId());
        req.setModifyUserName(sessionEmployee.getRealName());
        req.setCarMemberType(0);
        req.setFreeTecServiceFeeType(1);
        tytTecServiceFeeConfigMapper.update(req);
        deleteConfigThreeLevel(req.getId());
        madeConfigThreeLevel(req, req);
    }

    @Override
    public void saveCommissionTransportTecServiceFeeData(Long srcMsgId, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult) {
        if (srcMsgId == null || tytTecServiceFeeConfigToComputResult == null) {
            return;
        }

        if (tytTecServiceFeeConfigToComputResult.getTransportProportionNum() != null) {
            stringRedisTemplate.opsForValue().set(COMMISSION_PROPORTION_TRANSPORT_NUM + ":" + srcMsgId, String.valueOf(tytTecServiceFeeConfigToComputResult.getTransportProportionNum()), 1, TimeUnit.DAYS);
        }

        Integer defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee", 50);

        TytTransportTecServiceFee tytTransportTecServiceFee = new TytTransportTecServiceFee();
        tytTransportTecServiceFee.setSrcMsgId(srcMsgId);
        tytTransportTecServiceFee.setCreateTime(new Date());
        tytTransportTecServiceFee.setModifyTime(new Date());

        tytTransportTecServiceFee.setApplyTransportType(tytTecServiceFeeConfigToComputResult.getApplyTransportType());
        tytTransportTecServiceFee.setRefundFlagType(tytTecServiceFeeConfigToComputResult.getRefundFlagType());
        tytTransportTecServiceFee.setPricePublishType(tytTecServiceFeeConfigToComputResult.getPricePublishType());
        tytTransportTecServiceFee.setTecServiceFeeMin(tytTecServiceFeeConfigToComputResult.getTecServiceFeeMin());
        tytTransportTecServiceFee.setTecServiceFeeMax(tytTecServiceFeeConfigToComputResult.getTecServiceFeeMax());
        tytTransportTecServiceFee.setPriceMin(tytTecServiceFeeConfigToComputResult.getPriceMin());
        tytTransportTecServiceFee.setPriceMax(tytTecServiceFeeConfigToComputResult.getPriceMax());
        tytTransportTecServiceFee.setTecServiceFeeRate(tytTecServiceFeeConfigToComputResult.getTecServiceFeeRate());
        tytTransportTecServiceFee.setDiscountTime(tytTecServiceFeeConfigToComputResult.getDiscountTime());
        tytTransportTecServiceFee.setDiscount(tytTecServiceFeeConfigToComputResult.getDiscount());
        tytTransportTecServiceFee.setTransportProportionNum(tytTecServiceFeeConfigToComputResult.getTransportProportionNum());
        tytTransportTecServiceFee.setDiscountConfig(tytTecServiceFeeConfigToComputResult.getDiscountConfig());
        tytTransportTecServiceFee.setAllDiscount(tytTecServiceFeeConfigToComputResult.getAllDiscount());

        if (tytTecServiceFeeConfigToComputResult.getCommissionScore() != null) {
            tytTransportTecServiceFee.setCommissionScore(tytTecServiceFeeConfigToComputResult.getCommissionScore());
        }
        if (StringUtils.isNotBlank(tytTecServiceFeeConfigToComputResult.getGoodCarPriceTransportCarryPrice())) {
            tytTransportTecServiceFee.setGoodCarPriceTransportCarryPrice(tytTecServiceFeeConfigToComputResult.getGoodCarPriceTransportCarryPrice());
        }

        tytTransportTecServiceFee.setMemberBeforeFee(tytTecServiceFeeConfigToComputResult.getTecServiceFeeAfterDiscount());
        tytTransportTecServiceFee.setMemberAfterFee(tytTecServiceFeeConfigToComputResult.getTecServiceFee());
        tytTransportTecServiceFee.setNoMemberBeforeFee(tytTecServiceFeeConfigToComputResult.getTecServiceFeeAfterDiscount());
        tytTransportTecServiceFee.setNoMemberAfterFee(tytTecServiceFeeConfigToComputResult.getTecServiceFee());
        tytTransportTecServiceFee.setMemberInterestsWord(tytTecServiceFeeConfigToComputResult.getInterestsWord());
        tytTransportTecServiceFee.setMemberInterestsUrl(tytTecServiceFeeConfigToComputResult.getInterestsUrl());
        tytTransportTecServiceFee.setNoMemberInterestsWord(tytTecServiceFeeConfigToComputResult.getInterestsWord());
        tytTransportTecServiceFee.setNoMemberInterestsUrl(tytTecServiceFeeConfigToComputResult.getInterestsUrl());
        tytTransportTecServiceFee.setMemberShowPrivacyPhoneTab(tytTecServiceFeeConfigToComputResult.getPrivacyPhoneType());
        tytTransportTecServiceFee.setNoMemberShowPrivacyPhoneTab(tytTecServiceFeeConfigToComputResult.getPrivacyPhoneType());
        tytTransportTecServiceFee.setMemberFreeTecServiceFeeType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType());
        tytTransportTecServiceFee.setMemberFreeViewCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount());
        tytTransportTecServiceFee.setMemberFreeCallCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount());
        tytTransportTecServiceFee.setMemberFreeReadyType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() != null
                && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() ? 1 : 0);
        tytTransportTecServiceFee.setMemberFreeReadyTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReadyTime());
        tytTransportTecServiceFee.setMemberFreeTecServiceFeeTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime());
        tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeType());
        tytTransportTecServiceFee.setNoMemberFreeViewCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeViewCount());
        tytTransportTecServiceFee.setNoMemberFreeCallCount(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeCallCount());
        tytTransportTecServiceFee.setNoMemberFreeReadyType(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() != null
                && tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReady() ? 1 : 0);
        tytTransportTecServiceFee.setNoMemberFreeReadyTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeIsReadyTime());
        tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeTime(tytTecServiceFeeConfigToComputResult.getFreeTecServiceFeeTime());
        tytTransportTecServiceFee.setMemberUseCommissionStageType(tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() == null || !tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() ? 1 : 2);
        tytTransportTecServiceFee.setNoMemberUseCommissionStageType(tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() == null || !tytTecServiceFeeConfigToComputResult.getUseCommissionScoreStageConfig() ? 1 : 2);

        if (tytTransportTecServiceFee.getMemberBeforeFee() == null) {
            tytTransportTecServiceFee.setMemberBeforeFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getMemberAfterFee() == null) {
            tytTransportTecServiceFee.setMemberAfterFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getNoMemberBeforeFee() == null) {
            tytTransportTecServiceFee.setNoMemberBeforeFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getNoMemberAfterFee() == null) {
            tytTransportTecServiceFee.setNoMemberAfterFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getMemberShowPrivacyPhoneTab() == null) {
            tytTransportTecServiceFee.setMemberShowPrivacyPhoneTab(1);
        }
        if (tytTransportTecServiceFee.getNoMemberShowPrivacyPhoneTab() == null) {
            tytTransportTecServiceFee.setNoMemberShowPrivacyPhoneTab(1);
        }
        if (tytTransportTecServiceFee.getMemberFreeTecServiceFeeType() == null) {
            tytTransportTecServiceFee.setMemberFreeTecServiceFeeType(1);
        }
        if (tytTransportTecServiceFee.getMemberFreeTecServiceFeeTime() == null) {
            tytTransportTecServiceFee.setMemberFreeTecServiceFeeTime(60);
        }
        if (tytTransportTecServiceFee.getNoMemberFreeTecServiceFeeType() == null) {
            tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeType(1);
        }
        if (tytTransportTecServiceFee.getNoMemberFreeTecServiceFeeTime() == null) {
            tytTransportTecServiceFee.setNoMemberFreeTecServiceFeeTime(60);
        }
        if (tytTransportTecServiceFee.getMemberUseCommissionStageType() == null) {
            tytTransportTecServiceFee.setMemberUseCommissionStageType(1);
        }
        if (tytTransportTecServiceFee.getNoMemberUseCommissionStageType() == null) {
            tytTransportTecServiceFee.setNoMemberUseCommissionStageType(1);
        }
        TytTransportTecServiceFee bySrcMsgId = tytTransportTecServiceFeeMapper.getBySrcMsgId(srcMsgId);
        if (bySrcMsgId == null) {
            tytTransportTecServiceFeeMapper.insert(tytTransportTecServiceFee);
        } else {
            tytTransportTecServiceFeeMapper.updateBySrcMsgId(tytTransportTecServiceFee);
        }
        tytTransportTecServiceFeeMapper.saveTransportTecServiceFeeLog(srcMsgId, 1, 2);
    }

    @Override
    public void deleteTecServiceFeeDataBySrcMsgId(Long srcMsgId) {
        tytTransportTecServiceFeeMapper.delete(srcMsgId);
        tytTransportTecServiceFeeMapper.saveTransportTecServiceFeeLog(srcMsgId, 2, 2);
    }

    @Override
    public List<TransportTecServiceFeeLogVO> getTransportTecServiceFeeLog(Long srcMsgId) {
        List<TransportTecServiceFeeLogVO> transportTecServiceFeeLog = tytTransportTecServiceFeeMapper.getTransportTecServiceFeeLog(srcMsgId);
        if (CollectionUtils.isEmpty(transportTecServiceFeeLog)) {
            return new ArrayList<>();
        } else {
            return transportTecServiceFeeLog;
        }
    }

    private String makePriceByGoodCarPriceTransportCarryPrice(TytTransport tytTransport) {
        TytResultMsgBean tytResultMsgBean = platHttpService.doPost(PlatApiConstant.good_car_price_transport_carry_price, new TreeMap<>(), JSONObject.toJSONString(tytTransport), null);
        log.info("makePriceByGoodCarPriceTransportCarryPrice:{}", JSONObject.toJSONString(tytResultMsgBean));
        if (tytResultMsgBean.isSuccess() && tytResultMsgBean.getData() != null) {
            return JSON.parseObject(tytResultMsgBean.getData().toString(), String.class);
        }
        return null;
    }

    private CarryPriceReq createCarryPriceReq(SuggestPriceReq priceReq) {
        CarryPriceReq carryPriceReq = new CarryPriceReq();
        carryPriceReq.setStartProvince(priceReq.getStartProvince());
        carryPriceReq.setStartCity(priceReq.getStartCity());
        carryPriceReq.setStartArea(priceReq.getStartArea());
        carryPriceReq.setDestProvince(priceReq.getDestProvince());
        carryPriceReq.setDestCity(priceReq.getDestCity());
        carryPriceReq.setDestArea(priceReq.getDestArea());
        carryPriceReq.setGoodsName(priceReq.getGoodsName());
        carryPriceReq.setGoodsWeight(priceReq.getGoodsWeight());
        carryPriceReq.setGoodsLength(priceReq.getGoodsLength());
        carryPriceReq.setGoodsWide(priceReq.getGoodsWide());
        carryPriceReq.setGoodsHigh(priceReq.getGoodsHigh());
        carryPriceReq.setDistance(priceReq.getDistance());
        carryPriceReq.setGoodCarPriceTransport(priceReq.getGoodCarPriceTransport());
        /**
         *   优车新增逻辑
         */
        if (priceReq.getExcellentGoods() != null && priceReq.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
            carryPriceReq.setSource(PriceSourceEnum.excellent.getSource());
        } else {
            carryPriceReq.setSource(PriceSourceEnum.reference.getSource());
        }
        String distance = priceReq.getDistance();
        if ("0".equals(distance)) {
            distance = null;
        }
        carryPriceReq.setDistance(distance);
        carryPriceReq.setUserId(priceReq.getUserId());

        return carryPriceReq;
    }

}



