package com.tyt.cargo.dispatch.web.bean.price;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 专车运费导入
 *
 * <AUTHOR>
 * @since 2025-03-12 10:46
 */
@Data
public class ImportPriceConfigBean {

    @ExcelProperty("运费所属企业")
    private String cooperativeName;

    @ExcelProperty("出发地")
    private String startCity;

    @ExcelProperty("目的地")
    private String destCity;

    @ExcelProperty("初始吨位")
    private Integer startTonnage;

    @ExcelProperty("结束吨位")
    private Integer endTonnage;
    /**
     * 0-起步里程
     */
    @ExcelProperty("起步里程")
    private Integer startDistance;

    @ExcelProperty("起步价")
    private Integer startPrice;
    /**
     * 起步里程-200公里的阶梯价格
     */
    @ExcelProperty("超里程价")
    private BigDecimal stepPrice;
    /**
     * 0-200公里的单公里价
     */
    @ExcelProperty("单公里价")
    private BigDecimal singlePrice;
    /**
     * 整车运价下限
     */
    @ExcelProperty("整车运价下限")
    private BigDecimal priceLowerLimit;
}
