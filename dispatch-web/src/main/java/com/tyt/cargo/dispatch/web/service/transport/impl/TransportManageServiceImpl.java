package com.tyt.cargo.dispatch.web.service.transport.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.tyt.cargo.core.enums.ResponseEnum;
import com.tyt.cargo.core.exception.TytException;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.core.util.CommonUtil;
import com.tyt.cargo.core.util.EntityUtils;
import com.tyt.cargo.core.util.StringBaseUtils;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.dispatch.client.constant.PlatApiConstant;
import com.tyt.cargo.dispatch.client.constant.RemoteApiConstant;
import com.tyt.cargo.dispatch.client.enums.*;
import com.tyt.cargo.dispatch.client.enums.EmployeeRoleEnum;
import com.tyt.cargo.dispatch.client.enums.SourceTypeEnum;
import com.tyt.cargo.dispatch.client.form.UserBaseReq;
import com.tyt.cargo.dispatch.client.form.transport.*;
import com.tyt.cargo.dispatch.client.vo.base.TransportLabelJson;
import com.tyt.cargo.dispatch.client.vo.orders.locus.CurrentLocationVO;
import com.tyt.cargo.dispatch.client.vo.remote.BiRecommendedCarsVo;
import com.tyt.cargo.dispatch.client.vo.remote.BiRecommendedReq;
import com.tyt.cargo.dispatch.client.vo.remote.CarryPriceVo;
import com.tyt.cargo.dispatch.client.vo.transport.*;
import com.tyt.cargo.dispatch.client.vo.user.DispatchCompanyVo;
import com.tyt.cargo.dispatch.client.vo.user.EmployeeRole;
import com.tyt.cargo.dispatch.client.vo.user.UserBaseVO;
import com.tyt.cargo.dispatch.web.enums.EditRemarkEnum;
import com.tyt.cargo.dispatch.web.enums.ExcellentEnum;
import com.tyt.cargo.dispatch.web.enums.TransportQueryTypeEnum;
import com.tyt.cargo.dispatch.web.enums.UseCarTypeEnum;
import com.tyt.cargo.dispatch.web.enums.*;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.*;
import com.tyt.cargo.dispatch.web.mybatis.mapper.base.*;
import com.tyt.cargo.dispatch.web.service.base.impl.DispatchBaseServiceImpl;
import com.tyt.cargo.dispatch.web.service.bi.BiResponse;
import com.tyt.cargo.dispatch.web.service.bi.BiService;
import com.tyt.cargo.dispatch.web.service.mq.MessageCenterPushService;
import com.tyt.cargo.dispatch.web.service.order.locus.ICarLocusService;
import com.tyt.cargo.dispatch.web.service.plat.PlatHttpService;
import com.tyt.cargo.dispatch.web.service.transport.*;
import com.tyt.cargo.dispatch.web.service.user.UserSubService;
import com.tyt.cargo.dispatch.web.util.TimeUtil;
import com.tyt.cargo.redis.service.tyt.TytConfigService;
import com.tyt.cargo.web.model.plat.TytResultMsgBean;
import com.tyt.messagecenter.core.enums.BusinessTagEnum;
import com.tyt.messagecenter.core.enums.NativePageEnum;
import com.tyt.messagecenter.core.utils.CityUtil;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.tyt.messagecenter.core.vo.ts.GoodsPushDataVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2022/11/11
 */
@Service
@Slf4j
public class TransportManageServiceImpl extends DispatchBaseServiceImpl implements TransportManageService {

    @Value("${spring.profiles.active}")
    private String profile;

    @Autowired
    private TytTransportDispatchMapper tytTransportDispatchMapper;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;
    @Autowired
    private TytTransportValuableMapper tytTransportValuableMapper;
    @Autowired
    private TytValuableUserMapper tytValuableUserMapper;
    @Autowired
    private TytValuableFollowUpMapper tytValuableFollowUpMapper;
    @Autowired
    private TytSpecialCarDispatchDetailMapper tytSpecialCarDispatchDetailMapper;
    @Autowired
    private TytSpecialCarContactRecordMapper tytSpecialCarContactRecordMapper;
    @Autowired
    private TytDispatchCompanyMapper tytDispatchCompanyMapper;
    @Autowired
    private TytSigningCarInfoMapper tytSigningCarInfoMapper;
    @Autowired
    private TytTransportDispatchViewMapper tytTransportDispatchViewMapper;
    @Autowired
    private TytTransportDispatchViewDetailMapper tytTransportDispatchViewDetailMapper;
    @Autowired
    private TytTransportQuotedPriceMapper tytTransportQuotedPriceMapper;
    @Autowired
    private CsBusinessUserBindService csBusinessUserBindService;
    @Autowired
    private PlatHttpService platHttpService;
    @Autowired
    private MessageCenterPushService messageCenterPushService;
    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private TytTransportDispatchPushMapper tytTransportDispatchPushMapper;
    @Autowired
    private DwsCarUserRouteMapper dwsCarUserRouteMapper;
    @Autowired
    private TytUserMapper tytUserMapper;
    @Autowired
    private TytCarDetailTailMapper tytCarDetailTailMapper;

    @Autowired
    private TytTransportBackendMapper tytTransportBackendMapper;

    @Autowired
    private HistoryOrdersRouteDataMapper historyOrdersRouteDataMapper;

    @Resource(name = "systemFixedExecutor")
    private ExecutorService systemFixedExecutor;
    @Resource
    private GiveGoodsUserService giveGoodsUserService;
    @Resource
    private TytTransportMapper tytTransportMapper;
    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;
    @Resource
    private UserSubService userSubService;

    @Autowired
    private TytTransportMbMergeMapper tytTransportMbMergeMapper;
    @Resource
    private BiService biService;

    @Resource
    ICarLocusService carLocusService;

    @Autowired
    private SpecialCarOpLogService specialCarOpLogService;

    @Resource
    private TytTransportCapacityMapper tytTransportCapacityMapper;

    @Autowired
    private CsBusinessUserBindMapper csBusinessUserBindMapper;

    @Autowired
    private TytTransportYMMMapper tytTransportYMMMapper;

    @Autowired
    private TytConfigMapper tytConfigMapper;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;

    private static final String SPECIAL_CAR_LAST_LOCATION_SWITCH = "special_car_last_location_switch";
    private static final String VALUABLE_TRANSPORT_DISPATCH_CONFIG = "valuable_transport_dispatch_config";


    @Override
    public PageData<TransportManageVO> getMyPublish(TransportManageReq transportManageReq) {
        PageData<TransportManageVO> transportManagePage = null;
        //获取当前登录用户
        TytInternalEmployee employee = getSessionEmployee();
        CsBusinessUserBind businessUserBind = csBusinessUserBindService.getCsBusinessUserByPhoneNo(employee.getLoginPhoneNo());
        transportManageReq.setTodayZero(DateUtil.beginOfDay(new Date()).toString());
        transportManageReq.setTomorrowZero(DateUtil.beginOfDay(DateUtil.tomorrow()).toString());
        // 为空时查询所有
        if (transportManageReq.getQueryType() == null) {
            transportManagePage = getAllStatusList(transportManageReq, businessUserBind, employee);
        } else {
            TransportQueryTypeEnum transportQueryTypeEnum = Optional.ofNullable(TransportQueryTypeEnum.getByValue(transportManageReq.getQueryType()))
                    .orElseThrow(() -> TytException.createException(ResponseEnum.request_error.info()));
            switch (transportQueryTypeEnum) {
                case PUBLISHING:
                    //发布中
                    transportManagePage = getPublishingList(transportManageReq, businessUserBind, employee);
                    break;
                case CANCELED:
                    //撤销
                    transportManagePage = getCanceledList(transportManageReq, businessUserBind, employee);
                    break;
                case EXPIRED:
                    //过期
                    transportManagePage = getExpiredList(transportManageReq, businessUserBind, employee);
                    break;
                case DONE:
                    //成交
                    transportManagePage = getDoneList(transportManageReq, businessUserBind, employee);
                    break;
                default:
                    throwException(ResponseEnum.request_error.info());
            }
        }
        //所有列表增加货源来源字段的展示，货源来源字段的填充单独处理
        makeSourceTypeFieldForPublishingList(transportManagePage, false);
        if (businessUserBind != null) {
            EmployeeRole extraData = EmployeeRole.builder().roleId(businessUserBind.getRoleId()).build();
            transportManagePage.setExtraData(extraData);
        }
        if (!transportManagePage.getList().isEmpty()) {
            transportManagePage.getList().stream().forEach(o -> {
                o.setUploadCellPhone(Base64.getEncoder().encodeToString(o.getUserId().toString().getBytes()));
            });
        }
        //开票主体ID映射为开票主体名称由前端来做
        return transportManagePage;
    }

    /**
     * 查询所有状态下的货源列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @return
     */
    private PageData<TransportManageVO> getAllStatusList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportDispatchMapper.selectAllTransport(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 货源成交列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @return
     */
    private PageData<TransportManageVO> getDoneList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportDispatchMapper.selectDoneTransport(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 获取过期的货物列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @return
     */
    private PageData<TransportManageVO> getExpiredList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportDispatchMapper.selectExpiredTransport(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 获取撤销的货物列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @return
     */
    private PageData<TransportManageVO> getCanceledList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportDispatchMapper.selectCanceledTransport(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 获取发布中的货物列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @return
     */
    private PageData<TransportManageVO> getPublishingList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportDispatchMapper.selectPublishingTransport(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 查询沟通记录列表
     *
     * @param req
     * @return
     */
    @Override
    public List<ContactRecordVo> contactRecordList(TransportManageReq req) {
        List<ContactRecordVo> recordList = new ArrayList<>();
        List<TransportManageVO> list = this.specialCarList(req).getList();
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> srcMsgIds = list.stream().map(TransportManageVO::getSrcMsgId).collect(Collectors.toList());
            List<List<Long>> partition = ListUtil.partition(srcMsgIds, 50);
            for (List<Long> srcMsgIdList : partition) {
                List<ContactRecordVo> subList = tytSpecialCarContactRecordMapper.selectBySrcMsgIds(srcMsgIdList, ContactRecordTypeEnum.SPECIAL.getCode());
                List<TytSpecialCarDispatchFailure> failureList = tytSpecialCarDispatchFailureMapper.selectBySrcMsgIdList(srcMsgIds);
                if (!CollectionUtils.isEmpty(failureList)) {
                    subList.stream().forEach(e ->{
                        TytSpecialCarDispatchFailure failure = failureList.stream().filter(v -> v.getSrcMsgId().equals(e.getSrcMsgId())).findFirst().orElse(null);
                        if (Objects.nonNull(failure)) {
                            e.setAssignType(failure.getAssignType());
                            e.setLimGoodModelScore(failure.getLimGoodModelScore());
                        }
                    });
                }
                recordList.addAll(subList);
                if (recordList.size() > 10000) {
                    break;
                }
            }
        }
        return recordList;
    }

    /**
     * 查询价值货源沟通记录列表
     *
     * @param req
     * @return
     */
    @Override
    public List<ContactRecordVo> valuableContactList(TransportManageReq req) {
        List<ContactRecordVo> recordList = new ArrayList<>();
        List<TransportManageVO> list = this.valuableList(req).getList();
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> srcMsgIds = list.stream().map(TransportManageVO::getSrcMsgId).collect(Collectors.toList());
            List<List<Long>> partition = ListUtil.partition(srcMsgIds, 50);
            for (List<Long> srcMsgIdList : partition) {
                List<ContactRecordVo> subList = tytSpecialCarContactRecordMapper.selectBySrcMsgIds(srcMsgIdList, ContactRecordTypeEnum.VALUABLE.getCode());
                recordList.addAll(subList);
                if (recordList.size() > 10000) {
                    break;
                }
            }
        }
        return recordList;
    }

    /**
     * 添加货源沟通记录
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addContactRecord(ContactRecordReq req) {
        if (Objects.isNull(req.getSrcMsgId())) {
            throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        }

        TytTransportMain transportMain = transportMainService.getTransportMainById(req.getSrcMsgId());
        if (Objects.isNull(transportMain)) {
            throwException(ResponseEnum.request_error.info("货源不存在"));
        }
        // 货源状态处理，已过期状态处理
        int goodsStatus = transportMain.getStatus();
        if (goodsStatus == TransportStatusEnum.active.getCode()) {
            Date todayZero = DateUtil.beginOfDay(new Date()).toJdkDate();
            if (transportMain.getCtime().getTime() < todayZero.getTime()) {
                goodsStatus = TransportStatusEnum.invalid.getCode();
            }
        }

        TytInternalEmployee sessionEmployee = getSessionEmployee();
        req.setUserId(sessionEmployee.getId());
        req.setUserName(sessionEmployee.getName());

        TytSpecialCarDispatchFailure failure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(req.getSrcMsgId());
        Integer workOrderStatus = failure.getWorkOrderStatus();

        int count = tytSpecialCarContactRecordMapper.countBySrcMsgId(req.getSrcMsgId(), ContactRecordTypeEnum.SPECIAL.getCode());
        // 沟通状态
        ContactStatusEnum statusEnum = ContactStatusEnum.FIRST_RESPONSE;
        if (goodsStatus != TransportStatusEnum.active.getCode()) {
            statusEnum = ContactStatusEnum.COMPLETE;
            if (count == 0) {
                // 货源状态为终态，首次添加沟通记录，要修改工单状态为已结单，更新结单时间
                workOrderStatus = WorkOrderStatusEnum.COMPLETED.getCode();
                tytSpecialCarDispatchFailureMapper.updateWorkOrderStatus(failure.getId(), workOrderStatus, new Date(), null);
            }
        } else {
            if (count > 0) {
                statusEnum = ContactStatusEnum.FOLLOW_UP;
            } else {
                // 货源发布中，首次添加沟通记录，修改工单状态为处理中，更新首次响应时间
                workOrderStatus = WorkOrderStatusEnum.HANDLING.getCode();
                tytSpecialCarDispatchFailureMapper.updateWorkOrderStatus(failure.getId(), workOrderStatus, null, new Date());
            }
        }
        req.setContactStatus(statusEnum.getCode());
        req.setWorkOrderStatus(workOrderStatus);
        req.setGoodsStatus(goodsStatus);

        int opCount = specialCarOpLogService.countByOpType(req.getSrcMsgId(), OperationTypeEnum.CHANGE_GOODS_TYPE.getName());
        if (opCount > 0) {
            req.setIsChange(1);
        }
        TytSpecialCarContactRecord record = new TytSpecialCarContactRecord();
        BeanUtils.copyProperties(req, record);
        record.setType(ContactRecordTypeEnum.SPECIAL.getCode());
        tytSpecialCarContactRecordMapper.insertSelective(record);
    }

    /**
     * 添加价值货源沟通记录
     *
     * @param req
     */
    @Override
    public void addValuableContactRecord(ContactRecordReq req) {
        if (Objects.isNull(req.getSrcMsgId())) {
            throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        }

        TytTransportMain transportMain = transportMainService.getTransportMainById(req.getSrcMsgId());
        if (Objects.isNull(transportMain)) {
            throwException(ResponseEnum.request_error.info("货源不存在"));
        }

        TytInternalEmployee sessionEmployee = getSessionEmployee();
        req.setUserId(sessionEmployee.getId());
        req.setUserName(sessionEmployee.getName());

        TytSpecialCarContactRecord record = new TytSpecialCarContactRecord();
        BeanUtils.copyProperties(req, record);
        record.setType(ContactRecordTypeEnum.VALUABLE.getCode());
        tytSpecialCarContactRecordMapper.insertSelective(record);

        // 修改价值货源 是否跟进、跟进人、最新跟进时间
        TytTransportValuable valuable = tytTransportValuableMapper.getValuableBySrcMsgId(req.getSrcMsgId());
        if (Objects.nonNull(valuable)) {
            valuable.setFollowUp(1);
            valuable.setFollowUpUserId(record.getUserId());
            valuable.setFollowUpUserName(record.getUserName());
            valuable.setFollowUpTime(new Date());
            tytTransportValuableMapper.updateByPrimaryKeySelective(valuable);
        }
    }

    /**
     * 价值货源跟进人筛选获取全部跟进人
     *
     * @param req
     * @return
     */
    @Override
    public PageData<UserBaseVO> followUpList(UserBaseReq req) {
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();
        if (Objects.isNull(pageNum)) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize)) {
            pageSize = 10;
        }
        PageHelper.startPage(pageNum, pageSize);
        List<UserBaseVO> list = tytSpecialCarContactRecordMapper.followUpList(req.getUserName());
        return new PageData<>(list);
    }

    /**
     * 价值货源需要跟进的数量
     *
     * @return
     */
    @Override
    public JSONObject followupNum() {
        JSONObject result = new JSONObject();
        int num = 0;

        Long loginUserId = getSessionEmployee().getId();
        TytValuableFollowUp followup = tytValuableFollowUpMapper.selectFollowup(loginUserId);
        if (Objects.nonNull(followup)) {
            Date createTime = TimeUtil.addMinute(-10);
            if (Objects.equals(ValuableFollowupTypeEnum.MARKET.getCode(), followup.getType())) {
                // 当前登录人是市场跟进人
                num = tytTransportValuableMapper.countByMarketFollowupUser(loginUserId, createTime);
            } else {
                // 当前登录人是客服跟进人
                num = tytTransportValuableMapper.countByCsFollowupUser(loginUserId, createTime);
            }
        }

        result.put("followupNum", num);
        return result;
    }

    /**
     * 价值货源详情
     *
     * @param req
     * @return
     */
    @Override
    public ValuableTransportDetailVO valuableDetail(TransportManageReq req) {
        if (Objects.isNull(req.getSrcMsgId())) {
            throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        }
        TytTransportMain transportMain = transportMainService.getTransportMainById(req.getSrcMsgId());
        if (Objects.isNull(transportMain)) {
            throwException(ResponseEnum.request_error.info("货源不存在"));
        }

        ValuableTransportDetailVO valuable = tytTransportValuableMapper.getValuableDetailVOBySrcMsgId(req.getSrcMsgId());
        // 查看3次以上人数
        List<TytTransportDispatchViewDetail> viewDetails =
                tytTransportDispatchViewDetailMapper.selectViewListBySrcMsgIdList(Collections.singletonList(req.getSrcMsgId()));
        int viewThreeNum = 0;
        if (!CollectionUtils.isEmpty(viewDetails)) {
            Map<Long, List<TytTransportDispatchViewDetail>> userViewMap =
                    viewDetails.stream().collect(Collectors.groupingBy(TytTransportDispatchViewDetail::getCarUserId));
            for (List<TytTransportDispatchViewDetail> userViewDetails : userViewMap.values()) {
                if (userViewDetails.size() >= 3) {
                    viewThreeNum++;
                }
            }
        }
        valuable.setViewThreeNum(viewThreeNum);
        // 是否加价
        valuable.setAddPrice(0);
        TytTransport firstPriceTransport = tytTransportMapper.getFirstPriceTransport(req.getSrcMsgId());
        if (Objects.nonNull(firstPriceTransport)) {
            if (StringUtils.isNotBlank(firstPriceTransport.getPrice()) && StringUtils.isNotBlank(transportMain.getPrice())) {
                if (new BigDecimal(transportMain.getPrice()).compareTo(new BigDecimal(firstPriceTransport.getPrice())) > 0) {
                    valuable.setAddPrice(1);
                    valuable.setAddPriceTime(firstPriceTransport.getCtime());
                }
            }
        }
        return valuable;
    }

    @Override
    public void changeValuableCsUser(List<Long> srcMsgIds, Long csUserId, String csUserName) {
        tytTransportValuableMapper.changeValuableCsUser(srcMsgIds, csUserId, csUserName);
        for (Long srcMsgId : srcMsgIds) {
            tytTransportValuableMapper.insertValuableLog(csUserId, srcMsgId);
        }

    }

    @Override
    public List<CsBusinessUserBindVO> getAllTransportCsUser(String csUserName) {
        List<CsBusinessUserBindVO> csUserList = csBusinessUserBindMapper.getAllTransportCsUser(csUserName);
        List<CsDepartmentVO> csDepartment = csBusinessUserBindMapper.getAllCsDepartment();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(csUserList)) {
            List<Long> valuableCsUserIdList = csUserList.stream().map(CsBusinessUserBindVO::getId).collect(Collectors.toList());
            List<CsBusinessUserBindVO> csUserOrderNum = csBusinessUserBindMapper.getCsUserOrderNum(valuableCsUserIdList, com.tyt.cargo.core.util.DateUtil.startOfDay(new Date()), com.tyt.cargo.core.util.DateUtil.endOfDay(new Date()));
            Map<Long, Integer> csUserOrderMap = new HashMap<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(csUserOrderNum)) {
                csUserOrderMap = csUserOrderNum.stream().collect(Collectors.toMap(
                        CsBusinessUserBindVO::getId,
                        CsBusinessUserBindVO::getOrderNum,
                        Integer::min // 如果有相同的 id，保留最小的 orderNum
                ));
            }
            for (CsBusinessUserBindVO csBusinessUserBindValuableVO : csUserList) {
                if (csUserOrderMap.containsKey(csBusinessUserBindValuableVO.getId())) {
                    csBusinessUserBindValuableVO.setOrderNum(csUserOrderMap.get(csBusinessUserBindValuableVO.getId()));
                }
                for (String departmentId : csBusinessUserBindValuableVO.getDepartmentId().split(",")) {
                    for (CsDepartmentVO csDepartmentVO : csDepartment) {
                        if (csDepartmentVO.getDepartmentId().equals(Long.parseLong(departmentId))) {
                            csBusinessUserBindValuableVO.setDepartmentName(csBusinessUserBindValuableVO.getDepartmentName() + csDepartmentVO.getDepartmentName());
                        }
                    }
                }
            }
        }
        return csUserList;
    }

    @Override
    public List<CsBusinessUserBindVO> getValuableTransportCsUser() {
        List<CsBusinessUserBindVO> csUserList =  csBusinessUserBindMapper.getValuableTransportCsUser();
        List<CsDepartmentVO> csDepartment = csBusinessUserBindMapper.getAllCsDepartment();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(csUserList)) {
            List<Long> valuableCsUserIdList = csUserList.stream().map(CsBusinessUserBindVO::getId).collect(Collectors.toList());
            List<CsBusinessUserBindVO> csUserOrderNum = csBusinessUserBindMapper.getCsUserOrderNum(valuableCsUserIdList, com.tyt.cargo.core.util.DateUtil.startOfDay(new Date()), com.tyt.cargo.core.util.DateUtil.endOfDay(new Date()));
            Map<Long, Integer> csUserOrderMap = new HashMap<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(csUserOrderNum)) {
                csUserOrderMap = csUserOrderNum.stream().collect(Collectors.toMap(
                        CsBusinessUserBindVO::getId,
                        CsBusinessUserBindVO::getOrderNum,
                        Integer::min // 如果有相同的 id，保留最小的 orderNum
                ));
            }
            for (CsBusinessUserBindVO csBusinessUserBindValuableVO : csUserList) {
                if (csUserOrderMap.containsKey(csBusinessUserBindValuableVO.getId())) {
                    csBusinessUserBindValuableVO.setOrderNum(csUserOrderMap.get(csBusinessUserBindValuableVO.getId()));
                }
                for (String departmentId : csBusinessUserBindValuableVO.getDepartmentId().split(",")) {
                    for (CsDepartmentVO csDepartmentVO : csDepartment) {
                        if (csDepartmentVO.getDepartmentId().equals(Long.parseLong(departmentId))) {
                            csBusinessUserBindValuableVO.setDepartmentName(csBusinessUserBindValuableVO.getDepartmentName() + csDepartmentVO.getDepartmentName());
                        }
                    }
                }
            }
        }
        return csUserList;
    }

    @Override
    public void updateValuableTransportCsUserStatus(Long csUserId, Integer status) {
        Integer designateStatus = tytTransportValuableMapper.getValuableTransportCsUserDesignateStatusByCsUserId(csUserId);
        if (Objects.isNull(designateStatus)) {
            designateStatus = 0;
        }
        tytTransportValuableMapper.updateValuableTransportCsUserStatus(csUserId, status, designateStatus);
    }

    @Override
    public void updateValuableTransportCsUserDesignateStatus(Long csUserId, Integer designateStatus) {
        Integer status = tytTransportValuableMapper.getValuableTransportCsUserStatusByCsUserId(csUserId);
        if (Objects.isNull(status)) {
            status = 0;
        }
        tytTransportValuableMapper.updateValuableTransportCsUserStatus(csUserId, status, designateStatus);
    }

    @Override
    public ValuableDispatchConfigVO valuableDispatchConfig() {
        try {
            TytConfig config = tytConfigMapper.getByName(VALUABLE_TRANSPORT_DISPATCH_CONFIG);
            if (Objects.nonNull(config) && StringUtils.isNotBlank(config.getValue())) {
                String dispatchConfig = config.getValue();
                JSONObject dispatchConfigJson = JSONObject.parseObject(dispatchConfig);
                if (Objects.nonNull(dispatchConfigJson)) {
                    return JSONObject.toJavaObject(dispatchConfigJson, ValuableDispatchConfigVO.class);
                }
            }
        } catch (Exception e) {
            log.error("valuableDispatchConfig error:", e);
        }
        return new ValuableDispatchConfigVO();
    }

    @Override
    public void updateValuableDispatchConfig(ValuableDispatchConfigVO configVO) {
        if (Objects.isNull(configVO) || StringUtils.isAnyBlank(configVO.getExcellentGoods(), configVO.getPublishType(), configVO.getIdentity())) {
            throwException(ResponseEnum.request_error.info());
        }

        TytConfig config = tytConfigMapper.getByName(VALUABLE_TRANSPORT_DISPATCH_CONFIG);
        if (Objects.isNull(config)) {
            throwException(ResponseEnum.request_error.info("配置不存在"));
        }

        String jsonString = JSONObject.toJSONString(configVO);
        config.setValue(jsonString);
        config.setUpdateTime(new Date());
        tytConfigMapper.updateByPrimaryKeySelective(config);
    }

    /**
     * 专车货源沟通记录列表
     *
     * @param req
     * @return
     */
    @Override
    public PageData<ContactRecordVo> contactList(ContactRecordReq req) {
        // 6650变更，可以按 contactPhone 查询当天沟通记录，srcMsgId和type可为空
        // if (Objects.isNull(req.getSrcMsgId())) {
        //     throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        // }
        // if (Objects.isNull(req.getType())) {
        //     req.setType(ContactRecordTypeEnum.SPECIAL.getCode());
        // }
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();
        if (Objects.isNull(req.getPageNum())) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize)) {
            pageSize = 20;
        }
        PageHelper.startPage(pageNum, pageSize);
        List<ContactRecordVo> list = tytSpecialCarContactRecordMapper.selectContactList(req);
        return new PageData<>(list);
    }

    /**
     * 专车货源操作记录列表
     *
     * @param req
     * @return
     */
    @Override
    public PageData<SpecialCarOpLogVo> opLogList(SpecialCarOpLogReq req) {
        if (Objects.isNull(req.getGoodsId())) {
            throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        }
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();
        if (Objects.isNull(req.getPageNum())) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize)) {
            pageSize = 20;
        }

        List<SpecialCarOpLogVo> list = specialCarOpLogService.selectOpLogList(req.getGoodsId(), pageNum, pageSize);
        return new PageData<>(list);
    }

    /**
     * 查询货源当前状态集合
     *
     * @param req
     * @return
     */
    @Override
    public GoodsStatusVo currentGoodsStatus(SpecialCarOpLogReq req) {
        if (Objects.isNull(req.getGoodsId())) {
            throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        }
        GoodsStatusVo vo = new GoodsStatusVo();
        TytSpecialCarDispatchFailure failure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(req.getGoodsId());
        if (Objects.isNull(failure)) {
            vo.setIsSpecialCar(false);
            return vo;
        }
        vo.setIsSpecialCar(true);
        vo.setWorkOrderStatus(failure.getWorkOrderStatus());
        vo.setHaveContactRecord(0);
        int count = tytSpecialCarContactRecordMapper.countBySrcMsgId(req.getGoodsId(), ContactRecordTypeEnum.SPECIAL.getCode());
        if (count > 0) {
            vo.setHaveContactRecord(1);
        }

        TytTransportMain transportMain = transportMainService.getTransportMainById(req.getGoodsId());
        int goodsStatus = transportMain.getStatus();
        if (goodsStatus == TransportStatusEnum.active.getCode()) {
            Date todayZero = DateUtil.beginOfDay(new Date()).toJdkDate();
            if (transportMain.getCtime().getTime() < todayZero.getTime()) {
                goodsStatus = TransportStatusEnum.invalid.getCode();
            }
        }
        vo.setGoodsStatus(goodsStatus);

        vo.setHaveChangeType(0);
        int opCount = specialCarOpLogService.countByOpType(req.getGoodsId(), OperationTypeEnum.CHANGE_GOODS_TYPE.getName());
        if (opCount > 0) {
            vo.setHaveChangeType(1);
        }
        return vo;
    }

    /**
     * 价值货源列表
     *
     * @param transportManageReq
     * @return
     */
    @Override
    public PageData<TransportManageVO> valuableList(TransportManageReq transportManageReq) {
        PageData<TransportManageVO> transportManagePage = null;
        //获取当前登录用户
        TytInternalEmployee employee = getSessionEmployee();
        CsBusinessUserBind businessUserBind = csBusinessUserBindService.getCsBusinessUserByPhoneNo(employee.getLoginPhoneNo());
        transportManageReq.setTodayZero(DateUtil.beginOfDay(new Date()).toString());
        transportManageReq.setTomorrowZero(DateUtil.beginOfDay(DateUtil.tomorrow()).toString());
        if (!CollectionUtils.isEmpty(transportManageReq.getCsUserIdList())) {
            if (transportManageReq.getCsUserIdList().contains(-1L)) {
                transportManageReq.setIncludeNull(true);
            }
        }
        // 为空时查询所有
        if (transportManageReq.getQueryType() == null) {
            transportManagePage = getValuableAllStatusList(transportManageReq, businessUserBind, employee);
        } else {
            TransportQueryTypeEnum transportQueryTypeEnum = Optional
                    .ofNullable(TransportQueryTypeEnum.getByValue(transportManageReq.getQueryType()))
                    .orElseThrow(() -> TytException.createException(ResponseEnum.request_error.info()));
            switch (transportQueryTypeEnum) {
                case PUBLISHING:
                    //发布中
                    transportManagePage = getValuablePublishingList(transportManageReq, businessUserBind, employee);
                    break;
                case CANCELED:
                    //撤销
                    transportManagePage = getValuableCanceledList(transportManageReq, businessUserBind, employee);
                    break;
                case EXPIRED:
                    //过期
                    transportManagePage = getValuableExpiredList(transportManageReq, businessUserBind, employee);
                    break;
                case DONE:
                    //成交
                    transportManagePage = getValuableDoneList(transportManageReq, businessUserBind, employee);
                    break;
                default:
                    throwException(ResponseEnum.request_error.info());
            }
        }
        //所有列表增加货源来源字段的展示，货源来源字段的填充单独处理
        makeSourceTypeFieldForPublishingList(transportManagePage, true);
        if (businessUserBind != null) {
            EmployeeRole extraData = EmployeeRole.builder().roleId(businessUserBind.getRoleId()).build();
            transportManagePage.setExtraData(extraData);
        }
        if (!transportManagePage.getList().isEmpty()) {
            List<Long> srcMsgIdList = transportManagePage.getList().stream().map(TransportManageVO::getSrcMsgId).collect(Collectors.toList());

            List<TytTransportDispatchViewDetail> viewList = tytTransportDispatchViewDetailMapper.selectViewListBySrcMsgIdList(srcMsgIdList);
            Map<Long, List<TytTransportDispatchViewDetail>> viewNumMap = viewList.stream().collect(Collectors.groupingBy(TytTransportDispatchViewDetail::getSrcMsgId));

            List<Long> userIdList = transportManagePage.getList().stream().map(TransportManageVO::getUserId).collect(Collectors.toList());
            List<Long> idList = tytValuableUserMapper.selectByUserIdList(userIdList);

            transportManagePage.getList().forEach(o -> {
                // 额外字段赋值
                fillExtraValues(viewNumMap, idList, o);
            });

            // 以下字段导出才需要
            if (transportManageReq.isExport()) {
                // 查询跟进人部门信息
                List<String> followUpUserList = transportManagePage.getList().stream().map(TransportManageVO::getFollowUpUser).collect(Collectors.toList());
                Map<String, String> businessUserDepartment = csBusinessUserBindService.getBusinessUserDepartment(followUpUserList);

                // 跟进次数
                List<ContactRecordCountVo> followUpList = tytSpecialCarContactRecordMapper.countBySrcMsgIds(srcMsgIdList, ContactRecordTypeEnum.VALUABLE.getCode());
                Map<Long, Integer> followUpNumsMap = followUpList.stream().collect(Collectors.toMap(ContactRecordCountVo::getSrcMsgId, ContactRecordCountVo::getNums));

                // 派单时间
                List<TytSpecialCarDispatchDetail> dispatchTimeList = tytSpecialCarDispatchDetailMapper.getLatestDispatchTime(srcMsgIdList);
                Map<Long, Date> dispatchTimeMap = dispatchTimeList.stream().filter(Objects::nonNull).collect(Collectors.toMap(TytSpecialCarDispatchDetail::getTsId, TytSpecialCarDispatchDetail::getDispatchTime));

                for (TransportManageVO transportManageVO : transportManagePage.getList()) {
                    transportManageVO.setFollowUpUserDepartment(businessUserDepartment.get(transportManageVO.getFollowUpUser()));
                    transportManageVO.setFollowUpTimes(followUpNumsMap.get(transportManageVO.getSrcMsgId()));
                    transportManageVO.setDispatchTime(dispatchTimeMap.get(transportManageVO.getSrcMsgId()));
                }
            }

        }
        return transportManagePage;
    }

    private void fillExtraValues(Map<Long, List<TytTransportDispatchViewDetail>> viewNumMap, List<Long> idList, TransportManageVO o) {
        o.setUploadCellPhone(
                Base64.getEncoder().encodeToString(
                        (StringUtils.isEmpty(o.getUploadCellPhone())? "" : o.getUploadCellPhone()).getBytes()
                )
        );
        // 货源查看次数，查看3次以上人数
        List<TytTransportDispatchViewDetail> viewDetails = viewNumMap.get(o.getSrcMsgId());
        if (CollectionUtils.isEmpty(viewDetails)) {
            o.setViewNum(0);
            o.setViewThreeNum(0);
        } else {
            o.setViewNum(viewDetails.size());
            int viewThreeNum = 0;
            Map<Long, List<TytTransportDispatchViewDetail>> viewUserMap =
                    viewDetails.stream().collect(Collectors.groupingBy(TytTransportDispatchViewDetail::getCarUserId));
            for (List<TytTransportDispatchViewDetail> userViewDetails : viewUserMap.values()) {
                if (userViewDetails.size() >= 3) {
                    viewThreeNum++;
                }
            }
            o.setViewThreeNum(viewThreeNum);
        }
        // 是否加价，加价时间
        o.setAddPrice(0);
        TytTransport firstPriceTransport = tytTransportMapper.getFirstPriceTransport(o.getSrcMsgId());
        if (Objects.nonNull(firstPriceTransport)) {

            if (StringUtils.isNotBlank(firstPriceTransport.getPrice()) && StringUtils.isNotBlank(o.getPrice())) {
                if (new BigDecimal(o.getPrice()).compareTo(new BigDecimal(firstPriceTransport.getPrice())) > 0) {
                    o.setAddPrice(1);
                    TytTransport priceTransport = tytTransportMapper.getFirstByPrice(o.getSrcMsgId(), o.getPrice());
                    o.setAddPriceTime(priceTransport.getCtime());
                }
            }
        }
        // 是否添加沟通记录
        o.setHaveContactRecord(Objects.equals(o.getFollowUp(), 1));
        // 是否价值用户
        o.setValuableUser(0);
        if (!CollectionUtils.isEmpty(idList) && idList.contains(o.getUserId())) {
            o.setValuableUser(1);
        }
    }

    /**
     * 全部价值货源列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getValuableAllStatusList(TransportManageReq transportManageReq,
                                                                 CsBusinessUserBind businessUserBind,
                                                                 TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportValuableMapper
                .getValuableAllStatusList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 发布中价值货源列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getValuablePublishingList(TransportManageReq transportManageReq,
                                                                  CsBusinessUserBind businessUserBind,
                                                                  TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportValuableMapper
                .getValuablePublishingList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 已取消价值货源列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getValuableCanceledList(TransportManageReq transportManageReq,
                                                                CsBusinessUserBind businessUserBind,
                                                                TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportValuableMapper
                .getValuableCanceledList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 已过期价值货源列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getValuableExpiredList(TransportManageReq transportManageReq,
                                                               CsBusinessUserBind businessUserBind,
                                                               TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportValuableMapper
                .getValuableExpiredList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 已成交价值货源列表
     *
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getValuableDoneList(TransportManageReq transportManageReq,
                                                            CsBusinessUserBind businessUserBind,
                                                            TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytTransportValuableMapper
                .getValuableDoneList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 价值货源导出列表
     *
     * @param req
     * @return
     */
    @Override
    public List<ValuableTransportExportData> getValuableExportList(TransportManageReq req) {
        List<ValuableTransportExportData> list = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 100;
        req.setExport(true);
        while(true) {
            req.setPageNum(pageNum);
            req.setPageSize(pageSize);
            List<TransportManageVO> transportManageVOList = this.valuableList(req).getList();
            if (CollectionUtils.isEmpty(transportManageVOList)) {
                break;
            }
            transportManageVOList.forEach(t -> {
                ValuableTransportExportData transportExportData = new ValuableTransportExportData();
                BeanUtil.copyProperties(t, transportExportData);
                if (t.getExcellentGoods() != null && t.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName() + "（优货）");
                } else if (t.getExcellentGoods() != null && ExcellentEnum.SPECIAL.getCode().equals(t.getExcellentGoods())) {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName() + "（专车）");
                } else {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName());
                }
                transportExportData.setStatusName(TransportStatusEnum.getEnum(t.getStatus()) == null ? ""
                        : TransportStatusEnum.getEnum(t.getStatus()).getZhName());
                if (Objects.nonNull(t.getDistance())) {
                    transportExportData.setDistance(t.getDistance() / 100);
                }
                list.add(transportExportData);
            });

            if (transportManageVOList.size() < pageSize) {
                break;
            }
            if (list.size() >= 5000) {
                break;
            }
            pageNum = pageNum + 1;
        }
        return list;
    }

    /**
     * 专车货源列表
     * @param transportManageReq
     * @return
     */
    @Override
    public PageData<TransportManageVO> specialCarList(TransportManageReq transportManageReq) {
        PageData<TransportManageVO> transportManagePage = null;
        //获取当前登录用户
        TytInternalEmployee employee = getSessionEmployee();
        CsBusinessUserBind businessUserBind = csBusinessUserBindService.getCsBusinessUserByPhoneNo(employee.getLoginPhoneNo());
        transportManageReq.setTodayZero(DateUtil.beginOfDay(new Date()).toString());
        transportManageReq.setTomorrowZero(DateUtil.beginOfDay(DateUtil.tomorrow()).toString());
        // 为空时查询所有
        if (transportManageReq.getQueryType() == null) {
            transportManagePage = getSpecialCarAllStatusList(transportManageReq, businessUserBind, employee);
        } else {
            TransportQueryTypeEnum transportQueryTypeEnum = Optional.ofNullable(TransportQueryTypeEnum.getByValue(transportManageReq.getQueryType()))
                    .orElseThrow(() -> TytException.createException(ResponseEnum.request_error.info()));
            switch (transportQueryTypeEnum) {
                case PUBLISHING:
                    //发布中
                    transportManagePage = getSpecialCarPublishingList(transportManageReq, businessUserBind, employee);
                    break;
                case CANCELED:
                    //撤销
                    transportManagePage = getSpecialCarCanceledList(transportManageReq, businessUserBind, employee);
                    break;
                case EXPIRED:
                    //过期
                    transportManagePage = getSpecialCarExpiredList(transportManageReq, businessUserBind, employee);
                    break;
                case DONE:
                    //成交
                    transportManagePage = getSpecialCarDoneList(transportManageReq, businessUserBind, employee);
                    break;
                default:
                    throwException(ResponseEnum.request_error.info());
            }
        }
        //所有列表增加货源来源字段的展示，货源来源字段的填充单独处理
        makeSourceTypeFieldForPublishingList(transportManagePage, true);
        if (businessUserBind != null) {
            EmployeeRole extraData = EmployeeRole.builder().roleId(businessUserBind.getRoleId()).build();
            transportManagePage.setExtraData(extraData);
        }
        if (!transportManagePage.getList().isEmpty()) {
            List<Long> srcMsgIdList = transportManagePage.getList().stream().map(TransportManageVO::getSrcMsgId).collect(Collectors.toList());
            List<TytTransportMainExtend> extendList = tytTransportMainExtendMapper.selectBySrcMsgIdList(srcMsgIdList);

            transportManagePage.getList().stream().forEach(o -> {
                o.setUseCarType(UseCarTypeEnum.FULL.getName());
                TytTransportMainExtend mainExtend = extendList.stream()
                        .filter(e -> e.getSrcMsgId().equals(o.getSrcMsgId()))
                        .findFirst()
                        .orElse(new TytTransportMainExtend());
                if (Objects.equals(mainExtend.getUseCarType(), UseCarTypeEnum.PART.getCode())) {
                    o.setUseCarType(UseCarTypeEnum.PART.getName());
                }
                o.setUploadCellPhone(
                    Base64.getEncoder().encodeToString(
                        (StringUtils.isEmpty(o.getUploadCellPhone())? "" : o.getUploadCellPhone()).getBytes()
                    )
                );
                o.setResponseTimeStr(TimeUtil.calculateHoursDifference(o.getCreateTime(), o.getResponseTime()));
                o.setEndTimeStr(TimeUtil.calculateHoursDifference(o.getCreateTime(), o.getEndTime()));
            });
        }
        return transportManagePage;
    }

    /**
     * 专车货源列表全部
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getSpecialCarAllStatusList(TransportManageReq transportManageReq,
                                                                   CsBusinessUserBind businessUserBind,
                                                                   TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytSpecialCarDispatchFailureMapper.getSpecialCarAllStatusList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 专车货源列表发布中
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getSpecialCarPublishingList(TransportManageReq transportManageReq,
                                                                    CsBusinessUserBind businessUserBind,
                                                                    TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytSpecialCarDispatchFailureMapper.getSpecialCarPublishingList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 专车货源列表已撤销
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getSpecialCarCanceledList(TransportManageReq transportManageReq,
                                                                  CsBusinessUserBind businessUserBind,
                                                                  TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytSpecialCarDispatchFailureMapper.getSpecialCarCanceledList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 专车货源列表已过期
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getSpecialCarExpiredList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytSpecialCarDispatchFailureMapper.getSpecialCarExpiredList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 专车货源列表已完成
     * @param transportManageReq
     * @param businessUserBind
     * @param employee
     * @return
     */
    private PageData<TransportManageVO> getSpecialCarDoneList(TransportManageReq transportManageReq, CsBusinessUserBind businessUserBind, TytInternalEmployee employee) {
        PageHelper.startPage(transportManageReq.getPageNum(), transportManageReq.getPageSize());
        List<TransportManageVO> transportManageVOList = tytSpecialCarDispatchFailureMapper.getSpecialCarDoneList(transportManageReq, businessUserBind, employee);
        return new PageData<>(transportManageVOList);
    }

    /**
     * 获取导出专车货源列表
     *
     * @param req
     * @return
     */
    @Override
    public List<TransportExportData> getSpecialCarExportList(TransportManageReq req) {
        List<TransportExportData> list = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 100;
        while (true) {
            req.setPageNum(pageNum);
            req.setPageSize(pageSize);
            List<TransportManageVO> transportManageVOList = this.specialCarList(req).getList();
            if (CollectionUtils.isEmpty(transportManageVOList)) {
                break;
            }

            transportManageVOList.forEach(t -> {
                TransportExportData transportExportData = new TransportExportData();
                BeanUtil.copyProperties(t, transportExportData);
                transportExportData.setAmountAndIsRefund(t.getInfoFee() + (t.getRefundFlag() == null || t.getRefundFlag() == 0 ? "(不退还)" : "(退还)"));
                // 是否有货逻辑
                if (t.getExcellentGoods() != null && t.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName() + "（优货）");
                } else if (t.getExcellentGoods() != null && ExcellentEnum.SPECIAL.getCode().equals(t.getExcellentGoods())) {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName() + "（专车）");
                } else {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName());
                }
                transportExportData.setSourceType(t.getOwnerSourceType());
                transportExportData.setCargoOwnerName(t.getCargoOwnerName());
                transportExportData.setStatusName(TransportStatusEnum.getEnum(t.getStatus()) == null ? ""
                        : TransportStatusEnum.getEnum(t.getStatus()).getZhName());
                transportExportData.setWorkOrderStatusName(WorkOrderStatusEnum.getStatusName(t.getWorkOrderStatus()));
                transportExportData.setResponseTime(TimeUtil.calculateHoursDifference(t.getCreateTime(), t.getResponseTime()));
                transportExportData.setEndTime(TimeUtil.calculateHoursDifference(t.getCreateTime(), t.getEndTime()));
                transportExportData.setDeclareInPublicString(t.getDeclareInPublic() == 1 ? "是" : "否");
                transportExportData.setAssignType(AssignTypeEnum.getNameByCode(t.getAssignType()));
                transportExportData.setLimGoodModelScore(Objects.nonNull(t.getLimGoodModelScore()) ? t.getLimGoodModelScore().toString() : "");
                list.add(transportExportData);
            });

            if (transportManageVOList.size() < pageSize) {
                break;
            }
            if (list.size() > 5000) {
                break;
            }
            pageNum = pageNum + 1;
        }
        return list;
    }

    /**
     * 专车指派记录列表
     * @param transportManageReq
     * @return
     */
    @Override
    public AssignListVo assignList(TransportManageReq transportManageReq) {
        Long srcMsgId = transportManageReq.getSrcMsgId();
        if (Objects.isNull(srcMsgId)) {
            throwException(ResponseEnum.request_error.info("货源ID不能为空"));
        }
        AssignListVo listVo = new AssignListVo();

        List<AssignDetailVo> assignList = tytSpecialCarDispatchDetailMapper.selectAssignList(srcMsgId);
        if (!CollectionUtils.isEmpty(assignList)) {
            // 系统指派记录列表
            List<Long> carInfoIdList = assignList.stream().map(AssignDetailVo::getCarInfoId).collect(Collectors.toList());
            List<AssignDetailVo> infos = tytSigningCarInfoMapper.selectCarInfoList(carInfoIdList);
            if (!CollectionUtils.isEmpty(infos)) {
                for (AssignDetailVo vo : assignList) {
                    AssignDetailVo info = infos.stream().filter(e ->
                            Objects.equals(vo.getCarInfoId(), e.getCarInfoId())).findFirst().orElse(new AssignDetailVo());
                    vo.setName(info.getName());
                    vo.setCellphone(info.getCellphone());
                    vo.setTel(info.getTel());
                    vo.setSigningId(info.getSigningId());
                    if (Objects.nonNull(vo.getDistance())) {
                        vo.setDistance(vo.getDistance().setScale(1, RoundingMode.HALF_UP));
                    }
                }
            }

            // 查询联系人当天是否有沟通记录
            List<String> telList = assignList.stream().map(AssignDetailVo::getTel).collect(Collectors.toList());
            List<String> phoneList = assignList.stream().map(AssignDetailVo::getCellphone).collect(Collectors.toList());
            phoneList.addAll(telList);
            TytTransportMain transport = transportMainService.getTransportMainById(srcMsgId);
            List<String> contactPhoneList = tytSpecialCarContactRecordMapper.existContactRecordOfPhone(phoneList, DateUtil.formatDate(transport.getCtime()));
            for (AssignDetailVo vo : assignList) {
                vo.setIsContact(contactPhoneList.contains(vo.getTel()) || contactPhoneList.contains(vo.getCellphone()) ? 1 : 0);
            }

            // 是否可查看位置
            List<AssignDetailVo> assignDetailVos = assignList.stream()
                    .filter(e -> Objects.nonNull(e.getDispatchTime()))
                    .sorted(Comparator.comparingInt(AssignDetailVo::getPriority))
                    .collect(Collectors.toList());
            List<Long> userIdList = assignDetailVos.stream().map(AssignDetailVo::getUserId).collect(Collectors.toList());
            List<TytTransportDispatchViewDetail> viewDetails = tytTransportDispatchViewDetailMapper.selectViewDetailList(srcMsgId, userIdList);
            for (AssignDetailVo vo : assignDetailVos) {
                TytTransportDispatchViewDetail viewDetail = viewDetails.stream().filter(e ->
                        Objects.equals(vo.getUserId(), e.getCarUserId())).findFirst().orElse(null);
                if (Objects.nonNull(viewDetail)) {
                    vo.setIsView(1);
                } else {
                    vo.setIsView(0);
                }
            }
            listVo.setAssignList(assignDetailVos);

            // 可接单司机列表
            List<AssignDetailVo> driverList = assignList.stream()
                    .filter(e -> Objects.isNull(e.getDispatchTime()))
                    .sorted(Comparator.comparingInt(AssignDetailVo::getPriority))
                    .limit(15)
                    .collect(Collectors.toList());
            listVo.setDriverList(driverList);
        } else {
            listVo.setAssignList(new ArrayList<>());
            listVo.setDriverList(new ArrayList<>());
        }

        TytTransportMain transport = transportMainService.getTransportMainById(srcMsgId);
        listVo.setTransport(BeanUtil.copyProperties(transport, TransportManageVO.class));
        listVo.getTransport().setPublishTime(DateUtil.formatDate(transport.getCtime()));

        return listVo;
    }

    /**
     * 指派调度处理人
     * @param req
     */
    @Override
    public void assignDispatcher(AssignDispatcherReq req) {
        if (Objects.isNull(req.getGoodsId()) || Objects.isNull(req.getDispatcherId()) || StringUtils.isEmpty(req.getDispatcherName())) {
            throwException(ResponseEnum.request_error.info("缺少必要参数"));
        }

        tytSpecialCarDispatchFailureMapper.updateDispatcher(req.getGoodsId(), req.getDispatcherId(), req.getDispatcherName());
    }

    /**
     * 查询调度列表
     * @param req
     * @return
     */
    @Override
    public PageData<DispatchCompanyVo> dispatcherList(AssignDispatcherReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<DispatchCompanyVo> list = tytDispatchCompanyMapper.selectDispatcherList(req.getDispatcherName());
        return new PageData<>(list);
    }

    /**
     * 货源查看/联系列表
     *
     * @param req
     * @return
     */
    @Override
    public PageData<TransportViewListVO> transportViewList(TransportViewReq req) {

        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<TransportViewListVO> transportViewListVOList = tytTransportDispatchViewMapper.selectTransportView(req);
        if (CollectionUtil.isNotEmpty(transportViewListVOList)) {
            TytTransportMain main = transportMainService.getTransportMainById(req.getSrcMsgId());
            CountDownLatch countDownLatch = new CountDownLatch(transportViewListVOList.size());
            // 获取车型、样式、板长
            transportViewListVOList.forEach(t -> {
                systemFixedExecutor.execute(() -> {
                    try {
                        buildTransportView(t, req.getType(), main);
                    } catch (Exception e) {
                        log.error("获取车辆信息出错,carUserId:{}", t.getCarUserId(), e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            });
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("获取车辆信息出错,货源id:{}", req.getSrcMsgId(), e);
            }
        }
        return new PageData<>(transportViewListVOList);
    }

    /**
     * 查询类型、样式、板长
     *
     * @param transportViewListVO
     */
    private void buildTransportView(TransportViewListVO transportViewListVO, Integer type, TytTransportMain main) {
        List<TytCarDetailTail> carDetailTailList = tytCarDetailTailMapper.selectCarDetailTail(Arrays.asList(transportViewListVO.getCarUserId()));
        if (CollectionUtil.isNotEmpty(carDetailTailList)) {
            Set<String> carTypeSet = new HashSet<>();
            Set<String> carStyleSet = new HashSet<>();
            carDetailTailList.forEach(c -> {
                if (StringUtils.isNotBlank(c.getCarType())) {
                    carTypeSet.add(c.getCarType());
                }
                if (c.getIsPureFlat() != null) {
                    carStyleSet.add(String.valueOf(c.getIsPureFlat()));
                }
            });
            transportViewListVO.setCarType(String.join(",", carTypeSet));
            transportViewListVO.setCarStyle(String.join(",", carStyleSet));
        }
        if (Objects.equals(type, 1)) {
            // 司机首次查看时间
            TytTransportDispatchViewDetail viewDetail =
                    tytTransportDispatchViewDetailMapper.selectUserFirstViewDetail(transportViewListVO.getSrcMsgId(), transportViewListVO.getCarUserId());
            if (Objects.nonNull(viewDetail)) {
                transportViewListVO.setFirstViewTime(viewDetail.getCreateTime());
            }
            // 司机出价次数，司机最新一次出价，司机最新出价时间，货主最新一次出价，货主最新出价时间
            List<TytTransportQuotedPrice> priceList =
                    tytTransportQuotedPriceMapper.selectQuotedPriceList(transportViewListVO.getSrcMsgId(), transportViewListVO.getCarUserId());
            if (!CollectionUtils.isEmpty(priceList)) {
                TytTransportQuotedPrice quotedPrice = priceList.get(0);
                // 司机出价次数
                transportViewListVO.setGivePriceCount(quotedPrice.getCarQuotedPriceTimes());
                // 司机最新一次出价
                transportViewListVO.setDriverLatestPrice(quotedPrice.getCarQuotedPrice());
                // 司机最新出价时间
                transportViewListVO.setDriverLatestPriceTime(quotedPrice.getCarQuotedPriceTime());
                // 货主最新一次出价
                transportViewListVO.setOwnerLatestPrice(quotedPrice.getTransportQuotedPrice());
                // 货主最新出价时间
                transportViewListVO.setOwnerLatestPriceTime(quotedPrice.getTransportQuotedPriceTime());
            }
        }
        // 半年内是否承运此路线
        int haveRouteOrderIn6month = 0;
        DwsCarUserRoute route = dwsCarUserRouteMapper.getByUserIdAndRoute(transportViewListVO.getCarUserId(), main.getStartCity(), main.getDestCity());
        if (Objects.nonNull(route) && Objects.nonNull(route.getCnt()) && route.getCnt() > 0) {
            haveRouteOrderIn6month = 1;
        }
        transportViewListVO.setHaveRouteOrderIn6Month(haveRouteOrderIn6month);
    }

    /**
     * 查看、联系、推荐车辆的数量
     *
     * @param srcMsgId
     * @return
     */

    @Override
    public TransportCountVO transportListCount(Long srcMsgId) {
        // 查询查看和联系的总条数
        return tytTransportDispatchViewMapper.selectTotalCount(srcMsgId);
    }

    @Override
    public List<String> transportViewDetailList(TransportViewDetailReq req) {
        return tytTransportDispatchViewDetailMapper.selectTransportViewDetail(req);
    }


    @Override
    public void editRemark(TransportViewDetailReq req) {
        TytTransportDispatchView tytTransportDispatchView = tytTransportDispatchViewMapper.getBySrcMsgIdAndCarUserId(req);
        if (tytTransportDispatchView == null && req.getType().equals(EditRemarkEnum.RECOMMENDED_CARS.getType())) {
            //更新推送车辆备注
            tytTransportDispatchView = new TytTransportDispatchView();
            TytUser user = tytUserMapper.selectByPrimaryKey(req.getCarUserId());
            tytTransportDispatchView.setSrcMsgId(req.getSrcMsgId());
            tytTransportDispatchView.setCarUserId(user.getId());
            tytTransportDispatchView.setCarUserName(formatUserName(user.getTrueName(), String.valueOf(user.getId())));
            tytTransportDispatchView.setCarNickName(formatUserName(user.getCarUserName(), String.valueOf(user.getId())));
            tytTransportDispatchView.setCarPhone(user.getCellPhone());
            tytTransportDispatchView.setViewCount(0);
            tytTransportDispatchView.setContactCount(0);
            tytTransportDispatchView.setRemark(req.getRemark());
            tytTransportDispatchView.setCreateTime(new Date());
            tytTransportDispatchView.setModifyTime(new Date());
            tytTransportDispatchViewMapper.insert(tytTransportDispatchView);
        } else {
            // 更新查看、联系列表备注
            if (tytTransportDispatchView == null) {
                throwException(DispatchResponseEnum.data_not_exist.info());
            }
            tytTransportDispatchView.setRemark(req.getRemark());
            tytTransportDispatchView.setModifyTime(new Date());
            if (req.getType().equals(EditRemarkEnum.VIEW.getType())) {
                tytTransportDispatchView.setViewTime(new Date());
            } else if (req.getType().equals(EditRemarkEnum.CONTACT.getType())) {
                tytTransportDispatchView.setContactTime(new Date());
            }
            tytTransportDispatchViewMapper.updateByPrimaryKey(tytTransportDispatchView);
        }
    }

    public static String formatUserName(String name, String userId) {
        String userName = " ";
        if (StringUtils.isNotBlank(name) && !"null".equals(name)) {
            userName = name;
        } else {
            userName = "用户" + userId;
        }

        return userName;
    }


    @Override
    public void designateCar(SaveWayBillReq saveWayBillReq) {

        TytTransportMain transportMain = transportMainService.getTransportMainById(saveWayBillReq.getGoodsId());
        if (Objects.isNull(transportMain) || transportMain.getStatus() != 1) {
            //货源不存在货已下架
            super.throwException(DispatchResponseEnum.transport_error.info("该货源不存在或已下架"));
        }
        if (Objects.nonNull(transportMain.getCtime()) && !com.tyt.cargo.core.util.DateUtil.isToday(transportMain.getCtime().getTime())) {
            log.warn("指派车辆,该货源已过期,srcMsgId = {}", saveWayBillReq.getGoodsId());
            super.throwException(DispatchResponseEnum.transport_error.info("该货源已过期"));
        }

        Long userId = transportMain.getUserId();
        Long reqUserId = saveWayBillReq.getUserId();

        if (CommonUtil.hasNull(userId, reqUserId)) {
            super.throwException(DispatchResponseEnum.transport_error.info());
        }


        if (userId.equals(reqUserId)) {
            super.throwException(ResponseEnum.request_error.info("指派车辆和货主不能是同一个账号"));
        }


        boolean inBlack = transportMainService.checkCarUserInBlack(reqUserId, transportMain.getExcellentGoods());
        if (inBlack) {
            super.throwException(DispatchResponseEnum.all_car_in_blacklist.info());
        }

        //定金/信息费
        saveWayBillReq.setAgencyMoney(transportMain.getInfoFee().longValue());
        saveWayBillReq.setCarOwnerPayType(99);
        /**
         *  技术服务费 6350
         */
        saveWayBillReq.setTecServiceFee(transportMain.getTecServiceFee() == null ? 0L : transportMain.getTecServiceFee().longValue());
        log.info("指派车辆,最终传给plat的参数为:{}", JSON.toJSONString(saveWayBillReq));
        TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.save_way_bill, EntityUtils.entityToMap(saveWayBillReq), null);

        if (tytResultMsgBean == null) {
            super.throwException(ResponseEnum.request_error.info("指派车辆失败"));
        }

        log.info("指派车辆,plat返回信息为:{}", JSON.toJSONString(tytResultMsgBean));
        if (Objects.nonNull(tytResultMsgBean) && tytResultMsgBean.getCode() == 200) {
            String title = "货方已指派您接单";

            String startAddress = CityUtil.createAddressInfo(transportMain.getStartCity(), transportMain.getStartArea());
            String destAddress = CityUtil.createAddressInfo(transportMain.getDestCity(), transportMain.getDestArea());

            String shortTaskContent = this.getShortTaskContent(transportMain.getTaskContent());

            //通知的
            String contentTmp = "%s-%s的%s货方已指派您接单，点击此信息或进入“订单—待支付”立即支付订金，防止他人抢单！";
            String contentText = String.format(contentTmp, startAddress, destAddress, shortTaskContent);

            MessagePushBase messagePushBase = new MessagePushBase();
            //添加推送用户
            messagePushBase.addUserId(saveWayBillReq.getUserId());
            messagePushBase.setTitle(title);
            messagePushBase.setRemarks(title);
            messagePushBase.setCarPush((short) 1);

            //push消息
            NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
            notifyMessage.setContent(contentText);
            notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
            notifyMessage.addNativeParameter("id", saveWayBillReq.getGoodsId() + "");

            //站内信
            String msgContentTmp = "%s-%s的%s货方已指派您接单，请进入“订单—待支付”立即支付订金，防止他人抢单！";
            String msgContentText = String.format(msgContentTmp, startAddress, destAddress, shortTaskContent);
            NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(messagePushBase);
            newsMessagePush.setContent(msgContentText);
            newsMessagePush.setSummary(msgContentText);

            messageCenterPushService.sendMultiMessage(null, newsMessagePush, notifyMessage);

        } else {
            super.throwException(ResponseEnum.request_error.info(tytResultMsgBean.getMsg()));
        }

        //TODO 价值货源指派保存指派记录


    }


    根据以下提示写建表语句
    司机姓名：司机真实姓名
    司机手机号：司机真实手机号码
    司机特运通账号：司机特运通账号
    是否查看：被指派司机是否查看过待支付单
    接单状态：被指派司机是否接单
    距装货地距离：距离装货地的距离XX公里
    ctime 指派时间
    指派操作人 cs_business_user_id
    货源ID src_msg_id
            两个索引，一个src_msg_id，一个cs_business_user_id、ctime的联合索引


    /**
     * 缩减货物内容
     *
     * @param taskContent
     * @return
     */
    private String getShortTaskContent(String taskContent) {
        String result = taskContent;
        if (StringUtils.isNotBlank(taskContent) && taskContent.length() > 7) {
            result = taskContent.substring(0, 7) + "...";
        }
        return result;
    }

    @Override
    public int pushGoods(PushGoodsReq pushGoodsReq) {
        TytTransportMain transportMain = transportMainService.getTransportMainById(pushGoodsReq.getGoodsId());
        if (Objects.isNull(transportMain) || transportMain.getStatus() != 1) {
            super.throwException(ResponseEnum.request_error.info("该货源不存在或已下架"));
        }
        if (Objects.nonNull(transportMain.getCtime()) && !com.tyt.cargo.core.util.DateUtil.isToday(transportMain.getCtime().getTime())) {
            super.throwException(ResponseEnum.request_error.info("该货源已过期"));
        }
        // 如果有相似货源，要算出相似货源的人
        Set<Long> userIdSet = new HashSet<>();
        if (CollectionUtil.isNotEmpty(pushGoodsReq.getSimilarGoodsIdList())) {
            List<SimilarViewContactVo> viewContactVoList = this.similarViewContactList(pushGoodsReq.getSimilarGoodsIdList());
            if (CollectionUtil.isNotEmpty(viewContactVoList)) {
                userIdSet = viewContactVoList.stream().map(SimilarViewContactVo::getCarUserId).collect(Collectors.toSet());
            }
        }
        if (CollectionUtil.isNotEmpty(pushGoodsReq.getUserIds())) {
            userIdSet.addAll(pushGoodsReq.getUserIds());
        }
        // 如果已经推送过的就不再进行推送
        List<Long> pushedUserIdList = tytTransportDispatchPushMapper.selectPushStatus(null, pushGoodsReq.getGoodsId());
        if (CollectionUtil.isNotEmpty(pushedUserIdList)) {
            pushedUserIdList.forEach(userIdSet::remove);
        }
        if (CollectionUtil.isEmpty(userIdSet)) {
            log.warn("要推送的车方用户为空，srcMsgId:{}", pushGoodsReq.getGoodsId());
            return 0;
        }
        String taskContent = transportMain.getTaskContent();
        if (taskContent.length() > 7) {
            taskContent = taskContent.substring(0, 7);
        }
        //主题
        String title = "特运通货源待您接单";
        //标题
        String summary = transportMain.getStartPoint() + "-" + transportMain.getDestPoint() + "的" + taskContent + "待您查看接单！";

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        List<Long> pushUserIdList = new ArrayList<>(userIdSet);
        messagePushBase.setUserIdList(pushUserIdList);
        messagePushBase.setTitle(title);
        messagePushBase.setContent(summary);
        messagePushBase.setRemarks(summary);
        messagePushBase.setCarPush((short) 1);
        //push消息
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
        notifyMessage.addNativeParameter("id", pushGoodsReq.getGoodsId() + "");
        //站内信
        NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(messagePushBase);
        newsMessagePush.setTitle(title);
        newsMessagePush.setSummary(summary);
        messageCenterPushService.sendMultiMessage(null, newsMessagePush, notifyMessage);
        List<TytTransportDispatchPush> transportDispatchPushes = new ArrayList<>();
        pushUserIdList.forEach(p -> {
            TytTransportDispatchPush tytTransportDispatchPush = new TytTransportDispatchPush();
            tytTransportDispatchPush.setCreateTime(new Date());
            tytTransportDispatchPush.setSrcMsgId(pushGoodsReq.getGoodsId());
            tytTransportDispatchPush.setUserId(p);
            transportDispatchPushes.add(tytTransportDispatchPush);
        });
        int i = tytTransportDispatchPushMapper.insertList(transportDispatchPushes);
        return i;
    }

    /**
     * 周边车辆列表
     *
     * @param req
     * @return
     */
    @Override
    public List<RecommendedCarsVo> recommendedCars(RecommendedCarsReq req) {
        List<RecommendedCarsVo> carsVoList = new ArrayList<>();
        // 先请求数据部门接口获取数据
        List<BiRecommendedCarsVo> recommendedCarsVoList = getBiRecommendedCarsVos(req);
        if (CollectionUtil.isEmpty(recommendedCarsVoList)) {
            return null;
        }
        List<Long> userIdList = recommendedCarsVoList.stream().map(BiRecommendedCarsVo::getUserId).collect(Collectors.toList());
        // 先查出条件过滤的用户
        if (StringUtils.isNotBlank(req.getCarType()) || StringUtils.isNotBlank(req.getCarStyle())) {
            userIdList = tytCarDetailTailMapper.selectUserId(req, userIdList);
        }
        if (CollectionUtil.isEmpty(userIdList)) {
            return null;
        }
        // 列表查询
        List<TytCarDetailTail> tytCarDetailTailList = tytCarDetailTailMapper.selectCarDetailTail(userIdList);
        if (CollectionUtil.isNotEmpty(tytCarDetailTailList)) {
            //查看是否注册
            List<UserBaseVO> userBaseVOList = tytUserMapper.selectByIds(userIdList);
            if (CollectionUtil.isEmpty(userBaseVOList)) {
                return null;
            }
            //查询是否推送
            List<Long> pushedUserIdList = tytTransportDispatchPushMapper.selectPushStatus(userIdList, req.getSrcMsgId());
            // 查询备注
            List<TytTransportDispatchView> remarkList = tytTransportDispatchViewMapper.selectDispatchView(userIdList, req.getSrcMsgId());


            Map<Long, Set<String>> carTypeSetMap = new HashMap<>();
            Map<Long, Set<String>> carStyleSetMap = new HashMap<>();
            for (TytCarDetailTail carDetailTail : tytCarDetailTailList) {
                RecommendedCarsVo vo = new RecommendedCarsVo();
                vo.setSrcMsgId(req.getSrcMsgId());
                // 组装类型，样式，板长信息
                if (CollectionUtil.isEmpty(carsVoList)) {
                    buildTypeStyleLength(carTypeSetMap, carStyleSetMap, carDetailTail, vo);
                } else {
                    Map<Long, List<RecommendedCarsVo>> collect = carsVoList.stream().collect(Collectors.groupingBy(RecommendedCarsVo::getCarUserId));
                    if (collect.keySet().contains(carDetailTail.getUserId())) {
                        //过滤重复数据
                        filterDuplicateData(carTypeSetMap, carStyleSetMap, carDetailTail, collect);
                        continue;
                    } else {
                        buildTypeStyleLength(carTypeSetMap, carStyleSetMap, carDetailTail, vo);
                    }
                }
                //设置基本信息
                for (UserBaseVO userBaseVO : userBaseVOList) {
                    if (userBaseVO.getUserId().equals(carDetailTail.getUserId())) {
                        vo.setCarUserId(userBaseVO.getUserId());
                        vo.setCarPhone(userBaseVO.getCellPhone());
                        vo.setCarUserName(userBaseVO.getTrueName());
                    }
                }
                //如果没有用户信息跳过
                if (vo.getCarUserId() == null) {
                    continue;
                }
                //设置匹配分数
                for (BiRecommendedCarsVo biRecommendedCarsVo : recommendedCarsVoList) {
                    if (biRecommendedCarsVo.getUserId().equals(carDetailTail.getUserId())) {
                        vo.setMatchScore(biRecommendedCarsVo.getMatchScore());
                        vo.setHistoryOrders(biRecommendedCarsVo.getHistoryOrders());
                        vo.setDestDistance(biRecommendedCarsVo.getDestDistance());
                    }
                }
                //判断是否推送
                vo.setPushed(pushedUserIdList.contains(carDetailTail.getUserId()));
                //添加备注信息
                for (TytTransportDispatchView remark : remarkList) {
                    if (remark.getCarUserId().equals(vo.getCarUserId())) {
                        vo.setRemark(remark.getRemark());
                    }
                }
                // 查看是否有订单
//                int count = tytTransportOrdersMapper.selectOnOpenOrders(carDetailTail.getUserId());
//                if (count > 1) {
//                    vo.setHaveOnlineOrder("1");
//                } else {
//                    vo.setHaveOnlineOrder("0");
//                }
//
//                if (StringUtils.isNotBlank(req.getHaveOnlineOrder()) && !req.getHaveOnlineOrder().equals(vo.getHaveOnlineOrder())) {
//                    continue;
//                }
                carsVoList.add(vo);
            }
            // 按匹配分排序
            carsVoList = carsVoList.stream().sorted(Comparator.comparing(o -> new BigDecimal(o.getMatchScore()), Comparator.reverseOrder()))
                    .collect(Collectors.toList());

        }


        return carsVoList;
    }

    private void filterDuplicateData(Map<Long, Set<String>> carTypeSetMap, Map<Long, Set<String>> carStyleSetMap, TytCarDetailTail carDetailTail, Map<Long, List<RecommendedCarsVo>> collect) {
        RecommendedCarsVo vo = collect.get(carDetailTail.getUserId()).get(0);
        Set<String> carTypeSet = carTypeSetMap.get(carDetailTail.getUserId());
        carTypeSet = carTypeSet == null ? new HashSet<>() : carTypeSet;
        if (StringUtils.isNotBlank(carDetailTail.getCarType())) {
            carTypeSet.add(carDetailTail.getCarType());
            carTypeSetMap.put(carDetailTail.getUserId(), carTypeSet);
        }

        Set<String> carStyleSet = carStyleSetMap.get(carDetailTail.getUserId());
        carStyleSet = carStyleSet == null ? new HashSet<>() : carStyleSet;
        if (carDetailTail.getIsPureFlat() != null) {
            carStyleSet.add(String.valueOf(carDetailTail.getIsPureFlat()));
            carStyleSetMap.put(carDetailTail.getUserId(), carStyleSet);
        }

        vo.setCarType(String.join(",", carTypeSet));
        vo.setCarStyle(String.join(",", carStyleSet));
    }

    /**
     * @param carTypeSetMap
     * @param carStyleSetMap
     * @param carDetailTail
     * @param vo
     */
    private void buildTypeStyleLength(Map<Long, Set<String>> carTypeSetMap, Map<Long, Set<String>> carStyleSetMap, TytCarDetailTail carDetailTail, RecommendedCarsVo vo) {
        Set<String> carTypeSet = new HashSet<>();
        if (StringUtils.isNotBlank(carDetailTail.getCarType())) {
            carTypeSet.add(carDetailTail.getCarType());
            carTypeSetMap.put(carDetailTail.getUserId(), carTypeSet);
        }

        Set<String> carStyleSet = new HashSet<>();
        if (carDetailTail.getIsPureFlat() != null) {
            carStyleSet.add(String.valueOf(carDetailTail.getIsPureFlat()));
            carStyleSetMap.put(carDetailTail.getUserId(), carStyleSet);
        }
        vo.setCarType(String.join(",", carTypeSet));
        vo.setCarStyle(String.join(",", carStyleSet));
    }

    /**
     * 调用数据部门接口
     *
     * @param req
     * @return
     */
    @SneakyThrows
    private List<BiRecommendedCarsVo> getBiRecommendedCarsVos(RecommendedCarsReq req) {

        BiRecommendedReq biRecommendedReq = new BiRecommendedReq();
        biRecommendedReq.setApiSign(RemoteApiConstant.api_sign);
        biRecommendedReq.setApiTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        biRecommendedReq.setGoodsId(String.valueOf(req.getSrcMsgId()));
        biRecommendedReq.setScoreMin(req.getMinMatchScore());
        biRecommendedReq.setScoreMax(req.getMaxMatchScore());
        if ("online".equals(profile)) {
            biRecommendedReq.setRunType("0");
        } else if ("release".equals(profile)) {
            biRecommendedReq.setRunType("3");
        } else {
            biRecommendedReq.setRunType("1");
        }

        BiResponse<List<BiRecommendedCarsVo>> response =
                biService.getFindGoodsPush(BiService.param2Map(biRecommendedReq)).execute().body();
        log.info("调用 bi getFindGoodsPush req: {}, 返回数据: {}", biRecommendedReq, response);
        return Optional.ofNullable(response).map(BiResponse::getData).orElse(Collections.emptyList());
    }

    /**
     * 保存/修改给货货主手机号
     *
     * @param srcMsgId
     * @param giveGoodsPhone
     */
    @Override
    public void saveGiveGoodsPhone(Long srcMsgId, String giveGoodsPhone) {
        if (StringUtils.isNotBlank(giveGoodsPhone)) {
            if (!StringBaseUtils.isMobileOrFixedPhone(giveGoodsPhone)) {
                super.throwException(ResponseEnum.request_error.info("给货货主手机格式错误"));
            }
            TytTransportDispatch tytTransportDispatch = tytTransportDispatchMapper.getTytTransportDispatchBySrcId(srcMsgId);
            if (tytTransportDispatch != null) {
                tytTransportDispatch.setGiveGoodsPhone(giveGoodsPhone);
                tytTransportDispatch.setModifyTime(new Date());
                tytTransportDispatchMapper.updateByPrimaryKey(tytTransportDispatch);
            }
        }

    }

    @Override
    public void updateOwnerFreight(Long srcMsgId, BigDecimal ownerFreight) {
        TytTransportMain transportMain = transportMainService.getTransportMainById(srcMsgId);
        if (Objects.isNull(transportMain)) {
            super.throwException(DispatchResponseEnum.transport_error.info("货源不存在！"));
        }
        //只有发布中的货源可以修改 产品定的
        if (transportMain.getStatus() != 1 || !DateUtil.isSameDay(new Date(), transportMain.getCtime())) {
            super.throwException(DispatchResponseEnum.no_modification_allowed.info("不允许修改！"));
        }
        tytTransportDispatchMapper.updateOwnerFreightBySrcMsgId(srcMsgId, ownerFreight);
    }

    @Override
    public void makeSourceTypeFieldForPublishingList(PageData<TransportManageVO> transportManagePage, boolean isSpecialCar) {
        if (transportManagePage == null) {
            return;
        }
        List<TransportManageVO> transportManageVOList = transportManagePage.getList();
        if (transportManageVOList != null && transportManageVOList.size() > 0) {
            List<Long> srcMsgIds = transportManageVOList.stream().map(TransportManageVO::getSrcMsgId).collect(Collectors.toList());
            List<TytTransportBackend> existTransport = tytTransportBackendMapper.findExistBySrcMsgIds(srcMsgIds);
            List<Long> existSrcMsgId = existTransport.stream().map(TytTransportBackend::getSrcMsgId).collect(Collectors.toList());
            /**
             *  6350 货源同步 新增货源来源枚举
             */
            List<TytTransportMbMerge> ymmExistBySrcMsgIds = tytTransportMbMergeMapper.findExistBySrcMsgIds(srcMsgIds);
            List<Long> ymmExistSrcMsgId = ymmExistBySrcMsgIds.stream().map(TytTransportMbMerge::getSrcMsgId).collect(Collectors.toList());
            for (TransportManageVO transportManageVO : transportManageVOList) {
                if (existSrcMsgId.contains(transportManageVO.getSrcMsgId())) {
                    transportManageVO.setOwnerSourceType("货主小程序");
                } else if (ymmExistSrcMsgId.contains(transportManageVO.getSrcMsgId())) {
                    transportManageVO.setOwnerSourceType("运满满货源");
                } else if (transportManageVO.getSourceType() == 5) {
                    transportManageVO.setOwnerSourceType("协议货源-宏信");
                } else {
                    if (isSpecialCar) {
                        if (Objects.equals(transportManageVO.getSourceType(), SourceTypeEnum.normal.getCode())) {
                            transportManageVO.setOwnerSourceType("货主发货");
                        } else {
                            transportManageVO.setOwnerSourceType("调度发货");
                        }
                    } else {
                        transportManageVO.setOwnerSourceType("调度发货");
                    }
                }
            }

            // 签约合作商id-name映射
            Map<Long, String> cargoOwnerMap = tytDispatchCooperativeMapper.selectAll().stream()
                    .collect(Collectors.toMap(TytDispatchCooperative::getId, TytDispatchCooperative::getCooperativeName));
            cargoOwnerMap.put(0L, "平台");

            TytInternalEmployee employee = getSessionEmployee();
            CsBusinessUserBind businessUserBind = csBusinessUserBindService.getCsBusinessUserByPhoneNo(employee.getLoginPhoneNo());
            Integer designateStatusByCsUserId = tytTransportValuableMapper.getValuableTransportCsUserDesignateStatusByCsUserId(businessUserBind.getId());

            for (TransportManageVO transportManageVO : transportManageVOList) {
                transportManageVO.setGoodCarPriceTransport(0);
                if (StringUtils.isNotBlank(transportManageVO.getLabelJson())) {
                    TransportLabelJson transportLabelJson = JSONObject.parseObject(transportManageVO.getLabelJson(), TransportLabelJson.class);
                    if (transportLabelJson != null && transportLabelJson.getGoodCarPriceTransport() != null && transportLabelJson.getGoodCarPriceTransport() == 1) {
                        transportManageVO.setGoodCarPriceTransport(1);
                    }
                }
                // 设置成交时长
                if (transportManageVO.getDoneTime() != null && transportManageVO.getCreateTime() != null) {
                    long diff = transportManageVO.getDoneTime().getTime() - transportManageVO.getCreateTime().getTime();
                    transportManageVO.setDonePeriod(DurationFormatUtils.formatDuration(diff, "HH:mm:ss"));
                }
                // 设置签约合作商名称
                if (transportManageVO.getCargoOwnerId() != null) {
                    transportManageVO.setCargoOwnerName(cargoOwnerMap.get(transportManageVO.getCargoOwnerId()));
                }

                //该价值货源是否显示指派按钮
                transportManageVO.setValuableTransportCanDesignate(0);
                if (designateStatusByCsUserId != null && designateStatusByCsUserId == 1) {
                    transportManageVO.setValuableTransportCanDesignate(1);
                }

            }
        }
    }

    @Override
    public WebResult saveInfoFeeDiff(Long srcMsgId, BigDecimal infoFeeDiff) {
        if (infoFeeDiff != null) {
            TytTransportDispatch tytTransportDispatch = tytTransportDispatchMapper.getTytTransportDispatchBySrcId(srcMsgId);
            if (tytTransportDispatch != null) {
                tytTransportDispatch.setInfoFeeDiff(infoFeeDiff);
                tytTransportDispatch.setModifyTime(new Date());
                tytTransportDispatchMapper.updateByPrimaryKey(tytTransportDispatch);
            }
            return WebResult.successResponse();
        } else {
            return WebResult.failResponse(DispatchResponseEnum.info_fee_diff_check_error.info());
        }
    }

    @Override
    public void saveDispatcherIdentity(String cellPhone, Integer dispatcherIdentityCode) {
        TytInternalEmployee sessionEmployee = super.getSessionEmployee();
        giveGoodsUserService.saveGiveGoodsUser(sessionEmployee.getId(), cellPhone, dispatcherIdentityCode);
    }

    @Override
    public PageData<SimilarGoodsVo> similarGoodsList(SimilarGoodsReq req) {
        List<SimilarGoodsVo> similarGoodsVoList = null;
        Date publishDate = StringUtils.isBlank(req.getPublishTime()) ? new Date() : new Date(Long.parseLong(req.getPublishTime()));
        Date startDayTime = DateUtil.beginOfDay(publishDate).toJdkDate();
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        similarGoodsVoList = tytTransportMapper.getSimilarity(req, startDayTime);
        if (CollectionUtil.isNotEmpty(similarGoodsVoList)) {
            similarGoodsVoList.forEach(s -> {
                TransportCountVO transportCountVO = tytTransportDispatchViewMapper.selectTotalCount(s.getSrcMsgId());
                if (transportCountVO != null) {
                    s.setViewCount(transportCountVO.getTotalViewCount());
                    s.setContactCount(transportCountVO.getTotalContactCount());
                }
            });
        }
        return new PageData<>(similarGoodsVoList);
    }


    @Override
    public List<SimilarViewContactVo> similarViewContactList(List<Long> srcMsgIdList) {
        return tytTransportDispatchViewMapper.similarViewContactList(srcMsgIdList);

    }


    @Override
    public PageData<VehicleSourceVo> vehicleSourceList(VehicleSourceReq vehicleSourceReq) {
        TytTransport tytTransport = tytTransportMapper.getTransportBySrcIdStutas(vehicleSourceReq.getSrcMsgId(), DateUtil.beginOfDay(new Date()));
        if (null == tytTransport) {
            throwException(DispatchResponseEnum.transport_error.info());
        }

        List<Long> srcMsgIds;
        if(Objects.equals(vehicleSourceReq.getType(),3)){
            //查询相似货源->当天发布，市到市级别且吨位上下5吨范围货源
            Double minWeigh = Double.parseDouble(tytTransport.getWeight()) - 5;
            Double maxWeight = Double.parseDouble(tytTransport.getWeight()) + 5;

            srcMsgIds = transportMainService.getSimilarTransport(tytTransport.getStartCity(),tytTransport.getDestCity(),minWeigh,maxWeight, DateUtil.beginOfDay(new Date()));
            tytTransport.getWeight();
        }else{
            srcMsgIds =Arrays.asList( vehicleSourceReq.getSrcMsgId());
        }

        if(CollectionUtil.isEmpty(srcMsgIds)){
            return new PageData<>(new ArrayList<VehicleSourceVo>());
        }


        PageHelper.startPage(vehicleSourceReq.getPageNum(), vehicleSourceReq.getPageSize());
        List<VehicleSourceVo> vehicleSourceList = tytTransportMapper.vehicleSourceList(srcMsgIds,vehicleSourceReq.getType());

        if (CollectionUtil.isNotEmpty(vehicleSourceList)) {
            vehicleSourceList.forEach(s -> {

                s.setCoopNums(tytTransportMapper.queryCoopNums(s.getCarUserId(),tytTransport.getUserId()));

                List<CarDetailVo> carDetailVos =  tytCarDetailTailMapper.selectCarDetailByUserId(s.getCarUserId());
                s.setCarDetailVos(carDetailVos);

                // 与货主承运次数:当前车主与所有调度的合作次数
                Integer cargoTransportCount = tytTransportMapper.getCargoTransportCount(s.getCarUserId());
                s.setCargoTransportCount(cargoTransportCount);
                // 同货源承运次数:同类型货源承运次数（例：挖掘机），时间范围为所有承运中该类型的次数
                Integer goodsTransportCount = tytTransportMapper.getGoodsTransportCount(s.getCarUserId(),tytTransport.getGoodTypeName());
                s.setGoodsTransportCount(goodsTransportCount);
                // 同线路承运次数:相同线路市到市级别且吨位上下5吨范围）货物承运次数，时间范围为所有承运中该路线切吨数上下5吨的次数
                String minWeigh = String.valueOf(Double.parseDouble(tytTransport.getWeight()) - 5);
                String maxWeight = String.valueOf(Double.parseDouble(tytTransport.getWeight()) + 5);
                Integer routeTransportCount = tytTransportMapper.getRouteTransportCount(s.getCarUserId(),tytTransport.getStartCity(),tytTransport.getDestCity(),minWeigh,maxWeight);
                s.setRouteTransportCount(routeTransportCount);

                s.getPushMsgId();
            });
        }
        return new PageData<>(vehicleSourceList);
    }

    @Override
    public void pushGoodsTips(PushGoodsTipReq pushGoodsTipReq) {
        TytTransportMain transportMain = transportMainService.getTransportMainById(pushGoodsTipReq.getSrcMsgId());

        if(transportMain == null){
            throwException(DispatchResponseEnum.transport_error.info());
        }

        TytUserSub userSub = userSubService.getTytUserSubByUserId(pushGoodsTipReq.getUserId());

        if(userSub == null){
            throwException(DispatchResponseEnum.user_not_exist.info());
        }

        Long tsUserId = transportMain.getUserId();

        if(pushGoodsTipReq.getUserId().equals(tsUserId)){
            throwException(DispatchResponseEnum.transport_push_error.info());
        }

        String[] infoArray = {transportMain.getTaskContent(), transportMain.getWeight() + "吨"};

        String infoText = StringUtils.join(infoArray, " ");

        String startAddress = CityUtil.createAddressInfo(transportMain.getStartCity(), transportMain.getStartArea());
        String destAddress = CityUtil.createAddressInfo(transportMain.getDestCity(), transportMain.getDestArea());

        GoodsPushDataVo goodsPushDataVo = new GoodsPushDataVo();
        String price = pushGoodsTipReq.getPrice();
        Long srcMsgId = transportMain.getSrcMsgId();
        String oldPrice = transportMain.getPrice();

        if(StringUtils.isEmpty(oldPrice)){
            oldPrice = "0";
        }
        String priceText = "";


        String title =  "平台推荐-可能合适";
        switch (pushGoodsTipReq.getPushType()){
            case 1:
                if(StringUtils.isEmpty(pushGoodsTipReq.getPrice())){
                    throwException(DispatchResponseEnum.transport_push_param_error.info());
                }
                title = "平台推荐-加价货源";
                transportMainService.changePrice(pushGoodsTipReq.getSrcMsgId(),pushGoodsTipReq.getPrice(),null);
                transportMain.setPrice(pushGoodsTipReq.getPrice());
                goodsPushDataVo.setOldPrice(new BigDecimal(oldPrice));
                break;
            case 2:
                price =oldPrice;
                title = "平台推荐-可能合适";
                break;
            case 3:
                price =oldPrice;
                title = "平台推荐-急走货源";
                break;
        }

        BigDecimal priceDec = null;
        if(StringUtils.isNotBlank(price)){
            priceDec = new BigDecimal(price);
            priceText = "运费【" + price + "】，";
        }

        goodsPushDataVo.setSrcMsgId(srcMsgId);
        goodsPushDataVo.setTitle(title);
        goodsPushDataVo.setStartAddress(startAddress);
        goodsPushDataVo.setDestAddress(destAddress);
        goodsPushDataVo.setInfoText(infoText);
        goodsPushDataVo.setPrice(priceDec);

        String extraData = JSON.toJSONString(goodsPushDataVo);

        String remarks = "pushGoodsTips:货源推送";

        String weight = transportMain.getWeight();
        String weightText = weight + "吨";
        String taskContent = transportMain.getTaskContent();

        String contentTmp = "有一条好货，从【%s】到【%s】，%s【%s】【%s】不容错过，快来看看吧。";

        String notifyContent = String.format(contentTmp, startAddress, destAddress, priceText, taskContent, weightText);

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(pushGoodsTipReq.getUserId());

        messagePushBase.setTitle(title);
        messagePushBase.setContent(notifyContent);
        messagePushBase.setRemarks(remarks);
        messagePushBase.setCarPush((short)1);

        //通知
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);

        notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
        notifyMessage.addNativeParameter("id", srcMsgId + "");

        notifyMessage.setPushCode(BusinessTagEnum.goods_push.name());
        notifyMessage.setExtraData(extraData);

        messageCenterPushService.sendMultiMessage(null, null, notifyMessage);
    }

    @Override
    public List<CurrentLocationVO> getCarLocus(String carHeadNo) {
        Integer specialCarLastLocationSwitch = tytConfigService.getIntValue(SPECIAL_CAR_LAST_LOCATION_SWITCH, 0);
        log.info("getCarLocus specialCarLastLocationSwitch:{}", specialCarLastLocationSwitch);
        if(specialCarLastLocationSwitch ==0){
            return carLocusService.getCurrentLocation(carHeadNo);
        }else {
            return carLocusService.getZJCurrentLocation(carHeadNo);
        }
    }

    @Override
    public void carQuotation(CarQuotationReq carQuotationReq) {
        Example exa = new Example(TytTransportCapacity.class);
        exa.and().andEqualTo("carUserId", carQuotationReq.getCarUserId());
        exa.and().andEqualTo("srcMsgId", carQuotationReq.getSrcMsgId());
        List<TytTransportCapacity> transportCapacities =  tytTransportCapacityMapper.selectByExample(exa);

        if(CollectionUtil.isEmpty(transportCapacities)){
            TytTransportCapacity tytTransportCapacity = TytTransportCapacity.builder()
                    .carQuote(carQuotationReq.getCarQuote())
                    .carUserId(carQuotationReq.getCarUserId())
                    .srcMsgId(carQuotationReq.getSrcMsgId())
                    .createTime(new Date())
                    .build();
            tytTransportCapacityMapper.insertSelective(tytTransportCapacity);
            return;
        }

        TytTransportCapacity tytTransportCapacity = transportCapacities.get(0);
        tytTransportCapacity.setCarQuote(carQuotationReq.getCarQuote());
        tytTransportCapacityMapper.updateByPrimaryKeySelective(tytTransportCapacity);
    }

    @Override
    public TransportRateRuleVO transportRateRule(TransportRateRuleReq transportRateRuleReq) {

        //srcMsgId不为空查询货源信息、查询建议价
        if(StringUtils.isNotBlank(transportRateRuleReq.getSrcMsgId())){
            Example exa = new Example(TytTransport.class);
            exa.and().andEqualTo("srcMsgId", transportRateRuleReq.getSrcMsgId());
            List<TytTransport> transports =  tytTransportMapper.selectByExample(exa);

            if(CollectionUtil.isEmpty(transports)){
                super.throwException(DispatchResponseEnum.transport_error.info());
            }

            TytTransport tytTransport = transports.get(0);
            //获取BI建议价
            SuggestPriceReq suggestPriceReq = SuggestPriceReq.builder()
                    .userId(tytTransport.getUserId())
                    .startProvince(tytTransport.getStartProvinc())
                    .destProvince(tytTransport.getDestProvinc())
                    .startCity(tytTransport.getStartCity())
                    .destCity(tytTransport.getDestCity())
                    .startArea(tytTransport.getStartArea())
                    .destArea(tytTransport.getDestArea())
                    .goodsName(tytTransport.getTaskContent())
                    .goodsHigh(Double.parseDouble(tytTransport.getHigh()))
                    .goodsWeight(Double.parseDouble(tytTransport.getWeight()))
                    .goodsLength(Double.parseDouble(tytTransport.getLength()))
                    .goodsWide(Double.parseDouble(tytTransport.getWide()))
                    .excellentGoods(tytTransport.getExcellentGoods())
                    .distance(String.valueOf(tytTransport.getDistance()))
                    .build();
            CarryPriceVo carryPriceVo = transportMainService.getSuggestPrice(suggestPriceReq);

            //设置基础信息
            transportRateRuleReq.setStartCity(tytTransport.getStartCity());
            transportRateRuleReq.setDestCity(tytTransport.getDestCity());
            transportRateRuleReq.setStartArea(tytTransport.getStartArea());
            transportRateRuleReq.setDestArea(tytTransport.getDestArea());
            transportRateRuleReq.setStartProvince(tytTransport.getStartProvinc());
            transportRateRuleReq.setDestProvince(tytTransport.getDestProvinc());
            transportRateRuleReq.setGoodsWeight(Double.parseDouble(tytTransport.getWeight()));
            transportRateRuleReq.setBasePrice(Objects.nonNull(carryPriceVo)?BigDecimal.valueOf(carryPriceVo.getBasePrice()):new BigDecimal(0));
        }


        //计算出价等级
        TransportRateRuleVO transportRateRuleVO = new TransportRateRuleVO();
        if(Objects.nonNull(transportRateRuleReq.getPrice())&&Objects.nonNull(transportRateRuleReq.getBasePrice())){
            BigDecimal quotient = transportRateRuleReq.getPrice().divide(transportRateRuleReq.getBasePrice(),2, RoundingMode.HALF_UP);
            if(quotient.compareTo(new BigDecimal(0))>=0&quotient.compareTo(new BigDecimal(0.7))<0){
                transportRateRuleVO.setPriceGrade("D");
            } else if (quotient.compareTo(new BigDecimal(0.7))>=0&quotient.compareTo(new BigDecimal(0.9))<0) {
                transportRateRuleVO.setPriceGrade("C");
            } else if (quotient.compareTo(new BigDecimal(0.9))>=0&quotient.compareTo(new BigDecimal(1.1))<0) {
                transportRateRuleVO.setPriceGrade("B");
            } else if (quotient.compareTo(new BigDecimal(1.1))>=0&quotient.compareTo(new BigDecimal(1.3))<0) {
                transportRateRuleVO.setPriceGrade("A");
            }else {
                transportRateRuleVO.setPriceGrade("S");
            }
        }

        HistoryOrdersRouteDataVO historyOrdersRouteDataVO =  historyOrdersRouteDataMapper.getOrdersRouteData(transportRateRuleReq);
        transportRateRuleVO.setHistoryOrdersRouteData(historyOrdersRouteDataVO);
        return transportRateRuleVO;
    }

    @Override
    public TransportNumVO getTransportNum(Long dispatcherId) {

        TransportNumVO transportNumVO = TransportNumVO.builder().build();

        //获取当前登录用户
        TytInternalEmployee employee = getSessionEmployee();
        CsBusinessUserBind businessUserBind = getCsBusinessUserByPhoneNo(employee.getLoginPhoneNo());
        Integer transportAppletNum = tytTransportDispatchMapper.countTransportApplet(businessUserBind,employee);
        transportNumVO.setTransportAppletNum(transportAppletNum);


        List<Integer> roleIds = Arrays.asList(EmployeeRoleEnum.dispatch_manager.getCode(),
                EmployeeRoleEnum.dispatch_manager_01.getCode());
        List<CsBusinessUserBind> csBusinessUserBinds = csBusinessUserBindMapper.selectByDispatcherId(dispatcherId,roleIds);
        if(!CollectionUtils.isEmpty(csBusinessUserBinds)){
            Integer transportYmmNum = tytTransportYMMMapper.countAllTransportYMMList();
            transportNumVO.setTransportYmmNum(transportYmmNum);
            return transportNumVO;
        }

        Integer transportYmmNum = tytTransportYMMMapper.countTransportYMMList(dispatcherId);
        transportNumVO.setTransportYmmNum(transportYmmNum);
        return transportNumVO;
    }


    private CsBusinessUserBind getCsBusinessUserByPhoneNo(String cellPhone) {

        Example example = new Example(CsBusinessUserBind.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("loginPhoneNo", cellPhone);
        criteria.andEqualTo("isValid", "1");
        return csBusinessUserBindMapper.selectOneByExample(example);
    }
}
