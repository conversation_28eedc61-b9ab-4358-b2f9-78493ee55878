package com.tyt.cargo.dispatch.web.service.price.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.tyt.cargo.core.enums.ResponseEnum;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.dispatch.client.form.price.SpecialCarPriceConfigReq;
import com.tyt.cargo.dispatch.client.vo.price.CargoOwnerInfoVo;
import com.tyt.cargo.dispatch.client.vo.price.SpecialCarPriceConfigVo;
import com.tyt.cargo.dispatch.web.bean.custom.ImportCustomFlagBean;
import com.tyt.cargo.dispatch.web.bean.price.ImportPriceConfigBean;
import com.tyt.cargo.dispatch.web.bean.price.PriceConfigBean;
import com.tyt.cargo.dispatch.web.easyexcel.ImportCustomFlagExcelListener;
import com.tyt.cargo.dispatch.web.easyexcel.ImportPriceConfigExcelListener;
import com.tyt.cargo.dispatch.web.enums.PriceConfigDelStatusEnum;
import com.tyt.cargo.dispatch.web.enums.PriceConfigStatusEnum;
import com.tyt.cargo.dispatch.web.enums.PriceConfigTypeEnum;
import com.tyt.cargo.dispatch.web.enums.PriceTypeEnum;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytDispatchCooperative;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytInternalEmployee;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytSpecialCarPriceConfig;
import com.tyt.cargo.dispatch.web.mybatis.mapper.base.TytDispatchCooperativeMapper;
import com.tyt.cargo.dispatch.web.mybatis.mapper.base.TytSpecialCarPriceConfigMapper;
import com.tyt.cargo.dispatch.web.service.base.impl.DispatchBaseServiceImpl;
import com.tyt.cargo.dispatch.web.service.custom.CustomService;
import com.tyt.cargo.dispatch.web.service.price.SpecialCarPriceConfigService;
import com.tyt.cargo.dispatch.web.util.ApplicationContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

/**
 * 专车运费配置
 * <AUTHOR>
 * @since 2024-06-22 10:51
 */
@Service
@Slf4j
public class SpecialCarPriceConfigServiceImpl extends DispatchBaseServiceImpl implements SpecialCarPriceConfigService {

    private static final String NOT_UNIQUE_ERROR_MSG = "该企业已存在路线和吨位相同的配置，请检查！";
    private static final String TONNAGE_ILLEGAL_MSG = "终止吨位应大于起始吨位！";
    private static final String FULL_PRICE_LOWER_LIMIT_ERROR = "整车运价下限不能为空！";
    private static final String LESS_PRICE_LOWER_LIMIT_ERROR = "零担运价下限不能为空！";
    private static final String TONNAGE_OVERLAP_ERROR_MSG = "吨位区间和已存在的运费配置重合，请修改后重新提交！";
    private static final String PRICE_RULE_FORMAT_ERROR = "运费配置字段格式错误";
    private static final String PRICE_TYPE_VALUE_ERROR = "运费价格类型字段(type)传值错误";
    private static final String MILEAGE_NOT_CONTINUOUS_ERROR = "当前里程分段不连续，请核验后再提交！";
    private static final String FIRST_START_NOT_ZERO_ERROR = "首条配置起始公里数不为0";

    @Autowired
    private TytSpecialCarPriceConfigMapper tytSpecialCarPriceConfigMapper;
    @Autowired
    private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;

    /**
     * 专车运费配置列表
     * <AUTHOR>
     * @date 2024/6/22 10:52
     * @param req
     * @return PageData<SpecialCarPriceConfigVo>
     */
    @Override
    public PageData<SpecialCarPriceConfigVo> priceConfigList(SpecialCarPriceConfigReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<SpecialCarPriceConfigVo> list = tytSpecialCarPriceConfigMapper.selectPriceConfigList(req);
        return new PageData<>(list);
    }

    /**
     * 添加或编辑专车运费配置
     * <AUTHOR>
     * @date 2024/6/22 13:30
     * @param priceConfigReq
     * @return void
     */
    @Override
    public WebResult addOrUpdatePriceConfig(SpecialCarPriceConfigReq priceConfigReq) {
        // 参数校验：运费规则校验、数据唯一性校验
        String checkMsg = checkPriceConfig(priceConfigReq);
        if (StringUtils.isNotEmpty(checkMsg)) {
            return WebResult.failResponse(ResponseEnum.request_error.info(checkMsg));
        }

        TytInternalEmployee sessionEmployee = getSessionEmployee();

        if (Objects.isNull(priceConfigReq.getId())) {
            // 新增
            TytSpecialCarPriceConfig priceConfig = new TytSpecialCarPriceConfig();
            BeanUtils.copyProperties(priceConfigReq, priceConfig);
            priceConfig.setCreateUserId(sessionEmployee.getId());
            priceConfig.setCreateUserName(sessionEmployee.getName());
            priceConfig.setCreateTime(new Date());
            tytSpecialCarPriceConfigMapper.insertSelective(priceConfig);
        } else {
            // 修改
            TytSpecialCarPriceConfig priceConfig = tytSpecialCarPriceConfigMapper.selectByPrimaryKey(priceConfigReq.getId());
            priceConfig.setModifyUserId(sessionEmployee.getId());
            priceConfig.setModifyUserName(sessionEmployee.getName());
            priceConfig.setModifyTime(new Date());
            priceConfig.setStartCity(priceConfigReq.getStartCity());
            priceConfig.setDestCity(priceConfigReq.getDestCity());
            priceConfig.setStartTonnage(priceConfigReq.getStartTonnage());
            priceConfig.setEndTonnage(priceConfigReq.getEndTonnage());
            priceConfig.setCargoOwnerId(priceConfigReq.getCargoOwnerId());
            priceConfig.setCargoOwnerName(priceConfigReq.getCargoOwnerName());
            priceConfig.setPriceRule(priceConfigReq.getPriceRule());
            priceConfig.setLessPriceRule(priceConfigReq.getLessPriceRule());
            priceConfig.setDrivingFee(Objects.isNull(priceConfigReq.getDrivingFee()) ? new BigDecimal("0") : priceConfigReq.getDrivingFee());
            priceConfig.setRemark(StringUtils.isEmpty(priceConfigReq.getRemark()) ? "" : priceConfigReq.getRemark());
            priceConfig.setPriceType(priceConfigReq.getPriceType());
            priceConfig.setPriceLowerLimit(priceConfigReq.getPriceLowerLimit());
            priceConfig.setLessPriceLowerLimit(priceConfigReq.getLessPriceLowerLimit());
            tytSpecialCarPriceConfigMapper.updateByPrimaryKey(priceConfig);
        }

        return WebResult.successResponse();
    }

    /**
     * 添加或修改运费规则校验
     * @param priceConfigReq
     * @return
     */
    private String checkPriceConfig(SpecialCarPriceConfigReq priceConfigReq) {
        if (priceConfigReq.getStartTonnage() >= priceConfigReq.getEndTonnage()) {
            return TONNAGE_ILLEGAL_MSG;
        }
        // 灵活运价模式下，运价下限必填
        if (Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), priceConfigReq.getPriceType())) {
            if (Objects.isNull(priceConfigReq.getPriceLowerLimit())) {
                return FULL_PRICE_LOWER_LIMIT_ERROR;
            }
            if (Objects.isNull(priceConfigReq.getLessPriceLowerLimit())) {
                return LESS_PRICE_LOWER_LIMIT_ERROR;
            }
        }

        // priceRule json格式校验
        String priceRuleCheckMsg = checkPriceRuleJson(priceConfigReq.getPriceRule());
        if (StringUtils.isNotEmpty(priceRuleCheckMsg)) {
            return priceRuleCheckMsg;
        }
        // 零担运费规则 lessPriceRule json格式校验
        String lessPriceRuleCheckMsg = checkPriceRuleJson(priceConfigReq.getLessPriceRule());
        if (StringUtils.isNotEmpty(priceRuleCheckMsg)) {
            return "零担运费规则" + lessPriceRuleCheckMsg;
        }

        // 路线、吨位、所属企业全部不一致可新增，否则不能添加或编辑
        int count = tytSpecialCarPriceConfigMapper.countPriceConfigForUnique(priceConfigReq);
        if (count > 0) {
            return NOT_UNIQUE_ERROR_MSG;
        }

        // 吨位区间不能重合
        List<TytSpecialCarPriceConfig> configList = tytSpecialCarPriceConfigMapper.selectByRouteAndCargoOwner(priceConfigReq);
        for (TytSpecialCarPriceConfig config : configList) {
            if ((priceConfigReq.getStartTonnage() > config.getStartTonnage() && priceConfigReq.getStartTonnage() < config.getEndTonnage()) ||
                    (priceConfigReq.getEndTonnage() > config.getStartTonnage() && priceConfigReq.getEndTonnage() < config.getEndTonnage()) ||
                    (priceConfigReq.getStartTonnage() >= config.getStartTonnage() && priceConfigReq.getEndTonnage() <= config.getEndTonnage()) ||
                    (priceConfigReq.getStartTonnage() <= config.getStartTonnage() && priceConfigReq.getEndTonnage() >= config.getEndTonnage())) {
                return TONNAGE_OVERLAP_ERROR_MSG;
            }
        }
        return null;
    }

    /**
     * priceRule json字段的校验
     *     [
     *         {
     *             "start": 0, // 起始公里
     *             "end": 10,  // 终止公里
     *             "type": 1,  // 价格类型：1-固定价格，2-公里价
     *             "price": 410 // 价格
     *         },
     *         {
     *             "start": 10,
     *             "end": 20,
     *             "type": 2,
     *             "price": 8.5  // 8.5元/公里
     *         },
     *         {
     *             "start": 20,
     *             "end": 999999, // 如果终止公里数为空，赋值999999
     *             "type": 2,
     *             "price": 11.5
     *         }
     *     ]
     * @param priceRule
     * @return
     */
    @Nullable
    private static String checkPriceRuleJson(String priceRule) {
        List<PriceConfigBean> priceConfigBeans;
        try {
            priceConfigBeans = JSON.parseArray(priceRule, PriceConfigBean.class);
        } catch (Exception e) {
            log.error("checkPriceConfig error, priceRule:{}", priceRule, e);
            return PRICE_RULE_FORMAT_ERROR;
        }

        // 配置项为空
        if (CollectionUtils.isEmpty(priceConfigBeans)) {
            return PRICE_RULE_FORMAT_ERROR;
        }

        try {
            priceConfigBeans.sort(Comparator.comparing(PriceConfigBean::getStart));
        } catch (Exception e) {
            log.error("checkPriceRuleJson排序priceConfigBeans时发生异常，priceConfigBeans:{}", priceConfigBeans, e);
            return PRICE_RULE_FORMAT_ERROR;
        }
        for (int i = 0; i < priceConfigBeans.size(); i++) {
            PriceConfigBean currentConfig = priceConfigBeans.get(i);
            // 检查单个配置项的合法性
            String checkResult = checkSinglePriceConfigRecord(currentConfig);
            if (StringUtils.isNotEmpty(checkResult)) {
                return checkResult;
            }
            // 首条配置默认从0开始
            if (i == 0) {
                if (currentConfig.getStart().compareTo(new BigDecimal("0")) != 0) {
                    return FIRST_START_NOT_ZERO_ERROR;
                }
            } else {
                // 检查相邻配置项的连续性
                PriceConfigBean previousConfig = priceConfigBeans.get(i - 1);
                if (previousConfig.getEnd().compareTo(currentConfig.getStart()) != 0) {
                    return MILEAGE_NOT_CONTINUOUS_ERROR;
                }
            }
        }
        return null;
    }

    /**
     * 单条价格配置的校验
     * @param priceConfigBean
     * @return
     */
    @Nullable
    private static String checkSinglePriceConfigRecord(PriceConfigBean priceConfigBean) {
        if (Objects.isNull(priceConfigBean.getStart()) || Objects.isNull(priceConfigBean.getEnd()) ||
                Objects.isNull(priceConfigBean.getType()) || Objects.isNull(priceConfigBean.getPrice()) ||
                priceConfigBean.getStart().compareTo(priceConfigBean.getEnd()) >= 0 ) {
            return PRICE_RULE_FORMAT_ERROR;
        }
        if (!priceConfigBean.getType().equals(PriceConfigTypeEnum.FIXED_PRICE.getCode()) &&
                !priceConfigBean.getType().equals(PriceConfigTypeEnum.KILOMETER_PRICE.getCode())) {
            return PRICE_TYPE_VALUE_ERROR;
        }
        return null;
    }

    /**
     * 获取价格配置详情
     * <AUTHOR>
     * @date 2024/6/22 18:37
     * @param id
     * @return WebResult<SpecialCarPriceConfigVo>
     */
    @Override
    public SpecialCarPriceConfigVo getDetail(Long id) {
        Example example = new Example(TytSpecialCarPriceConfig.class);
        example.and()
                .andEqualTo("id", id)
                .andEqualTo("delStatus", PriceConfigDelStatusEnum.NORMAL.getCode());
        List<TytSpecialCarPriceConfig> configList = tytSpecialCarPriceConfigMapper.selectByExample(example);

        SpecialCarPriceConfigVo configVo = null;
        if (!CollectionUtils.isEmpty(configList)) {
            configVo = new SpecialCarPriceConfigVo();
            BeanUtils.copyProperties(configList.get(0), configVo);
            if (configVo.getDrivingFee().compareTo(new BigDecimal("0")) == 0) {
                configVo.setDrivingFee(null);
            }
        }
        return configVo;
    }

    /**
     * 修改运费配置状态
     * <AUTHOR>
     * @date 2024/6/24 10:17
     * @param req
     * @return WebResult
     */
    @Override
    public WebResult updateStatus(SpecialCarPriceConfigReq req) {
        if (Objects.isNull(req.getId())) {
            return WebResult.failResponse(ResponseEnum.request_error.info("记录ID不能为空"));
        }
        if (Objects.isNull(req.getStatus())) {
            return WebResult.failResponse(ResponseEnum.request_error.info("记录ID不能为空"));
        }
        if (!PriceConfigStatusEnum.isLegalCode(req.getStatus())) {
            return WebResult.failResponse(ResponseEnum.request_error.info("状态值status不合法"));
        }

        WebResult<Object> checkMsg = checkRecordExist(req.getId());
        if (checkMsg != null) {
            return checkMsg;
        }

        TytInternalEmployee sessionEmployee = getSessionEmployee();

        TytSpecialCarPriceConfig config = new TytSpecialCarPriceConfig();
        config.setId(req.getId());
        config.setStatus(req.getStatus());
        config.setModifyUserId(sessionEmployee.getId());
        config.setModifyUserName(sessionEmployee.getName());
        config.setModifyTime(new Date());

        tytSpecialCarPriceConfigMapper.updateByPrimaryKeySelective(config);
        return WebResult.successResponse();
    }

    @Nullable
    private WebResult<Object> checkRecordExist(Long id) {
        Example example = new Example(TytSpecialCarPriceConfig.class);
        example.and()
                .andEqualTo("id", id)
                .andEqualTo("delStatus", PriceConfigDelStatusEnum.NORMAL.getCode());
        int count = tytSpecialCarPriceConfigMapper.selectCountByExample(example);
        if (count == 0) {
            return WebResult.failResponse(ResponseEnum.request_error.info("记录不存在"));
        }
        return null;
    }

    /**
     * 删除运费配置
     * <AUTHOR>
     * @date 2024/6/24 10:17
     * @param req
     * @return WebResult
     */
    @Override
    public WebResult delete(SpecialCarPriceConfigReq req) {
        if (Objects.isNull(req.getId())) {
            return WebResult.failResponse(ResponseEnum.request_error.info("记录ID不能为空"));
        }

        WebResult<Object> checkMsg = checkRecordExist(req.getId());
        if (checkMsg != null) {
            return checkMsg;
        }

        TytInternalEmployee sessionEmployee = getSessionEmployee();

        TytSpecialCarPriceConfig config = new TytSpecialCarPriceConfig();
        config.setId(req.getId());
        config.setDelStatus(PriceConfigDelStatusEnum.DELETED.getCode());
        config.setModifyUserId(sessionEmployee.getId());
        config.setModifyUserName(sessionEmployee.getName());
        config.setModifyTime(new Date());

        tytSpecialCarPriceConfigMapper.updateByPrimaryKeySelective(config);
        return WebResult.successResponse();
    }

    @Override
    public void importPriceConfig(MultipartFile file) {
        TytInternalEmployee employee = getSessionEmployee();

        try {
            EasyExcelFactory.read(file.getInputStream(),
                            ImportPriceConfigBean.class,
                            new ImportPriceConfigExcelListener(this, employee))
                    .sheet()
                    .doRead();
        } catch (Exception e) {
            log.error("导入专车运费失败, employeeId:{}", employee.getId(), e);
        }
    }

    @Override
    public void batchInsert(List<ImportPriceConfigBean> list, TytInternalEmployee employee) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ImportPriceConfigBean bean : list) {
            try {
                SpecialCarPriceConfigReq configReq = getSpecialCarPriceConfigReq(employee, bean);
                if (Objects.isNull(configReq)) {
                    log.info("专车运费导入，组装运费实体为空。路线：{}-{}，吨位:{}-{}", bean.getStartCity(),
                            bean.getDestCity(), bean.getStartTonnage(), bean.getEndTonnage());
                    continue;
                }
                String checkMsg = this.checkPriceConfig(configReq);
                if (StringUtils.isNotBlank(checkMsg)) {
                    log.info("专车运费导入, 运费规则校验失败. {}，路线：{}-{}，吨位:{}-{}", checkMsg, bean.getStartCity(),
                            bean.getDestCity(), bean.getStartTonnage(), bean.getEndTonnage());
                    continue;
                }
                TytSpecialCarPriceConfig priceConfig = new TytSpecialCarPriceConfig();
                BeanUtils.copyProperties(configReq, priceConfig);
                priceConfig.setCreateUserId(employee.getId());
                tytSpecialCarPriceConfigMapper.insertSelective(priceConfig);
            } catch (BeansException e) {
                log.error("专车运费导入, 插入失败，路线：{}-{}，吨位:{}-{}", bean.getStartCity(),
                        bean.getDestCity(), bean.getStartTonnage(), bean.getEndTonnage(), e);
            }
        }
    }

    private SpecialCarPriceConfigReq getSpecialCarPriceConfigReq(TytInternalEmployee employee, ImportPriceConfigBean bean) {
        SpecialCarPriceConfigReq configReq = new SpecialCarPriceConfigReq();
        configReq.setStartCity(bean.getStartCity());
        configReq.setDestCity(bean.getDestCity());
        configReq.setStartTonnage(bean.getStartTonnage());
        configReq.setEndTonnage(bean.getEndTonnage());
        configReq.setCargoOwnerId(1L);
        configReq.setCargoOwnerName("平台");
        if (StringUtils.isNotBlank(bean.getCooperativeName())) {
            TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByOwnerName(bean.getCooperativeName());
            if (Objects.nonNull(cooperative)) {
                configReq.setCargoOwnerId(cooperative.getId());
                configReq.setCargoOwnerName(cooperative.getCooperativeName());
            } else {
                log.info("专车运费导入，运费所属企业不存在或未启用，{}", JSONObject.toJSONString(bean));
                return null;
            }
        }
        configReq.setPriceType(PriceTypeEnum.FLEXIBLE.getCode());
        configReq.setLessPriceLowerLimit(bean.getPriceLowerLimit());
        configReq.setPriceLowerLimit(bean.getPriceLowerLimit());
        configReq.setStatus(PriceConfigStatusEnum.DISABLED.getCode());
        configReq.setCreateUserName(employee.getRealName());
        configReq.setRemark("系统导入");

        JSONArray priceArray = new JSONArray();
        if (Objects.nonNull(bean.getStartDistance()) && Objects.nonNull(bean.getStartPrice()) && Objects.nonNull(bean.getStepPrice())) {
            JSONObject priceJson1 = new JSONObject();
            priceJson1.put("start", 0);
            priceJson1.put("end", bean.getStartDistance());
            priceJson1.put("type", 1);
            priceJson1.put("price", bean.getStartPrice());
            priceArray.add(priceJson1);

            JSONObject priceJson2 = new JSONObject();
            priceJson2.put("start", bean.getStartDistance());
            priceJson2.put("end", 200);
            priceJson2.put("type", 2);
            priceJson2.put("price", bean.getStepPrice());
            priceArray.add(priceJson2);
        } else if (Objects.nonNull(bean.getSinglePrice())) {
            JSONObject priceJson = new JSONObject();
            priceJson.put("start", 0);
            priceJson.put("end", 200);
            priceJson.put("type", 2);
            priceJson.put("price", bean.getSinglePrice());
            priceArray.add(priceJson);
        } else {
            log.info("专车运费导入，数据格式不正确，{}", JSONObject.toJSONString(bean));
            return null;
        }
        configReq.setPriceRule(priceArray.toJSONString());
        return configReq;
    }

    /**
     * 查询专车运费所属企业列表
     * <AUTHOR>
     * @date 2024/6/24 10:51
     * @param req
     * @return WebResult
     */
    @Override
    public PageData<CargoOwnerInfoVo> cargoOwnerList(SpecialCarPriceConfigReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<CargoOwnerInfoVo> list = tytSpecialCarPriceConfigMapper.selectCargoOwnerList(req.getCargoOwnerName());
        return new PageData<>(list);
    }
}
