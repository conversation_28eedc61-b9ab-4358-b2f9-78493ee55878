package com.tyt.cargo.dispatch.web.mybatis.mapper.base;

import com.tyt.cargo.db.tool.CustomBaseMapper;
import com.tyt.cargo.dispatch.client.form.transport.TransportManageReq;
import com.tyt.cargo.dispatch.client.vo.transport.TransportManageVO;
import com.tyt.cargo.dispatch.client.vo.transport.ValuableTransportDetailVO;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBind;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytInternalEmployee;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytTransportValuable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TytTransportValuableMapper extends CustomBaseMapper<TytTransportValuable> {
    List<TransportManageVO> getValuableAllStatusList(TransportManageReq transportManageReq,
                                                     CsBusinessUserBind businessUserBind,
                                                     TytInternalEmployee employee);

    List<TransportManageVO> getValuablePublishingList(TransportManageReq transportManageReq,
                                                      CsBusinessUserBind businessUserBind,
                                                      TytInternalEmployee employee);

    List<TransportManageVO> getValuableCanceledList(TransportManageReq transportManageReq,
                                                    CsBusinessUserBind businessUserBind,
                                                    TytInternalEmployee employee);

    List<TransportManageVO> getValuableExpiredList(TransportManageReq transportManageReq,
                                                   CsBusinessUserBind businessUserBind,
                                                   TytInternalEmployee employee);

    List<TransportManageVO> getValuableDoneList(TransportManageReq transportManageReq,
                                                CsBusinessUserBind businessUserBind,
                                                TytInternalEmployee employee);

    int countByMarketFollowupUser(@Param("marketFollowupUserId") Long marketFollowupUserId,
                                  @Param("createTime") Date createTime);

    int countByCsFollowupUser(@Param("followupUserId") Long followupUserId,
                              @Param("createTime") Date createTime);

    ValuableTransportDetailVO getValuableDetailVOBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    TytTransportValuable getValuableBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    void changeValuableCsUser(@Param("srcMsgIds") List<Long> srcMsgIds, @Param("csUserId") Long csUserId, @Param("csUserName") String csUserName);

    void updateValuableTransportCsUserStatus(@Param("csUserId") Long csUserId, @Param("status") Integer status, @Param("designateStatus") Integer designateStatus);

    void insertValuableLog(@Param("csUserId") Long csUserId, @Param("srcMsgId") Long srcMsgId);

    Integer getValuableTransportCsUserDesignateStatusByCsUserId(@Param("csUserId") Long csUserId);

    Integer getValuableTransportCsUserStatusByCsUserId(@Param("csUserId") Long csUserId);
}