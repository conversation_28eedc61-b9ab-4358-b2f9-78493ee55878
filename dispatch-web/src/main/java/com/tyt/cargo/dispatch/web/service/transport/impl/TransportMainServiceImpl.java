package com.tyt.cargo.dispatch.web.service.transport.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tyt.cargo.core.enums.ResponseEnum;
import com.tyt.cargo.core.exception.TytException;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.core.util.*;
import com.tyt.cargo.dispatch.client.constant.DispatchConstant;
import com.tyt.cargo.dispatch.client.constant.PlatApiConstant;
import com.tyt.cargo.dispatch.client.enums.*;
import com.tyt.cargo.dispatch.client.form.transport.GoodCarPriceTransportParamBean;
import com.tyt.cargo.dispatch.client.form.transport.GoodCarPriceTransportTabAndBIPriceVO;
import com.tyt.cargo.dispatch.client.form.transport.GoodsStatusNewReq;
import com.tyt.cargo.dispatch.client.form.transport.SaveWayBillReq;
import com.tyt.cargo.dispatch.client.vo.custom.MeetCommissionRulesResult;
import com.tyt.cargo.dispatch.client.vo.remote.CarryPriceReq;
import com.tyt.cargo.dispatch.client.vo.remote.CarryPriceVo;
import com.tyt.cargo.dispatch.client.vo.transport.*;
import com.tyt.cargo.dispatch.client.vo.user.UserBaseVO;
import com.tyt.cargo.dispatch.web.bean.custom.AssignCarForGoodsBean;
import com.tyt.cargo.dispatch.web.bean.custom.AutoAssignOrderBean;
import com.tyt.cargo.dispatch.web.bean.custom.CheckInvoiceTransportUserAndParam;
import com.tyt.cargo.dispatch.web.bean.custom.GetAdditionalPriceParam;
import com.tyt.cargo.dispatch.web.bean.invoiceconfig.InvoiceTransportConfigLogDTO;
import com.tyt.cargo.dispatch.web.bean.price.CalculatePriceBean;
import com.tyt.cargo.dispatch.web.bean.tecServiceFee.TytFreeTecServiceFeeLog;
import com.tyt.cargo.dispatch.web.bean.tecServiceFee.TytTecServiceFeeConfigToComputResult;
import com.tyt.cargo.dispatch.web.commons.constant.RedisConstant;
import com.tyt.cargo.dispatch.web.commons.property.RocketMqProperty;
import com.tyt.cargo.dispatch.web.enums.DrivingAbilityEnum;
import com.tyt.cargo.dispatch.web.enums.ExcellentEnum;
import com.tyt.cargo.dispatch.web.enums.ExcellentGoodsTwoEnum;
import com.tyt.cargo.dispatch.web.enums.SignPartnerEnum;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.*;
import com.tyt.cargo.dispatch.web.mybatis.mapper.base.*;
import com.tyt.cargo.dispatch.web.service.abtest.AbtestService;
import com.tyt.cargo.dispatch.web.service.abtest.AbtestServiceImpl;
import com.tyt.cargo.dispatch.web.service.abtest.TytAbtestConfigVo;
import com.tyt.cargo.dispatch.web.service.base.*;
import com.tyt.cargo.dispatch.web.service.base.impl.DispatchBaseServiceImpl;
import com.tyt.cargo.dispatch.web.service.mq.BaseMqMessageService;
import com.tyt.cargo.dispatch.web.service.mq.MessageCenterPushService;
import com.tyt.cargo.dispatch.web.service.plat.PlatHttpService;
import com.tyt.cargo.dispatch.web.service.remote.CarryPriceService;
import com.tyt.cargo.dispatch.web.service.tecServiceFee.TecServiceFeeConfigService;
import com.tyt.cargo.dispatch.web.service.transport.*;
import com.tyt.cargo.dispatch.web.service.tyt.TytSequenceService;
import com.tyt.cargo.dispatch.web.service.user.UserCreditInfoService;
import com.tyt.cargo.dispatch.web.service.user.UserService;
import com.tyt.cargo.dispatch.web.util.EntityUtil;
import com.tyt.cargo.dispatch.web.util.GoodsUnique;
import com.tyt.cargo.dispatch.web.util.IKAnalyzerUtils;
import com.tyt.cargo.redis.service.tyt.TytConfigService;
import com.tyt.cargo.redis.util.RedisCacheUtil;
import com.tyt.cargo.web.model.plat.TytResultMsgBean;
import com.tyt.messagecenter.core.enums.NativePageEnum;
import com.tyt.messagecenter.core.vo.mq.MessagePushBase;
import com.tyt.messagecenter.core.vo.mq.NewsMessagePush;
import com.tyt.messagecenter.core.vo.mq.NotifyMessagePush;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.COMMA;
import static com.tyt.cargo.dispatch.client.enums.BackoutReasonEnum.change_goods_type;
import static com.tyt.cargo.dispatch.client.enums.BackoutReasonEnum.refresh;
import static com.tyt.cargo.dispatch.web.commons.constant.RedisConstant.Transport.EXCELLENT_GOODS_PUBLISH_TIME;

@Slf4j
@Service
public class TransportMainServiceImpl extends DispatchBaseServiceImpl implements TransportMainService {

    private static final String EXCLUSIVE_REG = "(?<!\\d)17\\.5米(?!吨)|(?<!\\d)17米5(?!\\d)(?!吨)|(?<!\\d)17\\.5(?!\\d)(?!米吨)";
    private static final String LB = "老板";

    public static final int INVOICE_TRANSPORT = 1;

    @Autowired
    private RedisCacheUtil redisCacheUtil;

    @Autowired
    private SpecialCarOpLogService specialCarOpLogService;

    @Autowired
    private TytCustomFirstOrderRecordMapper tytCustomFirstOrderRecordMapper;
    @Autowired
    private TytTransportMainMapper tytTransportMainMapper;

    @Autowired
    private TytTransportMainExtendMapper tytTransportMainExtendMapper;
    @Autowired
    private TytTransportExtendMapper tytTransportExtendMapper;

    @Autowired
    private TytUserMapper tytUserMapper;

    @Autowired
    private TytTransportMapper tytTransportMapper;

    @Autowired
    private TytCityService tytCityService;

    @Autowired
    private CsMaintainedCustomMapper csMaintainedCustomMapper;

    @Autowired
    private MachineTypeBrandService machineTypeBrandService;

    @Autowired
    private CarryPriceService carryPriceService;

    @Autowired
    private TytTransportDispatchMapper tytTransportDispatchMapper;

    @Autowired
    private TytSourceService tytSourceService;

    @Autowired
    private NullifyKeywordService nullifyKeywordService;

    @Autowired
    private TytTransportAutoNullifyMapper tytTransportAutoNullifyMapper;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private TytSequenceService tytSequenceService;

    @Autowired
    private UserCreditInfoService userCreditInfoService;

    @Autowired
    private TytMapDicService tytMapDicService;

    @Autowired
    private TransportDispatchService transportDispatchService;

    @Autowired
    private RocketMqProperty rocketMqProperty;

    @Autowired
    private BaseMqMessageService baseMqMessageService;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private TytUserSubMapper tytUserSubMapper;

    @Autowired
    private ReqPlatService reqPlatService;

    @Autowired
    private TytOwnerAuthMapper tytOwnerAuthMapper;

    @Autowired
    private GiveGoodsUserService giveGoodsUserService;
    @Autowired
    private TytTransportBackendMapper tytTransportBackendMapper;
    @Autowired
    private OwnerCompanyLogMapper ownerCompanyLogMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private AbtestService abtestService;
    @Autowired
    private TransportYMMService transportYMMService;

    @Autowired
    private TytDispatchCargoOwnerMapper tytDispatchCargoOwnerMapper;

    @Autowired
    private TytDispatchCooperativeMapper tytDispatchCooperativeMapper;

    @Autowired
    private TytSpecialCarDispatchDetailMapper tytSpecialCarDispatchDetailMapper;

    @Autowired
    private TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    @Autowired
    private GoodsAddressLevelRecordMapper goodsAddressLevelRecordMapper;

    @Autowired
    private TytSpecialCarPriceConfigMapper tytSpecialCarPriceConfigMapper;

    /**
     * 专车货主管理-平台
     */
    public static final String PLAT_CARGO_OWNER_NAME = "平台";

    @Autowired
    private TytTransportYMMMapper tytTransportYMMMapper;
    @Autowired
    private TytTransportMbMergeMapper tytTransportMbMergeMapper;

    @Autowired
    private TytInternalEmployeeMapper tytInternalEmployeeMapper;

    @Autowired
    private PlatHttpService platHttpService;

    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Autowired
    private UserService userService;

    @Autowired
    private TecServiceFeeConfigService tecServiceFeeConfigService;

    @Autowired
    private GoodsAddressLevelRecordService goodsAddressLevelRecordService;

    @Autowired
    private TytTransportExtendMapper transportExtendMapper;
    @Autowired
    private InvoiceTransportConfigService invoiceTransportConfigService;
    @Autowired
    private TytPublicResourceService tytPublicResourceService;
    @Autowired
    private TytKeywordMatchesNewUnstandardMapper tytKeywordMatchesNewUnstandardMapper;
    @Autowired
    private IKAnalyzerUtils ikAnalyzerUtils;

    @Resource(name = "systemFixedExecutor")
    private ExecutorService systemFixedExecutor;

    /**
     * YMM货源直接上平台一口价开关 0：关；1：开，默认开
     */
    public static final String YMM_TRANSPORT_DIRECT_TYT_FIXED_PRICE_SWITCH = "ymm_transport_direct_tyt_fixed_price_switch";

    /**
     * YMM货源直接上平台一口价根据货源ID进行AB区分开关 0：关；1：开，默认开
     */
    public static final String YMM_TRANSPORT_DIRECT_TYT_FIXED_PRICE_AB_SWITCH = "ymm_transport_direct_tyt_fixed_price_ab_switch";


    @Override
    public WebResult returnLockStatus() {
        return WebResult.failResponse(ResponseEnum.request_error.info("该货源正在操作，请稍后再试！"));
    }

    /**
     * 普通货源redis锁
     *
     * @param tsId
     * @return
     */
    private String createTransportOptKey(Long tsId) {
        String redisKey = com.tyt.messagecenter.core.utils.CommonUtil.joinRedisKey(RedisConstant.Transport.transport_opt_lock, tsId + "");

        return redisKey;
    }

    /**
     * 后台货源redis锁
     *
     * @param backendId
     * @return
     */
    private String createBackendTransportKey(Long backendId) {
        String redisKey = com.tyt.messagecenter.core.utils.CommonUtil.joinRedisKey(RedisConstant.Transport.backend_transport_lock, backendId + "");

        return redisKey;
    }

    @Override
    public boolean lockTransportOpt(Long srcMsgId) {
        if (srcMsgId == null) {
            return true;
        }

        String redisKey = this.createTransportOptKey(srcMsgId);

        String lockValue = System.currentTimeMillis() + "";
        Boolean nxResult = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, lockValue, 5, TimeUnit.SECONDS);

        boolean lockResult = false;
        if (nxResult != null) {
            lockResult = nxResult;
        }
        return lockResult;
    }

    @Override
    public void unlockTransportOpt(Long tsId) {
        String redisKey = this.createTransportOptKey(tsId);
        stringRedisTemplate.delete(redisKey);
    }

    @Override
    public boolean lockBackendTransport(Long backendId) {
        if (backendId == null) {
            return true;
        }

        String redisKey = this.createBackendTransportKey(backendId);

        String lockValue = System.currentTimeMillis() + "";
        Boolean nxResult = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, lockValue, 5, TimeUnit.SECONDS);

        boolean lockResult = false;
        if (nxResult != null) {
            lockResult = nxResult;
        }
        return lockResult;
    }


    /**
     * 校验是否授权
     *
     * @return
     */
    private boolean checkDispatchAuth(Long ownerUserId) {
        boolean authResult = false;

        Example exa = new Example(CsMaintainedCustom.class);
        exa.and().andEqualTo("customId", ownerUserId);

        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomMapper.selectOneByExample(exa);

        if (csMaintainedCustom != null && EmpowerStatusEnum.authed.equalsCode(csMaintainedCustom.getEmpowerStatus().intValue())) {
            authResult = true;
        }
        return authResult;
    }

    private void completionStartAndDestParameter(TransportPublishReq publishBean) {
        log.info("开始匹配出发地++++++++++++++++++++++++++++++++++++++++++++++");
        //出发地参数补充总
        TytCity startCity = tytCityService.getMatchCity(null, publishBean.getStartProvinc(), publishBean.getStartCity(),
                publishBean.getStartArea(), publishBean.getStartCoordX(), publishBean.getStartCoordY(), publishBean.getOwnerUserId());

        if (startCity != null) {
            String pointName = tytCityService.joinCityPoint(startCity.getProvinceName(), startCity.getCityName(), startCity.getAreaName());

            publishBean.setStartCoordX(new BigDecimal(startCity.getPx()));
            publishBean.setStartCoordY(new BigDecimal(startCity.getPy()));
            publishBean.setStartProvinc(startCity.getProvinceName());
            publishBean.setStartCity(startCity.getCityName());
            publishBean.setStartArea(startCity.getAreaName());
            publishBean.setStartPoint(pointName);
            if (StringUtils.isBlank(publishBean.getStartDetailAdd())) {
                boolean llCheck = tytCityService.checkLogitudeLatitude(publishBean.getStartLongitude(), publishBean.getStartLatitude(),
                        new BigDecimal(startCity.getLongitude()), new BigDecimal(startCity.getLatitude()));
                if (!llCheck) {
                    log.info("city_ll_not_match:  用户id:{}； 出发地：{}",
                            publishBean.getOwnerUserId(), publishBean.getStartProvinc() + publishBean.getStartCity() + publishBean.getStartArea());
                    publishBean.setStartLongitude(new BigDecimal(startCity.getLongitude()));
                    publishBean.setStartLatitude(new BigDecimal(startCity.getLatitude()));
                }
            }
            log.info("消息发布出发地的补全参数，省市区县:{}, x轴:{}：", "区县" + publishBean.getStartArea() + "，省" + publishBean.getStartProvinc() + "，市" + publishBean.getStartCity(), "x:" + publishBean.getStartCoordX() + "y:" + publishBean.getStartCoordY());
        }

        log.info("开始匹配目的地++++++++++++++++++++++++++++++++++++++++++++++");
        //目的参数补充
        TytCity destCity = tytCityService.getMatchCity(null, publishBean.getDestProvinc(), publishBean.getDestCity(),
                publishBean.getDestArea(), publishBean.getDestCoordX(), publishBean.getDestCoordY(), publishBean.getOwnerUserId());

        if (destCity != null) {
            String pointName = tytCityService.joinCityPoint(destCity.getProvinceName(), destCity.getCityName(), destCity.getAreaName());

            publishBean.setDestCoordX(new BigDecimal(destCity.getPx()));
            publishBean.setDestCoordY(new BigDecimal(destCity.getPy()));
            publishBean.setDestProvinc(destCity.getProvinceName());
            publishBean.setDestCity(destCity.getCityName());
            publishBean.setDestArea(destCity.getAreaName());
            publishBean.setDestPoint(pointName);
            if (StringUtils.isBlank(publishBean.getDestDetailAdd())) {
                boolean llCheck = tytCityService.checkLogitudeLatitude(publishBean.getDestLongitude(), publishBean.getDestLatitude(),
                        new BigDecimal(destCity.getLongitude()), new BigDecimal(destCity.getLatitude()));

                if (!llCheck) {
                    publishBean.setDestLongitude(new BigDecimal(destCity.getLongitude()));
                    publishBean.setDestLatitude(new BigDecimal(destCity.getLatitude()));
                }
            }
            log.info("消息发布到达地址的补全参数，省市区县:{}, x轴:{}：", "区县" + publishBean.getDestArea() + "，省" + publishBean.getDestProvinc() + "，市" + publishBean.getDestCity(), "x:" + publishBean.getDestCoordX() + "y:" + publishBean.getDestCoordY());
        }

    }

    /**
     * 发布信息参数校验/过滤校验.
     *
     * @param publishBean 发布参数
     */
    private void validationTransportPublishBean(TransportPublishReq publishBean) {

        if (StringUtils.isNotEmpty(publishBean.getWeight()) && !StringBaseUtils.isDouble(publishBean.getWeight())) {
            super.throwException(ResponseEnum.request_error.info("货物重量格式错误，请输入数字"));
        }

        //出发地和目的地以及坐标轴参数修补
        this.completionStartAndDestParameter(publishBean);

        BigDecimal infoFee = publishBean.getInfoFee();
        BigDecimal tecServiceFee = publishBean.getTecServiceFee();
        if (tecServiceFee == null) {
            publishBean.setTecServiceFee(new BigDecimal("0"));
            tecServiceFee = new BigDecimal("0");
        }
        if (infoFee == null) {
            publishBean.setInfoFee(new BigDecimal("0"));
            infoFee = new BigDecimal("0");
        }

        TytTransportMain tytTransportMain = null;
        if (publishBean.getSrcMsgId() != null) {
            tytTransportMain = tytTransportMainMapper.selectByPrimaryKey(publishBean.getSrcMsgId());
        }

        if ((tytTransportMain == null || tytTransportMain.getInvoiceTransport() == null || tytTransportMain.getInvoiceTransport() != 1)
                && new BigDecimal("0").compareTo(infoFee) == 0
                && new BigDecimal("0").compareTo(tecServiceFee) == 0) {
            super.throwException(ResponseEnum.request_error.info("订金和技术服务费不能同时为0！"));
        }

        if (publishBean.getStartCoordX() == null || publishBean.getStartCoordY() == null
                || publishBean.getStartCoordX().compareTo(BigDecimal.ZERO) == 0
                || publishBean.getStartCoordY().compareTo(BigDecimal.ZERO) == 0) {
            super.throwException(ResponseEnum.request_error.info("出发地不正确，请更换"));
        }

        /*if (publishBean.getDestCoordX() == null || publishBean.getDestCoordY() == null
                || publishBean.getDestCoordX().compareTo(BigDecimal.ZERO) == 0
                || publishBean.getDestCoordY().compareTo(BigDecimal.ZERO) == 0) {
            super.throwException(ResponseEnum.request_error.info("目的地不正确，请更换"));
        }*/

        if (StringUtils.isBlank(publishBean.getStartProvinc())) {
            super.throwException(ResponseEnum.request_error.info("出发地参数不能为空"));
        }
        if (StringUtils.isBlank(publishBean.getDestProvinc())) {
            super.throwException(ResponseEnum.request_error.info("目的地参数不能为空"));
        }
        if (publishBean.getStartCoordX() == null) {
            super.throwException(ResponseEnum.request_error.info("startCoordX" + "参数不能为空"));
        }
        if (publishBean.getStartCoordY() == null) {
            super.throwException(ResponseEnum.request_error.info("startCoordY" + "参数不能为空"));
        }

        if (publishBean.getDestCoordX() == null) {
            super.throwException(ResponseEnum.request_error.info("destCoordX" + "参数不能为空"));
        }
        if (publishBean.getDestCoordY() == null) {
            super.throwException(ResponseEnum.request_error.info("destCoordY" + "参数不能为空"));
        }

      /*  String timeErrorMsg = "装卸货时间已过期请重新选择";
        if (publishBean.getLoadingTime() != null && new Date().compareTo(publishBean.getLoadingTime()) > 0) {
            super.throwException(ResponseEnum.request_error.info(timeErrorMsg));
        }*/
        /**
         *  临时修复 自动接单限制
         */
       /* if (publishBean.getUnloadTime() != null && new Date().compareTo(publishBean.getUnloadTime()) > 0) {
            super.throwException(ResponseEnum.request_error.info(timeErrorMsg));
        }*/

        String taskContent = publishBean.getTaskContent();
        if (StringUtils.isBlank(taskContent)) {
            super.throwException(ResponseEnum.request_error.info("货物内容不能为空"));
        }
        taskContent = EmojiParser.removeAllEmojis(taskContent);
        log.info("taskContent=" + publishBean.getTaskContent() + " EmotaskContent=" + taskContent);

        if (StringUtils.isBlank(taskContent)) {
            super.throwException(ResponseEnum.request_error.info("货物内容不能有非法字符！"));
        }
        publishBean.setTaskContent(taskContent);

        if (StringUtils.isNotEmpty(publishBean.getCarType())) {
            publishBean.setCarType(EmojiParser.removeAllEmojis(publishBean.getCarType()));
            log.info("carType:" + publishBean.getCarType() + "-----------------------");
        }

        if (StringUtils.isNotEmpty(publishBean.getCarStyle())) {
            publishBean.setCarStyle(EmojiParser.removeAllEmojis(publishBean.getCarStyle()));
            log.info("carStyle:" + publishBean.getCarStyle() + "-----------------------");
        }

        if (publishBean.getStartLongitude() == null || publishBean.getStartLongitude().compareTo(BigDecimal.ZERO) < 1) {
            super.throwException(ResponseEnum.request_error.info("startLongitude" + "参数不能小于0"));
        }
        if (publishBean.getStartLatitude() == null || publishBean.getStartLatitude().compareTo(BigDecimal.ZERO) < 1) {
            super.throwException(ResponseEnum.request_error.info("startLatitude" + "参数不能小于0"));
        }
        if (publishBean.getDestLongitude() == null || publishBean.getDestLongitude().compareTo(BigDecimal.ZERO) < 1) {
            super.throwException(ResponseEnum.request_error.info("destLongitude" + "参数不能小于0"));
        }
        if (publishBean.getDestLatitude() == null || publishBean.getDestLatitude().compareTo(BigDecimal.ZERO) < 1) {
            super.throwException(ResponseEnum.request_error.info("destLatitude" + "参数不能小于0"));
        }
        if (StringUtils.isBlank(publishBean.getTel()) || !StringBaseUtils.isMobileOrFixedPhone(publishBean.getTel())) {
            super.throwException(ResponseEnum.request_error.info("电话格式有误或者不能为空"));
        }
        if (StringUtils.isNotEmpty(publishBean.getTel3()) && !StringBaseUtils.isMobileOrFixedPhone(publishBean.getTel3())) {
            super.throwException(ResponseEnum.request_error.info("电话格式有误"));
        }
        if (StringUtils.isNotEmpty(publishBean.getTel4()) && !StringBaseUtils.isMobileOrFixedPhone(publishBean.getTel4())) {
            super.throwException(ResponseEnum.request_error.info("电话格式有误"));
        }
        if (StringUtils.isNotEmpty(publishBean.getWide()) && !StringBaseUtils.isDouble(publishBean.getWide())) {
            super.throwException(ResponseEnum.request_error.info("货物宽度参数格式有误"));
        }
        if (StringUtils.isNotEmpty(publishBean.getLength()) && !StringBaseUtils.isDouble(publishBean.getLength())) {
            super.throwException(ResponseEnum.request_error.info("货物长度参数格式有误"));
        }
        if (StringUtils.isNotEmpty(publishBean.getHigh()) && !StringBaseUtils.isDouble(publishBean.getHigh())) {
            super.throwException(ResponseEnum.request_error.info("货物高度参数格式有误"));
        }
        if (StringUtils.isNotEmpty(publishBean.getPrice()) && !StringBaseUtils.isDouble(publishBean.getPrice())) {
            super.throwException(ResponseEnum.request_error.info("价格参数格式有误，请输入数字"));
        }

        String climb = publishBean.getClimb();
        if (StringUtils.isNotBlank(climb) && !NumberUtils.isDigits(climb)) {
            super.throwException(ResponseEnum.request_error.info("是否需要带爬梯格式有误！"));
        }
        String tyreExposedFlag = publishBean.getTyreExposedFlag();
        if (StringUtils.isNotBlank(tyreExposedFlag) && !NumberUtils.isDigits(tyreExposedFlag)) {
            super.throwException(ResponseEnum.request_error.info("轮胎外露标识格式有误！"));
        }
        String temp = publishBean.getDestDetailAdd();
        if (StringUtils.isNotBlank(temp)) {
            publishBean.setDestDetailAdd(EmojiParser.removeAllEmojis(temp));

        }
        temp = publishBean.getStartDetailAdd();
        if (StringUtils.isNotBlank(temp)) {

            publishBean.setStartDetailAdd(EmojiParser.removeAllEmojis(temp));
        }
        temp = publishBean.getRemark();
        if (StringUtils.isNotBlank(temp)) {
            publishBean.setRemark(EmojiParser.removeAllEmojis(temp));

        }
        Integer refundFlag = publishBean.getRefundFlag();
        if (Objects.isNull(refundFlag)) {
            //默认0 不退还
            publishBean.setRefundFlag(0);
        }

        if (publishBean.getShuntingQuantity() == null) {
            super.throwException(ResponseEnum.request_error.info("调车数量不能为空！"));
        }

        //一口价货源校验
        this.checkPublishTypeParams(publishBean);

        //2. 校验货物名称
        this.validationTaskContent(publishBean);

        String giveGoodsPhone = publishBean.getGiveGoodsPhone();

        if (StringUtils.isNotBlank(giveGoodsPhone)) {
            if (!CommonUtil.isPhoneNumber(giveGoodsPhone)) {
                super.throwException(ResponseEnum.request_error.info("给货货主手机号格式错误！"));
            }
        }

        // 专车发货参数校验
        if (ExcellentEnum.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
            checkForSpecialCar(publishBean);
        }
    }

    /**
     * 专车发货参数校验
     *
     * @param publishBean
     */
    private void checkForSpecialCar(TransportPublishReq publishBean) {
        // 指派司机账号不能与发货APP账号一致
        if (StringUtils.isNotEmpty(publishBean.getPhone()) && StringUtils.isNotEmpty(publishBean.getCarTelPhone()) &&
                publishBean.getPhone().equals(publishBean.getCarTelPhone())) {
            super.throwException(ResponseEnum.request_error.info("指派司机账号不能与发货App账号一致！"));
        }
        // 校验发货账号是否在专车货主管理中，授权状态为已授权
        List<TytDispatchCargoOwner> list = tytDispatchCargoOwnerMapper.selectAuthorizedRecords(publishBean.getOwnerUserId());
        if (CollectionUtils.isEmpty(list)) {
            throwException(ResponseEnum.request_error.info("发货APP账号未授权专车发货，请先授权后再发货！"));
        }
        // 匹配签约合作商
        TytDispatchCargoOwner owner = list.get(0);
        if (SignPartnerEnum.YES.getCode().equals(owner.getSignPartner())) {
            publishBean.setCargoOwnerId(owner.getCooperativeId());
        }
    }

    /**
     * 判断当前货源是否是 17.5 米专享货源
     *
     * @param publishBean
     * @return
     */
    public boolean isSeventeenFive(TransportPublishReq publishBean) {
        List<BigDecimal> decList = new ArrayList<>();
        decList.add(publishBean.getCarMinLength());//最小长度
        decList.add(publishBean.getCarMaxLength());//最大长度

        decList.add(publishBean.getWorkPlaneMinLength());//最小工作面长
        decList.add(publishBean.getWorkPlaneMaxLength());//最大工作面长

        decList.add(publishBean.getWorkPlaneMinHigh());//最小工作面高
        decList.add(publishBean.getWorkPlaneMaxHigh());//最大工作面高

        BigDecimal lengthDec = new BigDecimal("17.5");
        for (BigDecimal oneDec : decList) {
            if (oneDec != null && oneDec.compareTo(lengthDec) == 0) {
                log.info("dec_transoprt_17.5米专享： " + oneDec);
                return true;
            }
        }

        List<String> strList = new ArrayList<>();
        strList.add(publishBean.getTaskContent());//名称
        strList.add(publishBean.getRemark());//备注
        strList.add(publishBean.getCarLengthLabels());//长度标签

        strList.add(publishBean.getCarStyle());//挂车样式
        strList.add(publishBean.getCarType());//挂车型号

        //不符合的吨结尾
        String regFilterTon = "17\\.5米?吨";

        //前或后多数字
        String regFilterNum = "(\\d+17\\.5\\d*)|(\\d*17\\.5\\d+)";

        String regRight = "17\\.5米?";

        Pattern compile = Pattern.compile(EXCLUSIVE_REG);
        String lengthTxt = "17.5";
        for (String oneText : strList) {
            if (StringUtils.isNotBlank(oneText)) {
                //包含17.5米、17.5（不包含17.5吨）、17米5
                //排除：17米5吨，17.5米吨，及“以上”，及前后数字

                //替换排除为吨（以便只处理排除吨的情况）
                oneText = oneText.replace("以上", "吨");
                //替换17米5 为  17.5
                oneText = oneText.replace("17米5", "17.5");

                //移除前后多数字的
                oneText = oneText.replaceAll(regFilterNum, "@");
                //移除不符合的“吨”结尾的
                oneText = oneText.replaceAll(regFilterTon, "#");

                //判断17.5米是否存在
                boolean exist = CommonUtil.regFind(regRight, oneText);

                if (exist) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 校验是否有屏蔽词
     *
     * @param text
     * @param codeEnumArray
     * @return
     */
    public String checkTransportWord(String text, SourceGroupCodeEnum... codeEnumArray) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        if (ArrayUtils.isNotEmpty(codeEnumArray)) {
            for (SourceGroupCodeEnum oneCodeEnum : codeEnumArray) {
                List<TytSource> groupSourceList = tytSourceService.getGroupSourceList(oneCodeEnum.getGroupCode());

                if (CollectionUtils.isNotEmpty(groupSourceList)) {
                    for (TytSource tytSource : groupSourceList) {
                        String labelName = tytSource.getName();
                        text = text.replaceAll(labelName, "");
                    }
                }
            }
        }

        TytMachineTypeBrandNew machineTypeBrandNew = machineTypeBrandService.getSingleByName(text);
        if (machineTypeBrandNew != null) {
            return null;
        }

        String contentKeyValue = null;

        if (StringUtils.isNotBlank(text)) {
            Set<String> sensitiveSet = nullifyKeywordService.verifyNullifyContent(text);

            if (CollectionUtils.isNotEmpty(sensitiveSet)) {
                contentKeyValue = StringUtils.join(sensitiveSet, ",");
                log.info("verifyNullifyContent_true: " + contentKeyValue);
            }

        }
        return contentKeyValue;
    }

    /**
     * 保存屏蔽词记录
     *
     * @param publishBean
     * @param sensitiveWord
     */
    private void saveTransportNullify(TransportPublishReq publishBean, String sensitiveWord) {
        TytTransportAutoNullify tnb = new TytTransportAutoNullify();
        tnb.setTransId(0L);
        tnb.setUserId(publishBean.getOwnerUserId());
        tnb.setTaskContent(publishBean.getTaskContent());
        tnb.setTaskRemark(publishBean.getRemark());
        tnb.setMatchingKeyword(sensitiveWord);
        tnb.setUploadCellphone(publishBean.getTel());
        tnb.setPlatId((short) DispatchConstant.mock_client_sign);
        tnb.setStartPoint(publishBean.getStartPoint());
        tnb.setDestPoint(publishBean.getDestPoint());
        tnb.setStartProvinc(publishBean.getStartProvinc());
        tnb.setStartCity(publishBean.getStartCity());
        tnb.setStartArea(publishBean.getStartArea());
        tnb.setDestProvinc(publishBean.getDestProvinc());
        tnb.setDestCity(publishBean.getDestCity());
        tnb.setDestArea(publishBean.getDestArea());
        tnb.setIsInfoFee((short) 1);
        tnb.setCtime(new Date());
        tnb.setMtime(new Date());
        tnb.setState((short) -1);

        tytTransportAutoNullifyMapper.insertSelective(tnb);
    }

    /**
     * 验证货物内容是否包含散货关键词
     *
     * @param publishReq
     * @return
     */
    private void validationTaskContent(TransportPublishReq publishReq) {

        String contentKeyValue = this.checkTransportWord(publishReq.getTaskContent());

        if (StringUtils.isNotBlank(contentKeyValue)) {
            this.saveTransportNullify(publishReq, contentKeyValue);
            super.throwException(DispatchResponseEnum.name_sensitive_error.info());
        }

        String remark = publishReq.getRemark();

        contentKeyValue = this.checkTransportWord(remark, SourceGroupCodeEnum.remark);

        if (StringUtils.isNotBlank(contentKeyValue)) {
            this.saveTransportNullify(publishReq, contentKeyValue);
            super.throwException(DispatchResponseEnum.remark_sensitive_error.info());
        }

    }

    /**
     * 一口价货源相关校验
     *
     * @param transportReq
     */
    private void checkPublishTypeParams(TransportPublishReq transportReq) {
        Integer pricePublishType = transportReq.getPublishType();
        if (pricePublishType != null && pricePublishType.equals(PublishTypeEnum.fixed.getCode())) {
            Integer shuntingQuantity = transportReq.getShuntingQuantity();
            if (shuntingQuantity > 1) {
                super.throwException(ResponseEnum.request_error.info("一口价货源调车数量不可大于1"));
            }

            String price = transportReq.getPrice();
            if (StringUtils.isBlank(price)) {
                super.throwException(ResponseEnum.request_error.info("一口价货源信息费和运费不能为空"));
            }
        }

    }

    /**
     * 获取用户显示名称
     *
     * @param tytUser
     * @return
     */
    private String getUserShowName(TytUser tytUser) {
        String trueName = tytUser.getTrueName();
        String idCard = tytUser.getIdCard();
        String showName = null;
        if (StringUtils.isNotBlank(trueName) && StringUtils.isNotBlank(idCard)) {
            showName = trueName.substring(0, 1) + IdcardUtils.getCallGender(idCard);
        } else {
            showName = tytUser.getUserName();
        }
        return showName;
    }


    /**
     * 获得货物的hashcode(新规则),标准货源的判重标准：用户id+出发地+目的地+标准货物编号（只针对发布中的货源进行判重）
     * 非标准货源的判重标准：用户id+出发地+目的地+货物内容（只针对发布中的货源进行判重， 暂时不考虑相似度问题）
     *
     * @param transport
     * @return
     */
    private String getNewHashCode(TytTransport transport) {
        String code = "";

        code = transport.getUserId() + transport.getStartPoint() + transport.getDestPoint() +
                StringBaseUtils.filterBlank(StringBaseUtils.filterPunctuation(StringBaseUtils.filterEmotion(transport.getTaskContent())));
        return code.hashCode() + "";
    }

    private TytTransport createTransport(TransportPublishReq publishBean, TytUser tytUser) {
        TytTransport transport = new TytTransport();
        transport.setIsDelete((short) 0);


        transport.setUserShowName(this.getUserShowName(tytUser));

        /**
         *  优车新增类型 0：不是优车 1: 是优车
         */
        transport.setExcellentGoods(publishBean.getExcellentGoods());

        transport.setUserId(publishBean.getOwnerUserId());
        transport.setPlatId((short) DispatchConstant.mock_client_sign);
        transport.setStartCoord(publishBean.getStartCoordX() + "," + publishBean.getStartCoordY());
        transport.setDestCoord(publishBean.getDestCoordX() + "," + publishBean.getDestCoordY());
        transport.setDestPoint(publishBean.getDestPoint());
        transport.setStartPoint(publishBean.getStartPoint());
        String taskContent = publishBean.getTaskContent();
        transport.setTaskContent(StringBaseUtils.clearSquareNumbers(taskContent));
        transport.setTel(publishBean.getTel());
        transport.setTel3(StringUtils.isEmpty(publishBean.getTel3()) ? null : publishBean.getTel3());
        transport.setTel4(StringUtils.isEmpty(publishBean.getTel4()) ? null : publishBean.getTel4());
        transport.setPrice(publishBean.getPrice());
        transport.setStartLatitude(publishBean.getStartLatitude().movePointRight(6).intValue());
        transport.setStartLongitude(publishBean.getStartLongitude().movePointRight(6).intValue());
        transport.setDestLatitude(publishBean.getDestLatitude().movePointRight(6).intValue());
        transport.setDestLongitude(publishBean.getDestLongitude().movePointRight(6).intValue());
        transport.setStartCoordX(publishBean.getStartCoordX().movePointRight(2).intValue());
        transport.setStartCoordY(publishBean.getStartCoordY().movePointRight(2).intValue());
        transport.setDestCoordX(publishBean.getDestCoordX().movePointRight(2).intValue());
        transport.setDestCoordY(publishBean.getDestCoordY().movePointRight(2).intValue());
        transport.setStartArea(publishBean.getStartArea());
        transport.setStartCity(publishBean.getStartCity());
        transport.setStartProvinc(publishBean.getStartProvinc());
        transport.setDestArea(publishBean.getDestArea());
        transport.setDestCity(publishBean.getDestCity());
        transport.setDestProvinc(publishBean.getDestProvinc());
        transport.setDestProvinc(publishBean.getDestProvinc());
        //车货拆分新增参数---------------------------------
        transport.setBeginLoadingTime(publishBean.getBeginLoadingTime()); //新增字段
        transport.setBeginUnloadTime(publishBean.getBeginUnloadTime()); //新增字段
        transport.setLoadingTime(publishBean.getLoadingTime());
        transport.setUnloadTime(publishBean.getUnloadTime());
        transport.setCarMinLength(publishBean.getCarMinLength());
        transport.setCarMaxLength(publishBean.getCarMaxLength());
        transport.setWorkPlaneMinHigh(publishBean.getWorkPlaneMinHigh());
        transport.setWorkPlaneMaxHigh(publishBean.getWorkPlaneMaxHigh());
        transport.setWorkPlaneMinLength(publishBean.getWorkPlaneMinLength());
        transport.setWorkPlaneMaxLength(publishBean.getWorkPlaneMaxLength());
        transport.setClimb(publishBean.getClimb());
        transport.setCarStyle(publishBean.getCarStyle());

        //如果是指派货源-宏信，则给货源打标
        String hongXinTransportOwner = tytConfigService.getStringValue("hong_xin_transport_owner", "");
        if (StringUtils.isNotBlank(hongXinTransportOwner) && hongXinTransportOwner.contains(publishBean.getGiveGoodsPhone().toString())) {
            //是宏信货主发货
            if (publishBean.getCarUserid() != null) {
                Integer count = tytTransportMainMapper.getSigningCarIsHongXinJianFaByCarUserPhone(publishBean.getCarTelPhone());
                if (count != null && count != 0) {
                    //指派司机为协议司机并且合作商为宏信建发
                    publishBean.setSourceType(5);
                }
            }
        }

        // 判断是否是 17.5 米专享
        boolean isSeventeenFive = this.isSeventeenFive(publishBean);
        transport.setExclusiveType(isSeventeenFive ? 1 : 0);

        //企业货源屏蔽大厅，是否显示在找货大厅（企业货源）0.不显示 1.显示
        if (StringUtils.isNotBlank(publishBean.getCarTelPhone())) {
            //如果指派了司机，则找货大厅中不显示
            transport.setIsShow(0);
        } else {
            transport.setIsShow(1);
        }

        if (publishBean.getDistance() != null) {
            transport.setDistance(publishBean.getDistance().movePointRight(2).intValue());
        }

        String taskContentTemp = StringBaseUtils.hidePhoneNumber(taskContent);
        transport.setTaskContent(taskContentTemp.replaceAll("'", "-").replaceAll("\"", "--"));
        transport.setSource((short) 0);

        if (tytUser != null && tytUser.getVerifyFlag() != null && tytUser.getVerifyFlag().intValue() != 2) {
            transport.setVerifyFlag(tytUser.getVerifyFlag());
        } else {
            transport.setVerifyFlag((short) 0);
        }

        Date nowTime = new Date();

        transport.setPubTime(DateUtil.dateToString(nowTime, DateUtil.hour_format));
        transport.setPubDate(nowTime);
        transport.setCtime(nowTime);
        transport.setMtime(nowTime);

        if (StringUtils.isNotEmpty(publishBean.getStartDetailAdd())) {
            transport.setStartDetailAdd(publishBean.getStartDetailAdd());
        }
        if (StringUtils.isNotEmpty(publishBean.getDestDetailAdd())) {
            transport.setDestDetailAdd(publishBean.getDestDetailAdd());
        }

        transport.setIsSuperelevation(publishBean.getIsSuperelevation());
        if (publishBean.getSourceType() != null && (publishBean.getSourceType() == 4 || publishBean.getSourceType() == 5)) {
            //ymm自动发布会调用diapatch发布接口，要保留原SourceType
            transport.setSourceType(publishBean.getSourceType());
        } else {
            transport.setSourceType(SourceTypeEnum.dispatch.getCode());
        }

        if (StringUtils.isNotEmpty(publishBean.getRemark())) {
            //过滤货物内容的电话号码
            String remarkTemp = StringBaseUtils.hidePhoneNumber(publishBean.getRemark());
            transport.setRemark(remarkTemp);
        }
        // 优推需要 如：李老板 格式
        //如果是优车并且该货主不在AB测试中才将昵称赋值为X老板
        if (publishBean.getExcellentGoods() != null && publishBean.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
            // 如果是满满货源，nickname取运满满的名称
            if (transport.getSourceType() == SourceTypeEnum.ymm.getCode()) {
                TransportYMMListVO transportYMM = transportYMMService.getTransportYMM(publishBean.getCargoId());
                if (transportYMM != null) {
                    if (StringUtils.isNotBlank(transportYMM.getContactName())) {
                        transport.setNickName(transportYMM.getContactName().charAt(0) + LB);
                    } else {
                        transport.setNickName("");
                    }
                } else {
                    transport.setNickName("");
                }
            } else {
                youcheMakeNickName(transport, tytUser);
            }
        } else {
            transport.setNickName(StringBaseUtils.formatUserName(tytUser.getUserName(), tytUser.getId()));
        }
        /* 兼容老客户 */
        transport.setLinkman(StringBaseUtils.formatUserName(publishBean.getLinkMan(), tytUser.getId()));

        transport.setPubQq(tytUser.getQq());
        transport.setUploadCellphone(tytUser.getCellPhone());
        transport.setDisplayType("1");// 默认显示
        /* 添加用户信息 */
        transport.setIsCar(tytUser.getIsCar());
        // transport.setUserType(user.getUserType());
        short vipType = 0;

        if (userPermissionService.isVipPublishPermission(tytUser.getId())) {
            vipType = 1;
        }
        transport.setUserType(vipType);

        /* 重发周期默认值在tyt_config表配置 */
        Integer serverDefaultResendTime = tytConfigService.getIntValue(RedisConstant.Config.resend_time_key);

        short resend = 20;
        if (serverDefaultResendTime != null) {
            resend = serverDefaultResendTime.shortValue();
        }
        transport.setResend(resend);
        transport.setStatus((short) TransportStatusEnum.active.getCode());

        Short verifyPhotoSign = 0;
        if (tytUser != null && tytUser.getVerifyPhotoSign() != null && tytUser.getVerifyPhotoSign().intValue() == 1) {
            transport.setVerifyPhotoSign(tytUser.getVerifyPhotoSign());
        } else {
            transport.setVerifyPhotoSign((short) 0);
        }

        transport.setUserPart(tytUser.getUserPart());
        transport.setClientVersion(DispatchConstant.mock_client_version + "");
        String isInfoFee = "1";
        transport.setIsInfoFee(isInfoFee);
        transport.setInfoStatus("0");// 信息费运单状态：0待接单 1有人支付成功 （货主的待同意
        // ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成 5异常上报
        transport.setReleaseTime(nowTime);
        /* 已撤销，已过期进行直接发布，编辑再发布中,运单号重新生成 */

        transport.setTsOrderNo(tytSequenceService.generateSequenceForDate(DispatchConstant.TABLE_WAYBILL_NAME));

        transport.setRegTime(tytUser.getCtime());

        transport.setWeight(StringUtils.defaultIfEmpty(publishBean.getWeight(), null));
        transport.setLength(StringUtils.defaultIfEmpty(publishBean.getLength(), null));
        transport.setWide(StringUtils.defaultIfEmpty(publishBean.getWide(), null));
        transport.setHigh(StringUtils.defaultIfEmpty(publishBean.getHigh(), null));
        transport.setType(publishBean.getType());
        transport.setBrand(publishBean.getBrand());
        transport.setGoodTypeName(publishBean.getGoodTypeName());
        //0是，1不是
        byte isStandard = 1;
        if (publishBean.getIsStandard() != null) {
            isStandard = publishBean.getIsStandard().byteValue();
        }
        transport.setIsStandard(isStandard);
        transport.setGoodNumber(publishBean.getMachineNumber());
        Integer matchItemId = -1;
        if (publishBean.getMatchItemId() != null) {
            matchItemId = publishBean.getMatchItemId();
        }
        transport.setMatchItemId(matchItemId);

        this.addReferTransportNewInfo(transport);

        //hasdCode开关，如果 开 走新定义的规则，关 走老规则 1为关闭，2 为开启  全部走新规则
        //Integer hashcodeSwitch = tytConfigService.getIntValue("transportGreateHashcodeSwitch", 1);
        transport.setHashCode(getNewHashCode(transport));

        // 5920 新增3个字段
        transport.setCarType(publishBean.getCarType());
        transport.setCarLength(publishBean.getCarLength());
        transport.setSpecialRequired(publishBean.getSpecialRequired());
        //v6110新增货源标签字段
        transport.setCarLengthLabels(publishBean.getCarLengthLabels());
        String tyreExposedFlag = publishBean.getTyreExposedFlag();
        if (StringUtils.isBlank(tyreExposedFlag)) {
            tyreExposedFlag = "0";
        }
        transport.setTyreExposedFlag(tyreExposedFlag);
        //v6120新增调车数量
        transport.setShuntingQuantity(publishBean.getShuntingQuantity());
        //v6140 货源类型（电议1，一口价2） 信息费
        Short publishType = PublishTypeEnum.tel.getCode().shortValue();
        if (publishBean.getPublishType() != null) {
            PublishTypeEnum anEnum = PublishTypeEnum.getEnum(publishBean.getPublishType());
            publishType = anEnum.getCode().shortValue();
        }
        transport.setPublishType(publishType);

        transport.setInfoFee(publishBean.getInfoFee());

        transport.setRefundFlag(publishBean.getRefundFlag());
        transport.setTecServiceFee(publishBean.getTecServiceFee());

        String pcOldContent = getPcOldContent(transport);
        transport.setPcOldContent(pcOldContent);

        // 专车货源相关字段设置
        if (ExcellentEnum.SPECIAL.getCode().equals(publishBean.getExcellentGoods())) {
            transport.setLoadCellPhone(publishBean.getLoadCellPhone());
            transport.setUnloadCellPhone(publishBean.getUnloadCellPhone());
            transport.setDriverDriving(publishBean.getDriverDriving());
            transport.setCargoOwnerId(publishBean.getCargoOwnerId());
        }

        //如果是开票货源则构造企业税率、附加运费和总运费
        if (publishBean.getInvoiceTransport() != null && publishBean.getInvoiceTransport() == INVOICE_TRANSPORT) {
            transport.setInvoiceTransport(1);
            TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.get_Additional_Price_And_Enterprise_Tax_Rate, EntityUtils.entityToMap(new GetAdditionalPriceParam(transport.getUserId().toString(), transport.getPrice(), publishBean.getInvoiceSubjectId())), null);
            if (tytResultMsgBean != null && tytResultMsgBean.isSuccess()) {
                Map<String, String> data = (Map<String, String>) tytResultMsgBean.getData();
                transport.setEnterpriseTaxRate(new BigDecimal(data.get("enterpriseTaxRate")));
                transport.setAdditionalPrice(data.get("additionalPrice"));
            }
        } else {
            transport.setInvoiceTransport(0);
        }

        return transport;
    }

    /**
     * 补充新版标准化货物信息的参考值
     */
    public void addReferTransportNewInfo(TytTransport transport) {
        // 未传时直接当非标货源
        if (transport.getMatchItemId() == null) {
            transport.setIsStandard((byte) 1);
            transport.setMatchItemId(-1);
            return;
        }
        try{
            // 查询新版本标准化
            if (transport.getMatchItemId() >= 0 && transport.getMatchItemId() != 9999999) {
                TytMachineTypeBrandNew machineTypeBrandNew = machineTypeBrandService.getById(transport.getMatchItemId());
                if (machineTypeBrandNew != null) {
                    String brand = StringUtils.defaultIfBlank(machineTypeBrandNew.getBrand(), "");
                    String goodTypeName = StringUtils.defaultIfBlank(machineTypeBrandNew.getSecondClass(), "");
                    String topType = StringUtils.defaultIfBlank(machineTypeBrandNew.getTopType(), "");

                    transport.setBrand(brand);
                    transport.setGoodTypeName(goodTypeName);
                    transport.setType(topType);

                    transport.setReferLength(this.getReferNumber(transport.getLength(), machineTypeBrandNew.getLength()));
                    transport.setReferWidth(this.getReferNumber(transport.getWide(), machineTypeBrandNew.getWidth()));
                    transport.setReferHeight(this.getReferNumber(transport.getHigh(), machineTypeBrandNew.getHeight()));
                    transport.setReferWeight(this.getReferNumber(transport.getWeight(), machineTypeBrandNew.getWeight()));
                    transport.setIsStandard((byte) 0);
                    return;
                }
            }
            // 如果不是标准货源，看看能否从非标货源转化
            List<String> keywords = ikAnalyzerUtils.analyze(transport.getTaskContent());

            if (CollUtil.isNotEmpty(keywords)) {
                List<TytKeywordMatchesNewUnstandard> unstandardList = tytKeywordMatchesNewUnstandardMapper.selectByKeywords(keywords);
                if (CollUtil.isNotEmpty(unstandardList)) {
                    TytKeywordMatchesNewUnstandard unStandardDO = CollUtil.getFirst(unstandardList);
                    // 作为非标转化回来的标识
                    transport.setMatchItemId(9999999);
                    transport.setIsStandard((byte) 0);

                    transport.setBrand(unStandardDO.getBrand());
                    transport.setGoodTypeName(unStandardDO.getSecondClass());
                    transport.setType(unStandardDO.getSecondType());

                    transport.setReferLength(getReferNumber(unStandardDO.getLength(), transport.getLength()));
                    transport.setReferWidth(getReferNumber(unStandardDO.getWidth(), transport.getWide()));
                    transport.setReferHeight(getReferNumber(unStandardDO.getHeight(), transport.getHigh()));
                    transport.setReferWeight(getReferNumber(unStandardDO.getWeight(), transport.getWeight()));
                    return;
                }
            }
//         都不是，则设置为非标货源标识
            transport.setIsStandard((byte) 1);
            transport.setMatchItemId(-1);
        }catch (Exception e){
            log.error("addReferTransportNewInfo error", e);
        }
    }

    private boolean isUseYMMTransportPhone(Long cargoId, String price) {
        Integer fixedPriceSwitch = tytConfigService.getIntValue(YMM_TRANSPORT_DIRECT_TYT_FIXED_PRICE_SWITCH, 1);
        Integer fixedPriceABSwitch = tytConfigService.getIntValue(YMM_TRANSPORT_DIRECT_TYT_FIXED_PRICE_AB_SWITCH, 1);

        if (StringUtils.isNotBlank(price)) {
            //有价
            if (fixedPriceSwitch == 1) {
                if (fixedPriceABSwitch == 1) {
                    if (cargoId % 2 == 1) {
                        //YMM货源直接上平台，如果YMM货源ID为单数，则手机号直接使用YMM方货主手机号，因为
                        return true;
                    }
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验货源是否已存在
     *
     * @param tytTransport
     */
    private void checkHashCodeExist(TytTransport tytTransport) {
        Date nowTime = new Date();

        String dayStr = DateUtil.dateToString(nowTime, DateUtil.day_format);
        String hashCode = tytTransport.getHashCode();

        String hashCodeKey = RedisConstant.Transport.hash_code_key + dayStr + "_" + hashCode;

        Object cacheTs = redisCacheUtil.getObject(hashCodeKey);

        if (cacheTs != null) {
            super.throwException(ResponseEnum.request_error.info("您已经发布过此条信息！"));
        }

    }

    /**
     * 拼接相似货源编码
     *
     * @param tb
     * @param taskContentKey
     * @return
     */
    private String genSimilarityCode(TytTransport tb, String taskContentKey) {
        log.info("similarity_content_key：srcMsgId:{}, taskContent:{}, keyword:{}", tb.getSrcMsgId(), tb.getTaskContent(), taskContentKey);

        String todayDate = DateUtil.dateToString(tb.getCtime(), DateUtil.day_format);
        String startProvinc = tb.getStartProvinc();
        String startCity = tb.getStartCity();
        String startArea = tb.getStartArea();
        String destProvinc = tb.getDestProvinc();
        String destCity = tb.getDestCity();
        String destArea = tb.getDestArea();
        // 重量统一改成保留2位小数
        String weight = StringUtils.isBlank(tb.getWeight()) ? "" :
                new BigDecimal(tb.getWeight()).setScale(2, RoundingMode.HALF_UP).toString();

        String goodTypeName = StringUtils.defaultIfBlank(tb.getGoodTypeName(), "");
        String type = StringUtils.defaultIfBlank(tb.getType(), "");
        String brand = StringUtils.defaultIfBlank(tb.getBrand(), "");

        //规则：今天的日期 + 出发地省 + 出发地市 + 目的地省 + 目的地市 + 标准化ID
//        String similarityConnect = todayDate + startProvinc + startCity + startArea + destProvinc + destCity + destArea + weight + taskContentKey;
        String similarityConnect = todayDate + startProvinc + startCity + startArea + destProvinc + destCity + destArea + weight;
        if (org.apache.commons.lang.StringUtils.isBlank(brand) && org.apache.commons.lang.StringUtils.isBlank(goodTypeName) && org.apache.commons.lang.StringUtils.isBlank(type)) {
            similarityConnect = similarityConnect + StringUtils.substring(tb.getTaskContent(), 0, 10);
        } else {
            similarityConnect = similarityConnect + brand + goodTypeName + type;
        }
        log.info("similarity_connect = " + similarityConnect);
        try {
            // 先判断抽佣货源的开关，走抽佣货源的逻辑
            Integer similaritySwitch = tytConfigService.getIntValue("commission_similarity_switch", 0);
            // 0：折叠  1：不折叠
            if (similaritySwitch == 1) {
                log.info("走抽佣货源逻辑，similarity_connect = " + similarityConnect);
                String labelJson = tb.getLabelJson();
                if (org.apache.commons.lang.StringUtils.isNotBlank(labelJson)) {
                    TransportLabelJson transportLabelJson = JSON.parseObject(labelJson, TransportLabelJson.class);
                    // 抽佣货源
                    if (transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                        // 抽佣货源
                        similarityConnect = similarityConnect + tb.getUserId();
                    } else {
                        similarityConnect = this.modifyQualityGoodSimilarCode(tb, similarityConnect);
                    }
                }
            } else {
                similarityConnect = this.modifyQualityGoodSimilarCode(tb, similarityConnect);
            }
        } catch (com.tyt.service.common.exception.TytException e) {
            // ignore, 不满足abtest条件会抛异常
        }

        //相似code
        String similarityCode = DigestUtils.md5Hex(similarityConnect);
        // 如果管理后台手动修改过similarityCode，则需要拼接后缀
        if (tb.getSrcMsgId() != null && stringRedisTemplate.opsForValue().get("tyt:goods:similarity:change:" + tb.getSrcMsgId()) != null) {
            similarityCode += "_" + (int) (Math.random() * 1000);
        }
        return similarityCode;
    }

    private String modifyQualityGoodSimilarCode(TytTransport tb, String similarityConnect) {
        Integer isFoldQualityTs = abtestService.getUserType("is_fold_quality_ts", tb.getUserId());
        if (Objects.equals(0, isFoldQualityTs) && Objects.equals(1, tb.getExcellentGoods())) {
            // 0 不折叠；1 折叠
            // 不折叠才需要修改 similarityConnect, 折叠不需要修改, 所以 types = {0} 时修改
            return similarityConnect + tb.getUserId();
        } else {
            return similarityConnect;
        }
    }


    /**
     * 生成相似货源code
     *
     * @param tb
     * @return
     */
    @Override
    public String genSimilarityCode(TytTransport tb) {
        String taskContentKey = GoodsUnique.extractKeyInfo(tb.getTaskContent());
        //相似code
        String similarityCode = this.genSimilarityCode(tb, taskContentKey);
        return similarityCode;
    }

    /**
     * 校验个人是否有相似货源
     *
     * @param transport
     */
    public void checkPersonalSimilarity(TytTransport transport) {

        Date dayBegin = DateUtil.startOfDay(transport.getCtime());

        Long srcMsgId = transport.getSrcMsgId();
        Long userId = transport.getUserId();
        String similarityCode = this.genSimilarityCode(transport);

        List<String> telList = new ArrayList<>();
        String tel = transport.getTel();
        String tel3 = transport.getTel3();
        String tel4 = transport.getTel4();
        if (StringUtils.isNotBlank(tel)) {
            telList.add("'" + tel + "'");
        }
        if (StringUtils.isNotBlank(tel3)) {
            telList.add("'" + tel3 + "'");
        }
        if (StringUtils.isNotBlank(tel4)) {
            telList.add("'" + tel4 + "'");
        }

        String telStrs = StringUtils.join(telList, ",");

        Long personalSimilarity = tytTransportMapper.getPersonalSimilarity(similarityCode, dayBegin, userId, telStrs, srcMsgId);

        if (personalSimilarity != null) {
            log.info("checkPersonalSimilarity_exist : " + transport.getUserId());
            super.throwException(ResponseEnum.request_error.info("您已经发布过类似信息！"));
        }
    }

    private void handleStandardTransport(Integer matchItemId, TytTransport transport) {

        TytMachineTypeBrandNew machineTypeBrandNew = null;

        // 拆版后新版本标准化查询
        if (matchItemId != null && matchItemId != -1) {
            machineTypeBrandNew = machineTypeBrandService.getById(matchItemId);
            if (machineTypeBrandNew != null) {
                transport.setIsStandard((byte) 0);
                transport.setType(machineTypeBrandNew.getTopType());
                transport.setBrand(machineTypeBrandNew.getBrand());
                transport.setGoodTypeName(machineTypeBrandNew.getSecondClass());
                transport.setGoodNumber(1);
            }
        } else {
            transport.setIsStandard((byte) 1);
            transport.setMatchItemId(-1);
        }

        this.addReferTransportNewInfo(transport, machineTypeBrandNew);

    }

    /**
     * 计算乘法
     *
     * @param reqNumber
     * @param multiple
     * @return
     */
    private Integer calcMultiply(String reqNumber, int multiple) {
        BigDecimal bigDecimal = new BigDecimal(reqNumber);
        BigDecimal multDec = new BigDecimal(multiple);

        BigDecimal resultDec = bigDecimal.multiply(multDec);
        return resultDec.intValue();
    }

    /**
     * 处理参考参数
     *
     * @param reqNumberStr
     * @param numberDec
     * @return
     */
    private Integer getReferNumber(String reqNumberStr, BigDecimal numberDec) {
        Integer referNumber = null;
        if (StringUtils.isNotBlank(reqNumberStr) && StringBaseUtils.isDouble(reqNumberStr)) {
            referNumber = this.calcMultiply(reqNumberStr, 100);
        } else {
            if (numberDec != null) {
                referNumber = numberDec.movePointRight(2).intValue();
            }
        }
        return referNumber;
    }

    /**
     * 处理参考参数
     */
    public static Integer getReferNumber(BigDecimal reqNumber, String numberDecStr) {
        Integer referNumber = null;
        if (reqNumber != null) {
            referNumber = reqNumber.movePointRight(2).intValue();
        } else {
            if (StringUtils.isNotBlank(numberDecStr)) {
                BigDecimal numberDec = new BigDecimal(numberDecStr);
                referNumber = numberDec.movePointRight(2).intValue();
            }
        }
        return referNumber;
    }


    /**
     * 补充新版标准化货物信息的参考值
     *
     * @param transport
     */
    public void addReferTransportNewInfo(TytTransport transport, TytMachineTypeBrandNew machineTypeBrandNew) {

        if (machineTypeBrandNew != null) {
            transport.setReferLength(this.getReferNumber(transport.getLength(), machineTypeBrandNew.getLength()));
            transport.setReferWidth(this.getReferNumber(transport.getWide(), machineTypeBrandNew.getWidth()));
            transport.setReferHeight(this.getReferNumber(transport.getHigh(), machineTypeBrandNew.getHeight()));
            transport.setReferWeight(this.getReferNumber(transport.getWeight(), machineTypeBrandNew.getWeight()));
        } else {
            transport.setReferLength(this.getReferNumber(transport.getLength(), null));
            transport.setReferWidth(this.getReferNumber(transport.getWide(), null));
            transport.setReferHeight(this.getReferNumber(transport.getHigh(), null));
            transport.setReferWeight(this.getReferNumber(transport.getWeight(), null));
        }
    }


    /**
     * 撤销货源
     *
     * @param srcMsgId
     */
    private List<TytTransport> getActiveTransportList(Long srcMsgId) {

        List<Integer> statusList = Arrays.asList(1, 4, 5);

        Example exa = new Example(TytTransport.class);
        exa.and().andEqualTo("srcMsgId", srcMsgId)
                .andIn("status", statusList);

        List<TytTransport> tytTransports = tytTransportMapper.selectByExample(exa);
        return tytTransports;
    }


    /**
     * 今日货源编辑
     *
     * @param newTransport
     * @param oldTran
     */
    private void todayModifyPublish(TytTransport newTransport, TytTransportMain oldTran, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {

        Long srcMsgId = oldTran.getSrcMsgId();

        if (srcMsgId == null) {
            super.throwException(ResponseEnum.request_error.info("货源id不能为空"));
        }

        Date nowTime = new Date();

        Date startDayTime = DateUtil.startOfDay(nowTime);

        // 将历史货物设置为transport不显示状态且无效， ；
        int updateCount = tytTransportMapper.disableTransport(oldTran.getSrcMsgId(), nowTime);

        // 处理重发次数，pc端判充使用
        Integer resendCounts = tytTransportMapper.getMaxResendCount(oldTran.getSrcMsgId(), startDayTime);
        resendCounts = resendCounts + 1;
        String pcOldContent = "[" + resendCounts + "]." + newTransport.getPcOldContent();

        log.info("源货物的ID为{}  src_msgId{}", oldTran.getId(), oldTran.getSrcMsgId());
        // 修改transportMain表信息  并保存
        BeanUtils.copyProperties(newTransport, oldTran, "id", "srcMsgId", "tsOrderNo", "ctime", "releaseTime");

        oldTran.setResendCounts(resendCounts);
        oldTran.setPcOldContent(pcOldContent);
        oldTran.setMtime(nowTime);
        oldTran.setStatus((short) TransportStatusEnum.active.getCode());

        tytTransportMainMapper.updateByPrimaryKeySelective(oldTran);

        // main表扩展表修改
        TytTransportMainExtend oldMainExtend = tytTransportMainExtendMapper.selectBySrcMsgId(srcMsgId);
        if (Objects.nonNull(oldMainExtend)) {
            BeanUtils.copyProperties(mainExtend, oldMainExtend, "srcMsgId");
            oldMainExtend.setModifyTime(oldTran.getMtime());
            tytTransportMainExtendMapper.updateByPrimaryKey(oldMainExtend);
        } else {
            mainExtend.setSrcMsgId(oldTran.getSrcMsgId());
            mainExtend.setCreateTime(oldTran.getCtime());
            mainExtend.setModifyTime(oldTran.getMtime());
            tytTransportMainExtendMapper.insertSelective(mainExtend);
        }

        /* 设置重发次数并增加序号 ，保存transport信息*/
        newTransport.setResendCounts(resendCounts);
        newTransport.setPcOldContent(pcOldContent);
        newTransport.setTsOrderNo(oldTran.getTsOrderNo());
        newTransport.setSrcMsgId(oldTran.getSrcMsgId());
        newTransport.setReleaseTime(oldTran.getReleaseTime());
        log.info("[代调发货]--今日保存参数:{}", JSON.toJSONString(newTransport));
        tytTransportMapper.insertSelective(newTransport);

        BeanUtils.copyProperties(mainExtend, transportExtend);
        transportExtend.setId(null);
        transportExtend.setTsId(newTransport.getId());
        transportExtend.setCreateTime(newTransport.getCtime());
        transportExtend.setModifyTime(newTransport.getMtime());
        transportExtend.setTopFlag(1);
        transportExtendMapper.insertSelective(transportExtend);
    }


    /**
     * 历史货源编辑重发
     *
     * @param newTransport
     * @param oldTran
     */
    private void historyRePublish(TytTransport newTransport, TytTransportMain oldTran, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {

        TytTransportMain transportMain = ConvertUtil.beanConvert(newTransport, new TytTransportMain());

        Date nowTime = new Date();
        Long oldSrcMsgId = oldTran.getSrcMsgId();

        // 将历史货物设置为transportMain、transport不显示状态；
        tytTransportMainMapper.hideTransportMain(oldSrcMsgId, nowTime);
        tytTransportMapper.hideTransport(oldSrcMsgId, nowTime);
        log.info("[代调发货]--历史保存参数:{}", JSON.toJSONString(transportMain));
        // 保存transportMain信息
        tytTransportMainMapper.insertSelective(transportMain);
        tytTransportMainMapper.setSrcMsgId(transportMain.getId());
        // main表扩展表

        mainExtend.setId(null);
        mainExtend.setSrcMsgId(transportMain.getId());
        mainExtend.setCreateTime(transportMain.getCtime());
        mainExtend.setModifyTime(transportMain.getMtime());
        tytTransportMainExtendMapper.insertSelective(mainExtend);

        // 保存transport信息
        newTransport.setSrcMsgId(transportMain.getId());
        tytTransportMapper.insertSelective(newTransport);

        BeanUtils.copyProperties(mainExtend, transportExtend);
        transportExtend.setId(null);
        transportExtend.setTsId(newTransport.getId());
        transportExtend.setCreateTime(newTransport.getCtime());
        transportExtend.setModifyTime(newTransport.getMtime());
        transportExtend.setTopFlag(1);
        transportExtendMapper.insertSelective(transportExtend);

    }

    /**
     * 新发货源
     *
     * @param newTransport
     */
    private void newPublish(TytTransport newTransport, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {
        log.info("[代调发货] newPublish transport对象：{}", JSON.toJSONString(newTransport));
        TytTransportMain transportMain = ConvertUtil.beanConvert(newTransport, new TytTransportMain());

        // 保存transportMain信息
        tytTransportMainMapper.insertSelective(transportMain);
        tytTransportMainMapper.setSrcMsgId(transportMain.getId());
        // main表扩展表
        mainExtend.setSrcMsgId(transportMain.getId());
        mainExtend.setCreateTime(transportMain.getCtime());
        mainExtend.setModifyTime(transportMain.getMtime());
        tytTransportMainExtendMapper.insertSelective(mainExtend);

        // 保存transport信息
        newTransport.setSrcMsgId(transportMain.getId());
        tytTransportMapper.insertSelective(newTransport);

        BeanUtils.copyProperties(mainExtend, transportExtend);
        transportExtend.setId(null);
        transportExtend.setTsId(newTransport.getId());
        transportExtend.setCreateTime(newTransport.getCtime());
        transportExtend.setModifyTime(newTransport.getMtime());
        transportExtend.setTopFlag(1);
        transportExtendMapper.insertSelective(transportExtend);

    }


    /**
     * @param newTransport
     * @param oldTran
     * @param saveModeEnum 1:新发布；2：做日数据编辑发布：3：今日数据编辑发布
     * @return
     * @throws Exception
     */
    public TytTransport addTransportBusiness(TytTransport newTransport, TytTransportMain oldTran,
                                             TransportSaveModeEnum saveModeEnum, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {

        newTransport.setId(null);

        switch (saveModeEnum) {
            case modifySave:
                this.todayModifyPublish(newTransport, oldTran, mainExtend, transportExtend);
                break;
            case oldSave:
                this.historyRePublish(newTransport, oldTran, mainExtend, transportExtend);
                break;
            default:
                this.newPublish(newTransport, mainExtend, transportExtend);
                break;

        }

        return newTransport;
    }

    public void updateSimilarityCode(TytTransport tb) {
        Date nowTime = new Date();
        Date startDayTime = DateUtil.startOfDay(nowTime);

        String taskContentKey = GoodsUnique.extractKeyInfo(tb.getTaskContent());

        //相似code
        String similarityCode = this.genSimilarityCode(tb, taskContentKey);

        //首发ID
        Long similarityFirstId = tb.getSrcMsgId();

        String newNickName = StringBaseUtils.hidePhoneNumber(tb.getNickName());
        //首发货物基本信息
        //	String similarityFirstInfo = tb.getType()+tb.getGoodTypeName() + "##" + tb.getNickName() + "##" + tb.getUserType() + "##" + tb.getIsInfoFee();
        String similarityFirstInfo = taskContentKey + "##" + newNickName + "##" + tb.getUserType()
                + "##" + tb.getIsInfoFee() + "##" + tb.getRegTime().getTime();

        TytTransport similarityTb = tytTransportMapper.getFirstBySameCode(similarityCode, startDayTime);

        if (similarityTb != null) {
            similarityFirstId = similarityTb.getSimilarityFirstId();
            similarityFirstInfo = similarityTb.getSimilarityFirstInfo();
        }

        TytTransport updateTransport = new TytTransport();
        updateTransport.setId(tb.getId());
        updateTransport.setSimilarityCode(similarityCode);
        updateTransport.setSimilarityFirstId(similarityFirstId);
        updateTransport.setSimilarityFirstInfo(similarityFirstInfo);
        updateTransport.setSort(updateTransport.getId());
        tytTransportMapper.updateByPrimaryKeySelective(updateTransport);

        TytTransportMain updateTransportMain = new TytTransportMain();
        updateTransportMain.setId(tb.getSrcMsgId());
        updateTransportMain.setSimilarityCode(similarityCode);
        updateTransportMain.setSimilarityFirstId(similarityFirstId);
        updateTransportMain.setSimilarityFirstInfo(similarityFirstInfo);
        tytTransportMainMapper.updateByPrimaryKeySelective(updateTransportMain);

    }

    /**
     * 保存货源
     *
     * @param tytTransport
     * @param oldTransportMain
     * @return
     */
    private TytTransport savePublishTransport(TytTransport tytTransport, TytTransportMain oldTransportMain, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {

        Long updateSrcMsgId = null;
        if (oldTransportMain != null) {
            updateSrcMsgId = oldTransportMain.getId();
        }


        //更新用户相关字段
        this.refreshUserInfo(tytTransport);

        //标准化货源处理
        this.handleStandardTransport(tytTransport.getMatchItemId(), tytTransport);

        TransportSaveModeEnum saveModeEnum = TransportSaveModeEnum.newSave;

        tytTransport.setIsDisplay((short) 1);
        tytTransport.setDisplayType("1");

        if (tytTransport.getIsShow() == null) {
            tytTransport.setIsShow(1);
        }

        if (updateSrcMsgId != null && updateSrcMsgId > 0) {
            /*
            oldTransportMain = tytTransportMainMapper.selectByPrimaryKey(updateSrcMsgId);

            if(oldTransportMain == null){
                super.throwException(ResponseEnum.request_error.info("该货源不存在！"));
            }
             */

            long ctimeDayLong = DateUtil.startOfDay(oldTransportMain.getCtime()).getTime();
            long nowDayTime = DateUtil.startOfDay(new Date()).getTime();

            if (nowDayTime > ctimeDayLong) {
                //历史货源
                saveModeEnum = TransportSaveModeEnum.oldSave;

                updateSrcMsgId = null;
            } else {
                saveModeEnum = TransportSaveModeEnum.modifySave;
                //编辑校验
                Short status = oldTransportMain.getStatus();
                if (TransportStatusEnum.active.equalsCode(status.intValue())) {
                    super.throwException(ResponseEnum.request_error.info("该货源未下架，不允许编辑！"));
                }
            }
        } else {
            updateSrcMsgId = null;
        }

        tytTransport.setSrcMsgId(updateSrcMsgId);

        if (saveModeEnum.equals(TransportSaveModeEnum.newSave)) {
            //新发货源
            tytTransport.setFirstPublishType(tytTransport.getPublishType());
        } else {
            tytTransport.setFirstPublishType(oldTransportMain.getFirstPublishType());
        }
        // sort先给默认值
        tytTransport.setSort(0L);
        tytTransport.setSortType(TsSortTypeEnum.top.getCode());

        //校验是否重复
//        this.checkHashCodeExist(tytTransport);

        //校验相似货源
        if (!tytTransport.getSourceType().equals(SourceTypeEnum.ymm.getCode())) {
            this.checkPersonalSimilarity(tytTransport);
        }

        log.info("[代调发货]--保存货源参数：{}", JSON.toJSONString(tytTransport));

        // 获取好货模型分数，好货运价模型分数
        getGoodsModelScore(tytTransport, mainExtend, transportExtend);

        /* 保存数据到数据库，缓存 */
        TytTransport newTransport = this.addTransportBusiness(tytTransport, oldTransportMain, saveModeEnum, mainExtend, transportExtend);

        // 更新相似货源信息及首发信息
        this.updateSimilarityCode(newTransport);

        if (saveModeEnum.equals(TransportSaveModeEnum.newSave) || saveModeEnum.equals(TransportSaveModeEnum.oldSave)) {
            //需要发布新的货源
            tytUserSubMapper.increaseSendTptNumber(tytTransport.getUserId());
        }

        // 将发布成功的货源唯一值放到缓存中；
        //this.addTransportHashCode(newTransport.getHashCode(), todayStr);

        return newTransport;
    }

    /**
     * 调用bi获取好货模型分数，好货运价模型分数
     *
     * @param tytTransport
     * @param mainExtend
     * @param transportExtend
     */
    private void getGoodsModelScore(TytTransport tytTransport, TytTransportMainExtend mainExtend, TytTransportExtend transportExtend) {
        try {
            GoodModelResult goodModelResult = tecServiceFeeConfigService.checkInstantGrab(tytTransport);
            if (Objects.nonNull(goodModelResult)) {
                mainExtend.setGoodModelScore(goodModelResult.getScore());
                mainExtend.setGoodModelLevel(goodModelResult.getLevel());
                mainExtend.setLimGoodModelScore(goodModelResult.getLim_score());
                mainExtend.setLimGoodModelLevel(goodModelResult.getLim_level());
                transportExtend.setGoodModelScore(goodModelResult.getScore());
                transportExtend.setGoodModelLevel(goodModelResult.getLevel());
                transportExtend.setLimGoodModelScore(goodModelResult.getLim_score());
                transportExtend.setLimGoodModelLevel(goodModelResult.getLim_level());
            }
            BigDecimal commissionScore = tecServiceFeeConfigService.checkCommissionScore(tytTransport);
            if (Objects.nonNull(commissionScore)) {
                mainExtend.setCommissionScore(commissionScore);
                transportExtend.setCommissionScore(commissionScore);
            }
        } catch (Exception e) {
            log.error("getGoodsModelScore error:", e);
        }
    }

    /**
     * 将发布成功的货源唯一值放到缓存中
     *
     * @param hashCode
     * @param todayStr
     */
    private void addTransportHashCode(String hashCode, String todayStr) {

        String key = RedisConstant.Transport.hash_code_key + todayStr + "_" + hashCode;
        redisCacheUtil.setObject(key, hashCode, 24, TimeUnit.HOURS);

    }

    /**
     * 移除hashCode
     *
     * @param hashCode
     * @param todayStr
     */
    private void delTransportHashCode(String hashCode, String todayStr) {

        String key = RedisConstant.Transport.hash_code_key + todayStr + "_" + hashCode;
        redisCacheUtil.delete(key);

    }

    /**
     * 刷新用户信息
     *
     * @param transport
     */
    private void refreshUserInfo(TytTransport transport) {

        Long userId = transport.getUserId();
        if (userId == null) {
            super.throwException(ResponseEnum.request_error.info());
        }

        //用户信用分及等级
        ApiDataUserCreditInfoTwo userCreditInfo = userCreditInfoService.getUserCreditInfo(transport.getUserId());

        BigDecimal totalScore = BigDecimal.ZERO;
        Integer rankLevel = 1;
        if (null != userCreditInfo) {
            if (userCreditInfo.getTotalScore() != null) {
                totalScore = userCreditInfo.getTotalScore();
            }
            if (userCreditInfo.getRankLevel() != null) {
                rankLevel = userCreditInfo.getRankLevel();
            }
        }
        transport.setTotalScore(totalScore);
        transport.setRankLevel(rankLevel);

        //用户成交单数
        Example exa = new Example(TytUserSub.class);
        exa.and().andEqualTo("userId", userId);
        TytUserSub tytUserSub = tytUserSubMapper.selectOneByExample(exa);

        Integer tradeNum = 0;
        Integer userDealNum = tytUserSub.getDealNum();
        if (userDealNum != null) {
            tradeNum = userDealNum;
        }
        transport.setTradeNum(tradeNum);

        //用户授权昵称
        String authName = null;

        Example oaExa = new Example(TytOwnerAuth.class);
        oaExa.and().andEqualTo("userId", userId)
                .andEqualTo("status", 2);
        TytOwnerAuth tytOwnerAuth = tytOwnerAuthMapper.selectOneByExample(oaExa);

        if (tytOwnerAuth != null) {
            String authNameTmp = tytOwnerAuth.getAuthName();

            if (StringUtils.isNotBlank(authNameTmp)) {
                authName = authNameTmp;
            }
        }
        transport.setAuthName(authName);

    }

    /**
     * 校验小程序订单是否可以发布
     *
     * @param srcMsgId
     * @param backendId
     */
    private void checkBackendValid(Long srcMsgId, Long backendId) {

        TytTransportBackend transportBackend = null;
        if (backendId != null && backendId != 0L) {
            transportBackend = tytTransportBackendMapper.selectByPrimaryKey(backendId);
        }

        //校验小程序是否已发布
        if (transportBackend != null) {
            Long backendMsgId = transportBackend.getSrcMsgId();

            if (!CommonUtil.objEquels(srcMsgId, backendMsgId, true)) {
                //证明已由其他人发过
                super.throwException(ResponseEnum.request_error.info("当前订单已发布，无法重新发布！"));
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TytTransport publishTransportInTrans(TransportPublishReq transportReq) {

        /*当前货源存在订单直接返回 */
        Date nowTime = new Date();
        Date startDayTime = DateUtil.startOfDay(nowTime);

        String todayStr = DateUtil.dateToString(startDayTime, DateUtil.day_format);

        //兼容openApi
        TytInternalEmployee sessionEmployee = null;
        if (Objects.nonNull(transportReq.getDispatchId())) {
            sessionEmployee = tytInternalEmployeeMapper.selectByPrimaryKey(transportReq.getDispatchId());
        }

        if (Objects.isNull(sessionEmployee)) {
            sessionEmployee = super.getSessionEmployee();
        }


        Long sessionManagerId = sessionEmployee.getId();

        //1. 校验货物参数
        this.validationTransportPublishBean(transportReq);
        //校验货主是否授权
        Long ownerUserId = transportReq.getOwnerUserId();

        if (ownerUserId == null) {
            super.throwException(ResponseEnum.request_error.info("货主id不能为空"));
        }

        TytUser tytUser = tytUserMapper.selectByPrimaryKey(ownerUserId);

        if (tytUser == null) {
            super.throwException(ResponseEnum.request_error.info("用户不存在！"));
        }

        Example exa = new Example(CsMaintainedCustom.class);
        exa.and().andEqualTo("customId", ownerUserId);

        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomMapper.selectOneByExample(exa);

        if (csMaintainedCustom == null || !EmpowerStatusEnum.authed.equalsCode(csMaintainedCustom.getEmpowerStatus().intValue())) {
            super.throwException(ResponseEnum.request_error.info("用户未授权，无法发货成功！"));
        }

        //如果是开票货源，校验开票相关规则
        if (transportReq.getInvoiceTransport() != null && transportReq.getInvoiceTransport() == INVOICE_TRANSPORT) {
            if (transportReq.getInvoiceSubjectId() == null || StringUtils.isBlank(transportReq.getServiceProviderCode())) {
                String invoiceSujectData = tytConfigService.getStringValue("invoice_subject_data", "1,JCZY");
                String[] split = invoiceSujectData.split(",");
                transportReq.setInvoiceSubjectId(Long.valueOf(split[0]));
                transportReq.setServiceProviderCode(split[1]);
            }

            CheckInvoiceTransportUserAndParam checkInvoiceTransportUserAndParam = new CheckInvoiceTransportUserAndParam(transportReq.getOwnerUserId(), transportReq.getDistance() == null ? null : transportReq.getDistance().toString(), transportReq.getPrice(), transportReq.getWeight()
                    , transportReq.getLength(), transportReq.getWide(), transportReq.getHigh(), transportReq.getInvoiceSubjectId());
            TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.check_user_can_publish_invoice_transport_in_do_publish_and_check_param, EntityUtils.entityToMap(checkInvoiceTransportUserAndParam), null);
            log.info("开票货源编辑发布调用plat开票货源直接发布接口，请求参数{}, 返回结果：{}", JSON.toJSONString(checkInvoiceTransportUserAndParam), JSON.toJSONString(tytResultMsgBean));
            if (tytResultMsgBean != null && !tytResultMsgBean.isSuccess()) {
                super.throwException(ResponseEnum.request_error.info(tytResultMsgBean.getMsg()));
            }

            if (ExcellentEnum.SPECIAL.getCode().equals(transportReq.getExcellentGoods())
                    && transportReq.getServiceProviderCode() != null && "JCZY".equals(transportReq.getServiceProviderCode())) {
                log.info("专车暂不支持甘肃网货开票主体，请在发票类型中更换");
                super.throwException(ResponseEnum.request_error.info("专车暂不支持甘肃网货开票主体，请在发票类型中更换"));
            }

            if (ExcellentEnum.SPECIAL.getCode().equals(transportReq.getExcellentGoods())
                    && transportReq.getServiceProviderCode() != null && "XHL".equals(transportReq.getServiceProviderCode())) {
                log.info("专车暂不支持翔和翎开票，请更换专票主体");
                super.throwException(ResponseEnum.request_error.info("专车暂不支持翔和翎开票，请更换专票主体"));
            }

            //翔和翎开票校验收货人信息
            if (transportReq.getServiceProviderCode().equals("XHL")) {
                if (StringUtils.isBlank(transportReq.getConsigneeEnterpriseName())
                        || StringUtils.isBlank(transportReq.getConsigneeTel())
                        || StringUtils.isBlank(transportReq.getConsigneeEnterpriseName())) {
                    super.throwException(ResponseEnum.request_error.info("请填写收货人信息"));
                }
                if (tytUser != null) {
                    if (StringUtils.isNotBlank(tytUser.getCellPhone())
                            && tytUser.getCellPhone().equals(transportReq.getConsigneeTel())) {
                        super.throwException(ResponseEnum.request_error.info("收货联系电话不能与发货人账号一致"));
                    }
                    if (StringUtils.isNotBlank(tytUser.getTrueName())
                            && tytUser.getTrueName().equals(transportReq.getConsigneeName())) {
                        super.throwException(ResponseEnum.request_error.info("收货人姓名不能与发货人姓名一致"));
                    }
                }
                TytInvoiceEnterprise userEnterpriseData = tytUserMapper.getUserEnterpriseData(ownerUserId);
                if (userEnterpriseData != null
                        && StringUtils.isNotBlank(userEnterpriseData.getEnterpriseName())
                        && transportReq.getConsigneeEnterpriseName().equals(userEnterpriseData.getEnterpriseName())) {
                    super.throwException(ResponseEnum.request_error.info("收货单位不能与发货人企业认证名称一致"));
                }
            }
            // 暂时没用到
            if (Objects.equals(transportReq.getPaymentsType(), 1)) {
                checkSegmentedPayments(transportReq.getPrepaidPrice(), transportReq.getCollectedPrice(), transportReq.getReceiptPrice(), transportReq.getPrice(), transportReq.getOwnerUserId(), false);
            }

        }

        /**
         *   代调6期
         */
        checkAndSaveFirstPublish(transportReq);
        //生成transport
        TytTransport tytTransport = this.createTransport(transportReq, tytUser);
        if (tytTransport.getDistance() == null) {
            // 距离从字典中取
            TytMapDict tytMapDict = tytMapDicService.getDistance(tytTransport);
            if (tytMapDict != null) {
                tytTransport.setAndroidDistance(tytMapDict.getDistance());
                tytTransport.setIosDistance(tytMapDict.getIosDistance());
                tytTransport.setDistance(tytMapDict.getDistance());
            }
        } else {
            tytTransport.setAndroidDistance(tytTransport.getDistance());
            tytTransport.setIosDistance(tytTransport.getDistance());
        }
//        // 距离从字典中取
//        TytMapDict tytMapDict = tytMapDicService.getDistance(tytTransport);
//        if (tytMapDict != null) {
//            tytTransport.setAndroidDistance(tytMapDict.getDistance());
//            tytTransport.setIosDistance(tytMapDict.getIosDistance());
//            if (tytTransport.getDistance() == null) {
//                tytTransport.setDistance(tytMapDict.getDistance());
//            }
//        }

        Long updateSrcMsgId = transportReq.getSrcMsgId();
        TytTransportMain oldTransportMain = null;
        if (updateSrcMsgId != null && updateSrcMsgId > 0) {
            oldTransportMain = tytTransportMainMapper.selectByPrimaryKey(updateSrcMsgId);

            if (oldTransportMain == null) {
                super.throwException(ResponseEnum.request_error.info("货源不存在！"));
            }
        }

        this.checkBackendValid(updateSrcMsgId, transportReq.getBackendId());
        log.info("[代调发货] 生成transport对象save之前：{}", JSON.toJSONString(tytTransport));

        //YMM一口价货源判断是否需要转优车定价
        GoodCarPriceTransportTabAndBIPriceVO goodCarPriceTransportTabAndBIPriceVO = null;
        if (transportReq.getSourceType() != null && transportReq.getSourceType().equals(SourceTypeEnum.ymm.getCode())
                && transportReq.getPublishType() == 2 && StringUtils.isNotBlank(transportReq.getPrice())
                && tytTransport.getDistance() != null) {
            goodCarPriceTransportTabAndBIPriceVO = isGoodCarPriceTransportByYmm(tytTransport);
        }

        //如果货源是YMM货源或者是专车货源，技术服务费都通过计算的方式赋值
        //------------------------这一步要在所有逻辑处理结束后进行-------------------------
        MeetCommissionRulesResult meetCommissionRules = new MeetCommissionRulesResult();
        boolean isCommissionTransport = false;
        TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = null;
        if (tytTransport.getSourceType().equals(SourceTypeEnum.ymm.getCode()) || (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 2)) {
            tytTecServiceFeeConfigToComputResult = makeTecServiceFeeData(tytTransport, oldTransportMain, meetCommissionRules);
            if (tytTecServiceFeeConfigToComputResult != null) {
                isCommissionTransport = true;
            }
        }


        TytTransportMainExtend mainExtend = new TytTransportMainExtend();
        TytTransportExtend transportExtend = new TytTransportExtend();
        BeanUtil.copyProperties(transportReq, mainExtend);
        BeanUtil.copyProperties(transportReq, transportExtend);


        TytTransport saveTransport = this.savePublishTransport(tytTransport, oldTransportMain, mainExtend, transportExtend);

        //如果是小程序那边来的货，需要更改小程序表的状态
        if (transportReq.getBackendId() != null) {
            dealBackendTransport(transportReq.getBackendId(), saveTransport);
        }

        giveGoodsUserService.saveGiveGoodsUser(sessionManagerId, transportReq.getGiveGoodsPhone(), transportReq.getDispatchIdentityCode());

        //保存调度发货记录
        transportDispatchService.saveDispatch(saveTransport, transportReq.getOwnerFreight(), transportReq.getInfoFeeDiff(),
                transportReq.getGiveGoodsPhone(), transportReq.getGiveGoodsName(), sessionEmployee, csMaintainedCustom, tytUser, transportReq.getHongXinOrderId());

        // 如果是运满满货源，同步添加到运满满关系表中
        if (transportReq.getSourceType() != null && transportReq.getSourceType().equals(SourceTypeEnum.ymm.getCode())) {
            transportYMMService.saveOrUpdateMbMerge(saveTransport.getSrcMsgId(), transportReq.getCargoId(), transportReq.getCargoVersion(), transportReq.getPublishWay());
        }

        String instantGrabResendOverPushcacheKey = CommonUtil.joinRedisKey("tyt:cache:bi:data", saveTransport.getSrcMsgId().toString(), DateUtil.dateToString(new Date(), DateUtil.day_format_short));
        String biDataJsonString = null;

        if (goodCarPriceTransportTabAndBIPriceVO != null) {
            //只要不是null，就代表YMM货源符合优车定价条件
            biDataJsonString = "{\"automaticGoodCarPriceTransportType\":5,\"fixPriceFast\":"
                    + goodCarPriceTransportTabAndBIPriceVO.getFixPriceFast() + ",\"fixPriceMax\":"
                    + goodCarPriceTransportTabAndBIPriceVO.getFixPriceMax() + ",\"fixPriceMin\":"
                    + goodCarPriceTransportTabAndBIPriceVO.getFixPriceMin() + ",\"goodCarPriceTransport\":1}";
        }

        // 保存公里数和其他费用到五级地址表
        if (ExcellentEnum.SPECIAL.getCode().equals(transportReq.getExcellentGoods())) {
            if (Objects.isNull(transportReq.getOtherFee())) {
                transportReq.setOtherFee(new BigDecimal("0"));
            }
            if (StringUtils.isNotBlank(biDataJsonString)) {
                JSONObject jsonObject = JSON.parseObject(biDataJsonString);
                jsonObject.put("distanceKilometer", transportReq.getDistanceKilometer());
                jsonObject.put("otherFee", transportReq.getOtherFee());
                biDataJsonString = jsonObject.toJSONString();
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("distanceKilometer", transportReq.getDistanceKilometer());
                jsonObject.put("otherFee", transportReq.getOtherFee());
                biDataJsonString = jsonObject.toJSONString();
            }
            // 直接写入表
            GoodsAddressLevelRecord record = goodsAddressLevelRecordMapper.selectBySrcMsgId(saveTransport.getSrcMsgId());
            if (Objects.isNull(record)) {
                goodsAddressLevelRecordMapper.insertGoodsAddress(saveTransport.getSrcMsgId(), transportReq.getDistanceKilometer(), transportReq.getOtherFee());
            } else {
                goodsAddressLevelRecordMapper.updateGoodsAddress(record.getId(), transportReq.getDistanceKilometer(), transportReq.getOtherFee());
            }
        }

        biDataJsonString = saveTecServiceFee(isCommissionTransport, biDataJsonString, meetCommissionRules, saveTransport, tytTecServiceFeeConfigToComputResult);

        if (biDataJsonString != null) {
            log.info("[抽佣] 写入BI五级地址表缓存：{}", biDataJsonString);
            stringRedisTemplate.opsForValue().set(instantGrabResendOverPushcacheKey, biDataJsonString, 60 * 60 * 60, TimeUnit.SECONDS);
        }


        // 将发布成功的货源唯一值放到缓存中；
        this.addTransportHashCode(saveTransport.getHashCode(), todayStr);

        return saveTransport;
    }


    /**
     * @param prepaidPrice
     * @param collectedPrice
     * @param receiptPrice
     * @param price
     * @param userId
     * @param direct         是否走直接发布
     */
    public void checkSegmentedPayments(BigDecimal prepaidPrice,
                                       BigDecimal collectedPrice,
                                       BigDecimal receiptPrice,
                                       String price,
                                       Long userId, boolean direct) {

        // 如果到付金额不为空，就是分段支付
        if (collectedPrice != null) {
            prepaidPrice = prepaidPrice == null ? BigDecimal.ZERO : prepaidPrice;
            receiptPrice = receiptPrice == null ? BigDecimal.ZERO : receiptPrice;
            // 先判断当前是否允许分段支付
            TytInvoiceEnterprise userEnterpriseData = tytUserMapper.getUserEnterpriseData(userId);

            if (userEnterpriseData != null) {
                InvoiceTransportConfigLogDTO config = invoiceTransportConfigService.getLastInvoiceTransportEnterpriseConfig(userEnterpriseData.getId());
                if (config == null || config.getSegmentedPayments() != 1) {
                    if (direct) {
                        super.throwException(ResponseEnum.request_error.info("当前不支持分段支付，请使用编辑再发布"));

                    } else {
                        super.throwException(ResponseEnum.request_error.info("当前不支持分段支付，请选择全额到付"));

                    }
                }
            }
            BigDecimal totalPrice = collectedPrice.add(prepaidPrice).add(receiptPrice);
            if (StringUtils.isBlank(price) || new BigDecimal(price).compareTo(totalPrice) != 0) {
                super.throwException(ResponseEnum.request_error.info("分段支付运费与运费总价不一致，请修改"));

            }
            BigDecimal prepaidProportion = BigDecimal.valueOf(0.3);
            BigDecimal receiptProportion = BigDecimal.valueOf(0.2);
            TytPublicResource publicResourceVO = tytPublicResourceService.getPublicResource("segmented_payments_price");
            String proportion = "30%,20%";
            if (publicResourceVO != null) {
                proportion = publicResourceVO.getValue();
                if (StringUtils.isNotBlank(proportion)) {
                    prepaidProportion = new BigDecimal(proportion.split(COMMA)[0].replace("%", "")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    receiptProportion = new BigDecimal(proportion.split(COMMA)[1].replace("%", "")).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                }
            }
            BigDecimal priceNumber = new BigDecimal(price);
            if (!prepaidPrice.equals(BigDecimal.ZERO)) {
                // 计算预付运费上限
                BigDecimal maxPrepaidPrice = priceNumber.multiply(prepaidProportion).setScale(2, RoundingMode.HALF_UP);
                if (maxPrepaidPrice.compareTo(prepaidPrice) < 0) {
                    super.throwException(ResponseEnum.request_error.info("预付运费需低于" + maxPrepaidPrice + "元（司机运费" + proportion.split(COMMA)[0] + "）, 请修改"));


                }
            }
            if (!receiptPrice.equals(BigDecimal.ZERO)) {
                // 计算回单付运费上限
                BigDecimal maxReceipt = priceNumber.multiply(receiptProportion).setScale(2, RoundingMode.HALF_UP);
                if (maxReceipt.compareTo(receiptPrice) < 0) {
                    super.throwException(ResponseEnum.request_error.info("回单付运费需低于" + maxReceipt + "元（司机运费" + proportion.split(COMMA)[1] + "）, 请修改"));
                }
            }
        }
    }


    private String saveTecServiceFee(boolean isCommissionTransport, String biDataJsonString, MeetCommissionRulesResult meetCommissionRules, TytTransport saveTransport, TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult) {
        //如果是抽佣货源BI五级地址表缓存并在抽佣货源信息表存储
        if (isCommissionTransport) {
            if (StringUtils.isNotBlank(biDataJsonString)) {
                JSONObject jsonObject = JSON.parseObject(biDataJsonString);
                jsonObject.put("commissionTransport", "1");
                jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                biDataJsonString = jsonObject.toJSONString();
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("commissionTransport", "1");
                jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                biDataJsonString = jsonObject.toJSONString();
            }
            //记录抽佣货源信息
            tecServiceFeeConfigService.saveCommissionTransportTecServiceFeeData(saveTransport.getSrcMsgId(), tytTecServiceFeeConfigToComputResult);
            List<Integer> needFreeTecTypeListResult = new ArrayList<>();
            boolean memberFree = false;
            boolean noMemberFree = false;
            if (CollectionUtils.isNotEmpty(tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList())) {
                needFreeTecTypeListResult.addAll(tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList());
                if (tytTecServiceFeeConfigToComputResult.getNeedFreeTecTypeList().contains(5)) {
                    memberFree = true;
                    noMemberFree = true;
                }
            }

            tecServiceFeeConfigService.deleteFreeTecServiceFeeLogBySrcMsgId(saveTransport.getSrcMsgId());

            TytFreeTecServiceFeeLog tytFreeTecServiceFeeLog = new TytFreeTecServiceFeeLog(null, saveTransport.getSrcMsgId(), 0, 0, 0, 0, 0, new Date(), new Date());
            if (!needFreeTecTypeListResult.isEmpty()) {
                for (Integer code : needFreeTecTypeListResult) {
                    if (code == 1) {
                        tytFreeTecServiceFeeLog.setNewTransportUserFree(1);
                    }
                    if (code == 3) {
                        tytFreeTecServiceFeeLog.setCityFree(1);
                    }
                    if (code == 4) {
                        tytFreeTecServiceFeeLog.setGoodCarPriceTransportFree(1);
                    }
                }
                if (memberFree) {
                    tytFreeTecServiceFeeLog.setMemberTimeOutFree(1);
                }
                if (noMemberFree) {
                    tytFreeTecServiceFeeLog.setNoMemberTimeOutFree(1);
                }
            }
            tecServiceFeeConfigService.addFreeTecServiceFeeLog(tytFreeTecServiceFeeLog);

        } else {
            if (StringUtils.isNotBlank(biDataJsonString)) {
                JSONObject jsonObject = JSON.parseObject(biDataJsonString);
                if (jsonObject.containsKey("commissionTransport")) {
                    jsonObject.remove("commissionTransport");
                }
                if (jsonObject.containsKey("meetCommissionRules")) {
                    jsonObject.remove("meetCommissionRules");
                }
                biDataJsonString = jsonObject.toJSONString();
            } else {
                if (meetCommissionRules != null && meetCommissionRules.getMeetCommissionRules() != null) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                    biDataJsonString = jsonObject.toJSONString();
                } else {
                    biDataJsonString = "{}";
                }
            }
            log.info("[抽佣] 清除BI五级地址表缓存：{}", biDataJsonString);
            //不抽佣则清除抽佣货源信息
            tecServiceFeeConfigService.deleteTecServiceFeeDataBySrcMsgId(saveTransport.getSrcMsgId());
            tecServiceFeeConfigService.deleteFreeTecServiceFeeLogBySrcMsgId(saveTransport.getSrcMsgId());

        }
        return biDataJsonString;
    }

    private TytTecServiceFeeConfigToComputResult makeTecServiceFeeData(TytTransport tytTransport, TytTransportMain oldTransportMain, MeetCommissionRulesResult meetCommissionRules) {

        Integer defaultTecServiceFee = tytConfigService.getIntValue("default_tec_service_fee", 50);

        //调度后台发专车和YMM货源时完全无视调度手填的技术服务费
        tytTransport.setTecServiceFee(null);
        TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputResult = tecServiceFeeConfigService.computeTecServiceFeeBtTransportData(tytTransport, oldTransportMain, meetCommissionRules);
        if (tytTecServiceFeeConfigToComputResult != null) {
            BigDecimal tecServiceFee = tytTecServiceFeeConfigToComputResult.getTecServiceFee();
            if (tecServiceFee == null) {
                tecServiceFee = new BigDecimal(defaultTecServiceFee);
            }
            tytTransport.setTecServiceFee(tecServiceFee);
            //抽佣货源打标
            if (StringUtils.isNotBlank(tytTransport.getLabelJson())) {
                JSONObject jsonObject = JSON.parseObject(tytTransport.getLabelJson());
                jsonObject.put("commissionTransport", "1");
                if (meetCommissionRules.getMeetCommissionRules() != null) {
                    jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                }
                tytTransport.setLabelJson(jsonObject.toJSONString());
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("commissionTransport", "1");
                if (meetCommissionRules.getMeetCommissionRules() != null) {
                    jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                }
                tytTransport.setLabelJson(jsonObject.toJSONString());
            }
        } else {
            //抽佣货源标识去掉
            if (StringUtils.isNotBlank(tytTransport.getLabelJson())) {
                JSONObject jsonObject = JSON.parseObject(tytTransport.getLabelJson());
                if (jsonObject.containsKey("commissionTransport")) {
                    jsonObject.remove("commissionTransport");
                    //如果曾经抽佣现在不抽了，要把main表技术服务费字段修改为0
                    tytTransport.setTecServiceFee(new BigDecimal(0));
                }
                if (meetCommissionRules.getMeetCommissionRules() != null) {
                    jsonObject.put("meetCommissionRules", meetCommissionRules.getMeetCommissionRules());
                }
                tytTransport.setLabelJson(StringUtils.isBlank(jsonObject.toJSONString()) ? "{}" : jsonObject.toJSONString());
            } else {
                tytTransport.setLabelJson("{}");
            }
            log.info("[抽佣] 货源取消抽佣打标，LabelJson：{}", tytTransport.getLabelJson());
        }

        return tytTecServiceFeeConfigToComputResult;
    }

    private GoodCarPriceTransportTabAndBIPriceVO isGoodCarPriceTransportByYmm(TytTransport tytTransport) {
        try {
            GoodCarPriceTransportParamBean goodCarPriceTransportParamBean = new GoodCarPriceTransportParamBean();
            goodCarPriceTransportParamBean.setDistance(new BigDecimal(tytTransport.getDistance()).movePointLeft(2).toString());
            goodCarPriceTransportParamBean.setUserId(tytTransport.getUserId());
            goodCarPriceTransportParamBean.setExcellentGoods(1);
            goodCarPriceTransportParamBean.setStartProvince(tytTransport.getStartProvinc());
            goodCarPriceTransportParamBean.setStartCity(tytTransport.getStartCity());
            goodCarPriceTransportParamBean.setStartArea(tytTransport.getStartArea());
            goodCarPriceTransportParamBean.setDestProvince(tytTransport.getDestProvinc());
            goodCarPriceTransportParamBean.setDestCity(tytTransport.getDestCity());
            goodCarPriceTransportParamBean.setDestArea(tytTransport.getDestArea());

            goodCarPriceTransportParamBean.setGoodsName(tytTransport.getTaskContent());
            goodCarPriceTransportParamBean.setGoodsWeight(tytTransport.getWeight());
            goodCarPriceTransportParamBean.setGoodsLength(tytTransport.getLength());
            goodCarPriceTransportParamBean.setGoodsWide(tytTransport.getWide());
            goodCarPriceTransportParamBean.setGoodsHigh(tytTransport.getHigh());
            log.info("判断YMM货源是否符合自动转优车定价，参数：{}", JSON.toJSONString(goodCarPriceTransportParamBean));

            TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.is_show_good_car_price_transport_tab, EntityUtils.entityToMap(goodCarPriceTransportParamBean), null);
            log.info("判断YMM货源是否符合自动转优车定价，plat返回结果：{}", JSON.toJSONString(tytResultMsgBean));
            if (tytResultMsgBean.isSuccess()) {
                GoodCarPriceTransportTabAndBIPriceVO goodCarPriceTransportTabAndBIPriceVO = JSON.parseObject(tytResultMsgBean.getData().toString(), GoodCarPriceTransportTabAndBIPriceVO.class);
                if (goodCarPriceTransportTabAndBIPriceVO != null
                        && goodCarPriceTransportTabAndBIPriceVO.getShowTab() != null && goodCarPriceTransportTabAndBIPriceVO.getShowTab()
                        && goodCarPriceTransportTabAndBIPriceVO.getFixPriceMin() != null
                        && goodCarPriceTransportTabAndBIPriceVO.getFixPriceMax() != null
                        && goodCarPriceTransportTabAndBIPriceVO.getFixPriceFast() != null
                        && goodCarPriceTransportTabAndBIPriceVO.getFixPriceMin() != 0
                        && goodCarPriceTransportTabAndBIPriceVO.getFixPriceMax() != 0
                        && goodCarPriceTransportTabAndBIPriceVO.getFixPriceFast() != 0
                        && tytTransport.getPrice() != null
                        && new BigDecimal(tytTransport.getPrice()).compareTo(new BigDecimal(goodCarPriceTransportTabAndBIPriceVO.getFixPriceMin())) >= 0) {
                    //符合优车定价条件
                    //需要将label_json的goodCarPriceTransport赋值为1
                    tytTransport.setExcellentGoods(1);
                    tytTransport.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
                    if (StringUtils.isNotBlank(tytTransport.getLabelJson())) {
                        JSONObject jsonObject = JSON.parseObject(tytTransport.getLabelJson());
                        jsonObject.put("goodCarPriceTransport", "1");
                        tytTransport.setLabelJson(jsonObject.toJSONString());
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("goodCarPriceTransport", "1");
                        tytTransport.setLabelJson(jsonObject.toJSONString());
                    }
                    return goodCarPriceTransportTabAndBIPriceVO;
                }
            }
        } catch (Exception e) {
            log.error("判断YMM货源是否符合自动转优车定价失败 货源信息：{}", JSON.toJSONString(tytTransport), e);
        }
        return null;
    }

    public void checkAndSaveFirstPublish(TransportPublishReq transportReq) {
        String giveGoodsPhone = transportReq.getGiveGoodsPhone();
        TytCustomFirstOrderRecord tytCustomFirstOrderRecord = new TytCustomFirstOrderRecord();

        TytTransportMain byPhone = tytTransportMainMapper.findByPhone(giveGoodsPhone);
        TytCustomFirstOrderRecord customFirstOrderRecord = tytCustomFirstOrderRecordMapper.findByPhone(giveGoodsPhone);
        tytCustomFirstOrderRecord.setCustomPhone(giveGoodsPhone);
        if (byPhone != null && customFirstOrderRecord == null) {
            tytCustomFirstOrderRecord.setFirstPublishTime(byPhone.getReleaseTime());
            tytCustomFirstOrderRecordMapper.insert(tytCustomFirstOrderRecord);
        } else if (byPhone == null && customFirstOrderRecord == null) {
            tytCustomFirstOrderRecord.setFirstPublishTime(new Date());
            tytCustomFirstOrderRecordMapper.insert(tytCustomFirstOrderRecord);
        }
    }

    /**
     * 处理小程序相关的数据
     *
     * @param backendId
     * @param tytTransport
     */
    private void dealBackendTransport(Long backendId, TytTransport tytTransport) {
        TytTransportBackend backendTransportBean = tytTransportBackendMapper.selectByPrimaryKey(backendId);
        if (Objects.nonNull(backendTransportBean) && backendTransportBean.getStatus() != 2) {
            // 如果小程序的货已经被其他平台接单并在发货中，这里就不允许再发货
            if (backendTransportBean.getStatus() == 3 && backendTransportBean.getOrderStatus() == 31) {
                super.throwException(ResponseEnum.request_error.info("该货源已被接单，不允许重复发货"));
            }
            backendTransportBean.setId(backendTransportBean.getId());
            backendTransportBean.setSrcMsgId(tytTransport.getSrcMsgId());
            /*backendTransportBean.setReceiverUserId(tytTransport.getUserId());
            backendTransportBean.setReceiverPhone(tytTransport.getUploadCellphone());*/
/*
            backendTransportBean.setReceiverShowName(tytTransport.getUserShowName());
*/
            backendTransportBean.setReceivingTime(new Date());
            backendTransportBean.setFindCarType(1);
            backendTransportBean.setStatus((short) 3);
            backendTransportBean.setOrderStatus(31);
            backendTransportBean.setMtime(new Date());
            tytTransportBackendMapper.updateByPrimaryKeySelective(backendTransportBean);
            tytTransportBackendMapper.updateBackendTransportUserStatus(backendTransportBean.getId());

            //记录企业货源状态流转日志
            OwnerCompanyLog ownerCompanyLog = new OwnerCompanyLog();
            ownerCompanyLog.setOrderNo(backendTransportBean.getOrderNo());
            ownerCompanyLog.setBackendId(backendTransportBean.getId());
            ownerCompanyLog.setCompanyId(backendTransportBean.getReceiverUserId());
            ownerCompanyLog.setEnterpriseId(backendTransportBean.getAppletsUserId());
            ownerCompanyLog.setStatus(3);
            ownerCompanyLog.setOrderStatus(31);
            ownerCompanyLog.setSrcMsgId(backendTransportBean.getSrcMsgId());
            ownerCompanyLog.setCreateTime(new Date());
            ownerCompanyLogMapper.insertSelective(ownerCompanyLog);
        }
    }

    /**
     * 向MQ发送信息
     *
     * @param transport 货物
     */
    private void sendNullifyMessage2MQ(TytTransport transport) {

        systemFixedExecutor.execute(() -> {
            try {
                // 发送初货物无效信息MQ
                TransportMqVo transportMqVo = new TransportMqVo();

                transportMqVo.setMessageSerailNum(CommonUtil.getUUID(false) + "-dispatch");
                transportMqVo.setMessageType(MqMessageTypeEnum.transport.getCode());
                transportMqVo.setTsId(transport.getId());
//        transportMqVo.setTopic(rocketMqProperty.getBaseTopic());
//        transportMqVo.setTag(rocketMqProperty.getTag());

                transportMqVo.setTopic("GOODS_CENTER_TOPIC");
                transportMqVo.setTag("TRANSPORT_PUBLISH");


                log.info("发货发mq {} : {},tsId:{}", "GOODS_CENTER_TOPIC", "TRANSPORT_PUBLISH",transportMqVo.getTsId());
                baseMqMessageService.sendMessage(transportMqVo, null);
            } catch (Exception e) {
                log.error("发货发mq异常", e);
            }
        });


    }

    /**
     * 代调发货页面，专车发货是否展示【司机驾驶此类货物】
     *
     * @param transportReq
     * @return JSONObject
     * <AUTHOR>
     * @date 2024/6/25 13:47
     */
    @Override
    public JSONObject isShowDriverDriving(TransportPublishReq transportReq) {
        if (Objects.isNull(transportReq.getOwnerUserId()) || StringUtils.isEmpty(transportReq.getStartCity()) ||
                StringUtils.isEmpty(transportReq.getDestCity()) || StringUtils.isEmpty(transportReq.getWeight()) ||
                StringUtils.isEmpty(transportReq.getGoodTypeName())) {
            super.throwException(ResponseEnum.request_error.info("请求参数错误"));
        }

        JSONObject result = new JSONObject();
        boolean show = false;

        // 发货账号是否在专车货主管理中，如果是签约合作商取合作商，如果是非签约合作商，取平台
        TytDispatchCargoOwner owner = tytDispatchCargoOwnerMapper.selectByUserId(transportReq.getOwnerUserId());
        if (Objects.isNull(owner)) {
            owner = tytDispatchCargoOwnerMapper.selectByOwnerName(PLAT_CARGO_OWNER_NAME);
        }
        if (Objects.isNull(owner)) {
            log.warn("isShowDriverDriving代调发货获取是否展示司机驾驶此类货物，获取签约合作商失败，ownerUserId:{}", transportReq.getOwnerUserId());
            super.throwException(ResponseEnum.request_error.info("未获取到签约合作商信息！"));
        }

        if (DrivingAbilityEnum.haveAbility(transportReq.getGoodTypeName())) {
            BigDecimal weight = new BigDecimal(transportReq.getWeight());
            // 获取运费配置
            TytSpecialCarPriceConfig priceConfig = tytSpecialCarPriceConfigMapper.selectMatchPriceConfig(owner.getId(),
                    transportReq.getStartCity(), transportReq.getDestCity(), weight);
            if (Objects.nonNull(priceConfig) && priceConfig.getDrivingFee().compareTo(new BigDecimal("0")) > 0) {
                show = true;
            }
        }

        result.put("show", show);
        return result;
    }

    /**
     * 专车运费测算
     *
     * @param transportReq
     * @return JSONObject
     * <AUTHOR>
     * @date 2024/6/26 10:03
     */
    @Override
    public BigDecimal calculatePrice(TransportPublishReq transportReq) {
        if (Objects.isNull(transportReq.getOwnerUserId()) || StringUtils.isEmpty(transportReq.getStartCity()) ||
                StringUtils.isEmpty(transportReq.getDestCity()) || StringUtils.isEmpty(transportReq.getWeight()) ||
                Objects.isNull(transportReq.getDistanceKilometer())) {
            throwException(ResponseEnum.request_error.info("请求参数错误！"));
        }

        BigDecimal result = new BigDecimal("0");

        // 匹配签约合作商
        Long cargoOwnerId = null;
        TytDispatchCargoOwner owner = tytDispatchCargoOwnerMapper.selectByUserId(transportReq.getOwnerUserId());
        if (Objects.nonNull(owner) && Objects.nonNull(owner.getCooperativeId()) && owner.getCooperativeId() != 0) {
            cargoOwnerId = owner.getCooperativeId();
        } else {
            TytDispatchCooperative cooperative = tytDispatchCooperativeMapper.selectByOwnerName(PLAT_CARGO_OWNER_NAME);
            if (Objects.nonNull(cooperative)) {
                cargoOwnerId = cooperative.getId();
            }
        }
        if (Objects.isNull(cargoOwnerId) || cargoOwnerId == 0) {
            log.info("专车运费计算未匹配到签约合作商，ownerUserId:{}, startCity:{}, destCity:{}, weight:{}", transportReq.getOwnerUserId(),
                    transportReq.getStartCity(), transportReq.getDestCity(), transportReq.getWeight());
            return result;
        }

        CalculatePriceBean calculatePriceBean = CalculatePriceBean.builder()
                .cargoOwnerId(cargoOwnerId)
                .startCity(transportReq.getStartCity())
                .destCity(transportReq.getDestCity())
                .weight(transportReq.getWeight())
                .distanceKilometer(transportReq.getDistanceKilometer())
                .driverDriving(transportReq.getDriverDriving())
                .otherFee(transportReq.getOtherFee())
                .infoFee(transportReq.getInfoFee())
                .build();
        TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.SPECIAL_CAR_CALCULATE_PRICE,
                EntityUtil.entityToMap(calculatePriceBean), null);
        log.info("专车运费计算，plat返回结果：{}", JSONObject.toJSONString(tytResultMsgBean));
        if (tytResultMsgBean.isSuccess()) {
            JSONObject priceJson = JSONObject.parseObject(tytResultMsgBean.getData().toString());
            String price = priceJson.getString("price");
            if (StringUtils.isNotEmpty(price)) {
                result = new BigDecimal(price);
            }
        }

        return result;
    }

    /**
     * 专车改派
     *
     * @param bean
     * @return void
     * <AUTHOR>
     * @date 2024/6/28 18:15
     */
    @Override
    public void assignCarForGoods(AssignCarForGoodsBean bean) {
        if (Objects.isNull(bean.getGoodsId()) || StringUtils.isEmpty(bean.getCarCellPhone())) {
            throwException(ResponseEnum.request_error.info("改派货源ID或改派司机不能为空"));
        }

        TransportPublishReq req = new TransportPublishReq();
        req.setCarTelPhone(bean.getCarCellPhone());
        UserBaseVO userBaseVO = checkCarIsExist(req.getCarTelPhone());
        if (Objects.isNull(userBaseVO)) {
            throwException(ResponseEnum.request_error.info("改派司机不存在"));
        }
        Long carUserId = userBaseVO.getUserId();
        req.setCarUserid(carUserId);

        TytTransportMain transportMain = tytTransportMainMapper.selectByPrimaryKey(bean.getGoodsId());
        if (Objects.isNull(transportMain) || TransportStatusEnum.active.getCode() != transportMain.getStatus()) {
            throwException(ResponseEnum.request_error.info("改派货源不存在或已下架"));
        }
        TytSpecialCarDispatchFailure failure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(bean.getGoodsId());
        if (!ExcellentEnum.SPECIAL.getCode().equals(transportMain.getExcellentGoods()) && Objects.isNull(failure)) {
            throwException(ResponseEnum.request_error.info("该货源为非专车货源，不支持改派"));
        }
        if (Objects.nonNull(transportMain.getCtime()) && !DateUtil.isToday(transportMain.getCtime().getTime())) {
            throwException(DispatchResponseEnum.transport_error.info("该货源已过期"));
        }
        if (Objects.equals(carUserId, transportMain.getUserId())) {
            throwException(ResponseEnum.request_error.info("指派车辆和货主不能是同一个账号"));
        }
        boolean inBlack = checkCarUserInBlack(carUserId, transportMain.getExcellentGoods());
        if (inBlack) {
            throwException(DispatchResponseEnum.car_in_blacklist.info());
        }
        TytTransport transport = new TytTransport();
        BeanUtils.copyProperties(transportMain, transport);

        extracted(req, carUserId, transport);

        if (Objects.nonNull(bean.getDispatchDetailId())) {
            tytSpecialCarDispatchDetailMapper.updateManualAssignStatus(bean.getDispatchDetailId());
        }
    }

    @Override
    public TransportPublishVo publishTransportWithMq(TransportPublishReq transportReq, TransportMainService transportMainService) {
        // 如果是运满满货源，发货时先校验状态
        checkYmmStatus(transportReq);

        //如果有指派司机，校验指派司机在user表中是否存在
        Long carUserId = checkCarIsExist(transportReq);
        transportReq.setCarUserid(carUserId);
        if (Objects.nonNull(carUserId)) {
            // 校验司机是否被拉黑
            if (checkCarUserInBlack(carUserId, transportReq.getExcellentGoods())) {
                throw TytException.createException(DispatchResponseEnum.car_in_blacklist.info());
            }
        }

        //必须要经过切面
        TytTransport tytTransport = transportMainService.publishTransportInTrans(transportReq);

        // 如果发布成功，优车货源添加发布时间，供货源下放使用
        if (transportReq.getExcellentGoods() != null && transportReq.getExcellentGoods() == 1) {
            redisCacheUtil.setObject(EXCELLENT_GOODS_PUBLISH_TIME + tytTransport.getSrcMsgId(), System.currentTimeMillis(), 24 * 60 * 60, TimeUnit.SECONDS);
        }

        if (carUserId != null) {
            extracted(transportReq, carUserId, tytTransport);
        } else {
            // 未指派司机，专车发货需要自动派单
            if (ExcellentEnum.SPECIAL.getCode().equals(transportReq.getExcellentGoods())) {
                autoAssignOrderForSpecialCar(tytTransport);
            }
        }

        //开票货源记录发布时货主的企业信息
        if (transportReq.getInvoiceTransport() != null && transportReq.getInvoiceTransport() == INVOICE_TRANSPORT) {
            userService.saveInvoiceTransportEnterpriseData(transportReq.getOwnerUserId(), tytTransport.getSrcMsgId()
                    , transportReq.getInvoiceSubjectId(), transportReq.getServiceProviderCode(), tytTransport.getEnterpriseTaxRate()
                    , transportReq.getConsigneeName(), transportReq.getConsigneeTel(), transportReq.getConsigneeEnterpriseName());
        }

        this.sendNullifyMessage2MQ(tytTransport);

        // 设置响应返回
        TransportPublishVo publishResult = new TransportPublishVo();
        publishResult.setTsId(tytTransport.getId());
        publishResult.setSrcMsgId(tytTransport.getSrcMsgId());
        publishResult.setTsOrderNo(tytTransport.getTsOrderNo());

        return publishResult;
    }

    /**
     * 校验司机是否被拉黑
     *
     * @param carUserId
     * @param excellentGoods
     * @return
     */
    @Override
    public boolean checkCarUserInBlack(Long carUserId, Integer excellentGoods) {
        if (Objects.equals(excellentGoods, ExcellentEnum.SPECIAL.getCode())) {
            log.info("校验司机是否被拉黑，请求参数：{}", carUserId);
            Map<String, String> parameterMap = new HashMap<>();
            parameterMap.put("userId", String.valueOf(carUserId));
            TytResultMsgBean resultMsgBean = platHttpService.doGet(PlatApiConstant.CAR_IN_BLACKLIST,
                    parameterMap, null);
            log.info("校验司机是否被拉黑，plat返回结果：{}", JSONObject.toJSONString(resultMsgBean));
            if (Objects.nonNull(resultMsgBean) && resultMsgBean.isSuccess()) {
                // true ：白名单  false：黑名单
                return !(boolean) resultMsgBean.getData();
            } else {
                throwException(ResponseEnum.sys_error.info());
            }
        }
        return false;
    }

    /**
     * 专车发货自动派单
     *
     * @param tytTransport
     */
    private void autoAssignOrderForSpecialCar(TytTransport tytTransport) {
        TytInternalEmployee sessionEmployee = getSessionEmployee();
        AutoAssignOrderBean assignOrderBean = AutoAssignOrderBean.builder()
                .tsId(tytTransport.getSrcMsgId())
                .startCity(tytTransport.getStartCity())
                .destCity(tytTransport.getDestCity())
                .driverDriving(tytTransport.getDriverDriving())
                .goodsTypeName(tytTransport.getGoodTypeName())
                .cargoOwnerId(tytTransport.getCargoOwnerId())
                .build();
        Long userId = tytUserMapper.getUserIdByLoginPhoneNo(sessionEmployee.getLoginPhoneNo());
        assignOrderBean.setUserId(userId);
        log.info("专车发货自动派单，请求参数：{}", JSONObject.toJSONString(assignOrderBean));
        TytResultMsgBean resultMsgBean = platHttpService.doGet(PlatApiConstant.SPECIAL_CAR_AUTO_ASSIGN_ORDER,
                EntityUtil.entityToMap(assignOrderBean), null);
        log.info("专车发货自动派单，plat返回结果：{}", JSONObject.toJSONString(resultMsgBean));
    }

    //宏信货源指派司机，生成待支付订单
    private void extracted(TransportPublishReq transportReq, Long carUserId, TytTransport tytTransport) {
        SaveWayBillReq saveWayBillReq = new SaveWayBillReq();

        saveWayBillReq.setUserId(carUserId);
        saveWayBillReq.setTelephone(transportReq.getCarTelPhone());
        saveWayBillReq.setGoodsId(tytTransport.getSrcMsgId());
        saveWayBillReq.setAgencyMoney(tytTransport.getInfoFee() == null ? 0L : tytTransport.getInfoFee().longValue());
        saveWayBillReq.setCarOwnerPayType(99);
        saveWayBillReq.setTecServiceFee(tytTransport.getTecServiceFee() == null ? 0L : tytTransport.getTecServiceFee().longValue());
        saveWayBillReq.setCarriageFee(StringUtils.isNotBlank(tytTransport.getPrice()) ? Integer.parseInt(tytTransport.getPrice()) : 0);

        //如果指派了司机，则直接给该司机生成待支付订单
        TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.save_way_bill, EntityUtils.entityToMap(saveWayBillReq), null);

        if (tytResultMsgBean == null) {
            super.throwException(ResponseEnum.request_error.info("指派车辆失败"));
        }

        // 指派次数处理世博在生成订单mq处理
        // if (tytTransport.getSourceType() == 5) {
        //     //指派次数+1
        //     tytTransportMainMapper.assignTransportToCarUserSuccess(transportReq.getCarTelPhone());
        // }

        assignTransportToCarUserPush(tytTransport, carUserId);
    }


    private void assignTransportToCarUserPush(TytTransport transportMain, Long carUserId) {
        try {
            String title = "货方已指派您接单";

            String startAddress = com.tyt.messagecenter.core.utils.CityUtil.createAddressInfo(transportMain.getStartCity(), transportMain.getStartArea());
            String destAddress = com.tyt.messagecenter.core.utils.CityUtil.createAddressInfo(transportMain.getDestCity(), transportMain.getDestArea());

            String shortTaskContent = this.getShortTaskContent(transportMain.getTaskContent());

            //通知的
            String contentTmp = "%s-%s的%s货方已指派您接单，点击此信息或进入“订单—待确认”立即支付订金，防止他人抢单！";
            String contentText = String.format(contentTmp, startAddress, destAddress, shortTaskContent);

            MessagePushBase messagePushBase = new MessagePushBase();
            //添加推送用户
            messagePushBase.addUserId(carUserId);
            messagePushBase.setTitle(title);
            messagePushBase.setRemarks(title);
            messagePushBase.setCarPush((short) 1);

            //push消息
            NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
            notifyMessage.setContent(contentText);
            notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
            notifyMessage.addNativeParameter("id", transportMain.getSrcMsgId() + "");

            //站内信
            String msgContentTmp = "%s-%s的%s货方已指派您接单，请进入“订单—待确认”立即支付订金，防止他人抢单！";
            String msgContentText = String.format(msgContentTmp, startAddress, destAddress, shortTaskContent);
            NewsMessagePush newsMessagePush = NewsMessagePush.createByPushBase(messagePushBase);
            newsMessagePush.setContent(msgContentText);
            newsMessagePush.setSummary(msgContentText);

            messageCenterPushService.sendMultiMessage(null, newsMessagePush, notifyMessage);
        } catch (Exception e) {
            log.info("宏信货源指派司机 push通知司机失败 货源ID:{} 司机ID:{}", transportMain.getSrcMsgId(), carUserId);
        }
    }

    /**
     * 缩减货物内容
     *
     * @param taskContent
     * @return
     */
    private String getShortTaskContent(String taskContent) {
        String result = taskContent;
        if (StringUtils.isNotBlank(taskContent) && taskContent.length() > 7) {
            result = taskContent.substring(0, 7) + "...";
        }
        return result;
    }

    private Long checkCarIsExist(TransportPublishReq transportReq) {
        if (StringUtils.isNotBlank(transportReq.getCarTelPhone())) {
            UserBaseVO userAuthByPhone = tytUserMapper.getUserAuthByPhone(transportReq.getCarTelPhone());
            if (userAuthByPhone == null || userAuthByPhone.getUserId() == null) {
                throw TytException.createException(DispatchResponseEnum.car_no_exist.info());
            }
            return userAuthByPhone.getUserId();
        }
        return null;
    }

    private UserBaseVO checkCarIsExist(String carTelPhone) {
        if (StringUtils.isNotBlank(carTelPhone)) {
            UserBaseVO userAuthByPhone = tytUserMapper.getUserAuthByPhone(carTelPhone);
            if (userAuthByPhone == null || userAuthByPhone.getUserId() == null) {
                throw TytException.createException(DispatchResponseEnum.car_no_exist.info());
            }
            return userAuthByPhone;
        }
        return null;
    }

    private void checkYmmStatus(TransportPublishReq transportReq) {
        if (transportReq.getSourceType() != null && transportReq.getSourceType().equals(SourceTypeEnum.ymm.getCode())) {
            //校验该货源是否已发布
            TytTransportMbMerge transportMbMerge = transportYMMService.getTransportMbMerge(transportReq.getCargoId());
            if (transportMbMerge != null) {
                throw TytException.createException(DispatchResponseEnum.transport_published.info());
            }
            // 校验是否是下架状态
            TransportYMMListVO transportYMM = transportYMMService.getTransportYMM(transportReq.getCargoId());
            if (transportYMM == null) {
                throw TytException.createException(DispatchResponseEnum.data_not_exist.info());
            }
            if (transportReq.getRefundFlag().intValue() != transportYMM.getDepositReturn()) {
                throw TytException.createException(DispatchResponseEnum.not_allow_update_refundStatus.info());
            }
            if (transportReq.getInfoFee().compareTo(transportYMM.getDepositAmt()) != 0) {
                throw TytException.createException(DispatchResponseEnum.not_allow_update_infoFee.info());
            }
            if (transportYMM.getDelFlag() == 1) {
                throw TytException.createException(DispatchResponseEnum.transport_off.info());
            }
            if (transportReq.getCargoVersion().intValue() != transportYMM.getCargoVersion().intValue()) {
                throw TytException.createException(DispatchResponseEnum.transport_expire.info());
            }
        }
    }


    private CarryPriceReq createCarryPriceReq(SuggestPriceReq priceReq) {
        CarryPriceReq carryPriceReq = new CarryPriceReq();
        carryPriceReq.setStartProvince(priceReq.getStartProvince());
        carryPriceReq.setStartCity(priceReq.getStartCity());
        carryPriceReq.setStartArea(priceReq.getStartArea());
        carryPriceReq.setDestProvince(priceReq.getDestProvince());
        carryPriceReq.setDestCity(priceReq.getDestCity());
        carryPriceReq.setDestArea(priceReq.getDestArea());
        carryPriceReq.setGoodsName(priceReq.getGoodsName());
        carryPriceReq.setGoodsWeight(priceReq.getGoodsWeight());
        carryPriceReq.setGoodsLength(priceReq.getGoodsLength());
        carryPriceReq.setGoodsWide(priceReq.getGoodsWide());
        carryPriceReq.setGoodsHigh(priceReq.getGoodsHigh());
        carryPriceReq.setDistance(priceReq.getDistance());
        carryPriceReq.setGoodCarPriceTransport(priceReq.getGoodCarPriceTransport());
        /**
         *   优车新增逻辑
         */
        if (priceReq.getExcellentGoods() != null && priceReq.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
            carryPriceReq.setSource(PriceSourceEnum.excellent.getSource());
        } else {
            carryPriceReq.setSource(PriceSourceEnum.reference.getSource());
        }
        String distance = priceReq.getDistance();
        if ("0".equals(distance)) {
            distance = null;
        }
        carryPriceReq.setDistance(distance);
        carryPriceReq.setUserId(priceReq.getUserId());

        return carryPriceReq;
    }

    @Override
    public CarryPriceVo getSuggestPrice(SuggestPriceReq suggestPriceReq) {

        CarryPriceReq carryPriceReq = this.createCarryPriceReq(suggestPriceReq);

        CarryPriceVo carryPrice = null;
        try {
            carryPrice = carryPriceService.getCarryPrice(carryPriceReq);
        } catch (Exception e) {
            log.error("", e);
        }

        return carryPrice;
    }

    @Override
    public List<String> getLastPhoneList() {

        TytInternalEmployee sessionEmployee = super.getSessionEmployee();

        Long sessionEmployeeId = sessionEmployee.getId();

        List<String> phoneList = new ArrayList<>();

        LocalDate currentDate = LocalDate.now();
        LocalDate oneMonthAgo = currentDate.minusMonths(1);
        Date dateOneMonthAgo = Date.from(oneMonthAgo.atStartOfDay(ZoneId.systemDefault()).toInstant());

        TytTransportMain transportMain = tytTransportDispatchMapper.getLastTransportNoYmmAndOwner(sessionEmployeeId, dateOneMonthAgo);

        if (transportMain != null) {
            String tel = transportMain.getTel();
            String tel3 = transportMain.getTel3();
            String tel4 = transportMain.getTel4();

            if (StringUtils.isNotBlank(tel)) {
                phoneList.add(tel);
            }
            if (StringUtils.isNotBlank(tel3)) {
                phoneList.add(tel3);
            }
            if (StringUtils.isNotBlank(tel4)) {
                phoneList.add(tel4);
            }
        }
        return phoneList;
    }

    @Override
    public Boolean validateTransportWord(String fieldName, String text) {

        //SourceGroupCodeEnum groupCodeEnum = SourceGroupCodeEnum.valueOf(fieldName);

        String sensitiveWord = this.checkTransportWord(text, SourceGroupCodeEnum.remark, SourceGroupCodeEnum.tailCarStyle, SourceGroupCodeEnum.tailCarType);

        return StringUtils.isBlank(sensitiveWord);
    }

    /**
     * 拼接 pc old content
     *
     * @param transport
     * @return
     */
    private String getPcOldContent(TytTransport transport) {
        String pcOldContent = transport.getStartPoint().trim() + "---" + transport.getDestPoint().trim() + " " + transport.getTaskContent().trim();
        return pcOldContent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TytTransport directPublishInTrans(Long srcMsgId, Long backendId, TytTransportMain oldTransportMain) {
        if (srcMsgId == null) {
            super.throwException(ResponseEnum.request_error.info());
        }
        TytInternalEmployee sessionEmployee = super.getSessionEmployee();

        Date nowTime = new Date();
        Date startDayTime = DateUtil.startOfDay(nowTime);

        if (oldTransportMain == null) {
            super.throwException(ResponseEnum.request_error.info("货源不存在！"));
        }

        TytTransportDispatch transportDispatch = transportDispatchService.getTransportDispatch(srcMsgId);

        if (transportDispatch == null) {
            super.throwException(ResponseEnum.request_error.info("该货源非调度货源！"));
        }

        this.checkBackendValid(srcMsgId, backendId);

        //V个人代调需求  将已删除的单子直接发布 变更一下删除状态
        if (oldTransportMain.getIsDelete() == (short) 1) oldTransportMain.setIsDelete((short) 0);

        TytTransport transportNew = ConvertUtil.beanConvert(oldTransportMain, new TytTransport());
        transportNew.setId(null);

        String timeErrorMsg = "装卸货时间已过期";
        if (transportNew.getLoadingTime() != null && nowTime.compareTo(transportNew.getLoadingTime()) > 0) {
            super.throwException(ResponseEnum.request_error.info(timeErrorMsg));
        }
        if (transportNew.getUnloadTime() != null && new Date().compareTo(transportNew.getUnloadTime()) > 0) {
            super.throwException(ResponseEnum.request_error.info(timeErrorMsg));
        }

        Long ownerUserId = transportNew.getUserId();

        Example exa = new Example(CsMaintainedCustom.class);
        exa.and().andEqualTo("customId", ownerUserId);

        CsMaintainedCustom csMaintainedCustom = csMaintainedCustomMapper.selectOneByExample(exa);

        if (csMaintainedCustom == null || !EmpowerStatusEnum.authed.equalsCode(csMaintainedCustom.getEmpowerStatus().intValue())) {
            super.throwException(ResponseEnum.request_error.info("货主未授权发货！"));
        }

        //开票货源直接发布校验开票规则
        if (transportNew.getInvoiceTransport() != null && transportNew.getInvoiceTransport() == 1) {
            Long invoiceSubjectId = tytUserMapper.getInvoiceTransportEnterpriseLogInvoiceSubjectIdBySrcMsgId(oldTransportMain.getSrcMsgId());

            CheckInvoiceTransportUserAndParam checkInvoiceTransportUserAndParam = new CheckInvoiceTransportUserAndParam(transportNew.getUserId(), transportNew.getDistance().toString(), transportNew.getPrice(), transportNew.getWeight()
                    , transportNew.getLength(), transportNew.getWide(), transportNew.getHigh(), invoiceSubjectId);
            TytResultMsgBean tytResultMsgBean = platHttpService.doGet(PlatApiConstant.check_user_can_publish_invoice_transport_in_do_save_direct_and_check_param, EntityUtils.entityToMap(checkInvoiceTransportUserAndParam), null);
            log.info("开票货源直接发布调用plat开票货源直接发布接口，请求参数{}, 返回结果：{}", JSON.toJSONString(checkInvoiceTransportUserAndParam), JSON.toJSONString(tytResultMsgBean));
            if (tytResultMsgBean != null && !tytResultMsgBean.isSuccess()) {
                super.throwException(ResponseEnum.request_error.info(tytResultMsgBean.getMsg()));
            }

            //如果是开票货源则重新计算企业税率、附加运费和总运费
            tytResultMsgBean = platHttpService.doGet(PlatApiConstant.get_Additional_Price_And_Enterprise_Tax_Rate, EntityUtils.entityToMap(new GetAdditionalPriceParam(transportNew.getUserId().toString(), transportNew.getPrice(), invoiceSubjectId)), null);
            if (tytResultMsgBean != null && tytResultMsgBean.isSuccess()) {
                Map<String, String> data = (Map<String, String>) tytResultMsgBean.getData();
                transportNew.setEnterpriseTaxRate(new BigDecimal(data.get("enterpriseTaxRate")));
                transportNew.setAdditionalPrice(data.get("additionalPrice"));
            }

            TytTransportEnterpriseLog transportEnterpriseLog = tytUserMapper.getInvoiceTransportEnterpriseLogBySrcMsgId(oldTransportMain.getSrcMsgId());
            checkSegmentedPayments(transportEnterpriseLog.getPrepaidPrice(), transportEnterpriseLog.getCollectedPrice(), transportEnterpriseLog.getReceiptPrice(), oldTransportMain.getPrice(), ownerUserId, true);
        }

        TytUser tytUser = tytUserMapper.selectByPrimaryKey(ownerUserId);

        // 判读是不是昨天的数据
        boolean isHistoryGoods = transportNew.getCtime().getTime() < startDayTime.getTime();

        if (!isHistoryGoods) {
            //今日编辑
            Short status = oldTransportMain.getStatus();
            if (!TransportStatusEnum.cancel.equalsCode(status.intValue())) {
                super.throwException(ResponseEnum.request_error.info("该货源未下架，不允许编辑！"));
            }
        }

        //隐藏待支付订单
        transportOrdersService.hideWaitPayOrders(ownerUserId, srcMsgId);

        if (tytUser.getVerifyFlag() != null && tytUser.getVerifyFlag().intValue() != 2) {
            transportNew.setVerifyFlag(tytUser.getVerifyFlag());
        } else {
            transportNew.setVerifyFlag((short) 0);
        }
        /* 兼容老客户 */
        /* 优车 修改昵称 */
        if (transportNew.getExcellentGoods() != null && !transportNew.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
            transportNew.setNickName(StringBaseUtils.formatUserName(tytUser.getUserName(), tytUser.getId()));
        }
        transportNew.setPubQq(tytUser.getQq());
        transportNew.setUploadCellphone(tytUser.getCellPhone());
        /* 添加用户信息 */
        transportNew.setIsCar(tytUser.getIsCar());
        short vipType = 0;
        if (userPermissionService.isVipPublishPermission(tytUser.getId())) {
            vipType = 1;
        }
        transportNew.setUserType(vipType);

        if (tytUser != null && tytUser.getVerifyPhotoSign() != null && tytUser.getVerifyPhotoSign().intValue() == 1) {
            transportNew.setVerifyPhotoSign(tytUser.getVerifyPhotoSign());
        } else {
            transportNew.setVerifyPhotoSign((short) 0);
        }
        transportNew.setUserPart(tytUser.getUserPart());
        transportNew.setRegTime(tytUser.getCtime());

        transportNew.setId(null);
        transportNew.setUserShowName(this.getUserShowName(tytUser));
        transportNew.setClientVersion(DispatchConstant.mock_client_version + "");
        transportNew.setPlatId((short) DispatchConstant.mock_client_sign);
        transportNew.setCtime(nowTime);
        transportNew.setMtime(nowTime);
        transportNew.setPubTime(DateUtil.dateToString(nowTime, DateUtil.hour_format));
        transportNew.setPubDate(nowTime);
        transportNew.setReleaseTime(oldTransportMain.getReleaseTime());
        transportNew.setStatus((short) 1);
        transportNew.setIsShow(1);
        if (oldTransportMain.getSourceType() == SourceTypeEnum.ymm.getCode() || oldTransportMain.getSourceType() == SourceTypeEnum.hong_xin.getCode()) {
            transportNew.setSourceType(oldTransportMain.getSourceType());
        } else {
            transportNew.setSourceType(SourceTypeEnum.dispatch.getCode());
        }
        String pcOldContent = getPcOldContent(transportNew);
        transportNew.setPcOldContent(pcOldContent);

        Integer resendCount = 0;
        if (isHistoryGoods) {
            //历史货源
            transportNew.setInfoStatus("0");
            transportNew.setTsOrderNo(tytSequenceService.generateSequenceForDate(DispatchConstant.TABLE_WAYBILL_NAME));
            transportNew.setResendCounts(0);

            transportNew.setReleaseTime(nowTime);
        }

        // 专车发货直接发布校验
        if (Objects.equals(ExcellentEnum.SPECIAL.getCode(), oldTransportMain.getExcellentGoods())) {
            checkForSpecialCarDirectPublish(transportNew, oldTransportMain);
        }

        TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.selectBySrcMsgId(srcMsgId);
        if (mainExtend == null) {
            mainExtend = new TytTransportMainExtend();
            mainExtend.setSrcMsgId(srcMsgId);
        }

        TytTransportExtend transportExtend = new TytTransportExtend();
        transportExtend.setSrcMsgId(srcMsgId);

        TytTransport saveTransport = this.savePublishTransport(transportNew, oldTransportMain, mainExtend, transportExtend);

        //如果是小程序那边来的货，需要更改小程序表的状态
        if (backendId != null) {
            dealBackendTransport(backendId, saveTransport);
        }
        if (isHistoryGoods && Objects.equals(ExcellentEnum.SPECIAL.getCode(), oldTransportMain.getExcellentGoods())) {
            // 历史专车货源直接发布，需要插入五级地址表信息
            GoodsAddressLevelRecord oldLevelRecord = goodsAddressLevelRecordMapper.selectBySrcMsgId(oldTransportMain.getSrcMsgId());
            goodsAddressLevelRecordMapper.insertGoodsAddress(saveTransport.getSrcMsgId(), oldLevelRecord.getDistanceKilometer(),
                    oldLevelRecord.getOtherFee());
        }

        //giveGoodsUserService.saveGiveGoodsUser(sessionManagerId, transportReq.getGiveGoodsPhone(), transportReq.getDispatchIdentityCode());

        //保存调度发货记录
        transportDispatchService.saveDispatch(saveTransport, transportDispatch.getOwnerFreight(), transportDispatch.getInfoFeeDiff(),
                transportDispatch.getGiveGoodsPhone(), transportDispatch.getGiveGoodsName(), sessionEmployee, csMaintainedCustom, tytUser, null);

        // 如果是运满满货源，同步添加到运满满关系表中
        if (oldTransportMain.getSourceType() != null && oldTransportMain.getSourceType().equals(SourceTypeEnum.ymm.getCode())) {
            // 如果是过期货源，需要用老的id拿到cargoid
            if (isHistoryGoods) {
                List<TytTransportMbMerge> existBySrcMsgIds = transportYMMService.findExistBySrcMsgIds(Arrays.asList(srcMsgId));
                if (CollectionUtils.isEmpty(existBySrcMsgIds)) {
                    super.throwException(DispatchResponseEnum.data_not_exist.info());
                }
                TytTransportMbMerge tytTransportMbMerge = existBySrcMsgIds.get(0);
                transportYMMService.saveOrUpdateMbMerge(saveTransport.getSrcMsgId(), tytTransportMbMerge.getCargoId(), tytTransportMbMerge.getCargoVersion(), null);
            } else {
                transportYMMService.saveOrUpdateMbMerge(saveTransport.getSrcMsgId(), null, null, null);
            }
        }

        String todayStr = DateUtil.dateToString(nowTime, DateUtil.day_format);

        // 将发布成功的货源唯一值放到缓存中；
        this.addTransportHashCode(saveTransport.getHashCode(), todayStr);

        return saveTransport;
    }

    /**
     * 专车发货直接发布校验
     *
     * @param transportNew
     * @param oldTransportMain
     */
    private void checkForSpecialCarDirectPublish(TytTransport transportNew, TytTransportMain oldTransportMain) {
        // 专车货主是否存在并且已授权
        List<TytDispatchCargoOwner> cargoOwners = tytDispatchCargoOwnerMapper.selectAuthorizedRecords(oldTransportMain.getUserId());
        if (CollectionUtils.isEmpty(cargoOwners)) {
            throwException(ResponseEnum.base_error.info("该货源货主不是专车货主或未授权"));
        }
        // 匹配最新运费及规则，运费重新计算
        TransportPublishReq req = new TransportPublishReq();
        req.setOwnerUserId(oldTransportMain.getUserId());
        req.setStartCity(oldTransportMain.getStartCity());
        req.setDestCity(oldTransportMain.getDestCity());
        req.setWeight(oldTransportMain.getWeight());
        req.setDriverDriving(oldTransportMain.getDriverDriving());
        req.setInfoFee(oldTransportMain.getInfoFee());
        GoodsAddressLevelRecord bean = goodsAddressLevelRecordMapper.selectBySrcMsgId(oldTransportMain.getSrcMsgId());
        if (Objects.isNull(bean) || Objects.isNull(bean.getDistanceKilometer())) {
            throwException(ResponseEnum.base_error.info("获取该货源公里数失败"));
        }
        req.setDistanceKilometer(bean.getDistanceKilometer());
        req.setOtherFee(bean.getOtherFee());
        BigDecimal price = calculatePrice(req);
        if (price.compareTo(new BigDecimal("0")) == 0) {
            throwException(ResponseEnum.base_error.info("专车货源运费测算失败，不能直接发布"));
        }
        transportNew.setPrice(price.toString());
    }

    @Override
    public TransportPublishVo directPublishWithMq(Long srcMsgId, Long backendId, TytTransportMain oldTransportMain, TransportMainService transportMainService) {

        checkYmmForDirectPublish(oldTransportMain, srcMsgId);

        //必须要经过切面
        TytTransport tytTransport = transportMainService.directPublishInTrans(srcMsgId, backendId, oldTransportMain);

        this.sendNullifyMessage2MQ(tytTransport);

        // 专车货源直接发布需要自动派单
        if (Objects.equals(ExcellentEnum.SPECIAL.getCode(), oldTransportMain.getExcellentGoods())) {
            autoAssignOrderForSpecialCar(tytTransport);
        }

        // 设置响应返回
        TransportPublishVo publishResult = new TransportPublishVo();
        publishResult.setTsId(tytTransport.getId());
        publishResult.setSrcMsgId(tytTransport.getSrcMsgId());
        publishResult.setTsOrderNo(tytTransport.getTsOrderNo());

        return publishResult;
    }

    private void checkYmmForDirectPublish(TytTransportMain oldTransportMain, Long srcMsgId) {
        /**
         *  直接发布 版本无变化不用拦截 前端约定提示
         */
        if (SourceTypeEnum.ymm.getCode() == oldTransportMain.getSourceType()) {
            TytTransportMbMerge tytTransportMbMerge = tytTransportMbMergeMapper.selectBySrcMsgIdAndStatus(srcMsgId);
            if (tytTransportMbMerge == null) {
                throw TytException.createException(DispatchResponseEnum.data_not_exist.info());
            }
            if (tytTransportMbMerge.getStatus() == 0) {
                throw TytException.createException(DispatchResponseEnum.transport_published.info());
            }
            TytTransportMbMerge tytTransportMbMergeByCargoId = tytTransportMbMergeMapper.selectByCargoId(tytTransportMbMerge.getCargoId());
            if (tytTransportMbMergeByCargoId != null) {
                throw TytException.createException(DispatchResponseEnum.transport_published.info());
            }

            TransportYMMListVO transportYMMListVO = tytTransportYMMMapper.selectYMMCargoById(tytTransportMbMerge.getCargoId());
            if (transportYMMListVO.getDelFlag() == 1) {
                throw TytException.createException(DispatchResponseEnum.transport_off.info());
            }
            if (!tytTransportMbMerge.getCargoVersion().equals(transportYMMListVO.getCargoVersion())) {
                throw TytException.createException(ResponseEnum.sys_error.info("运满满货源信息有变更，不允许发布"));
            }
        }
    }

    @Override
    public TransportPublishVo refreshTransport(Long srcMsgId, Long backendId, TransportMainService transportMainService) {

        String lockTsKey = CommonUtil.joinRedisKey(RedisConstant.Transport.refresh_lock_key, srcMsgId + "");

        Boolean lockResult = redisCacheUtil.getRedisTemplate().opsForValue().setIfAbsent(lockTsKey, srcMsgId, 60, TimeUnit.SECONDS);

        if (!lockResult) {
            super.throwException(ResponseEnum.request_error.info("操作太过频繁，请休息一下！"));
        }

        TransportPublishVo publishVo;
        try {
            String refreshKey = CommonUtil.joinRedisKey(RedisConstant.Transport.refresh_interval_key, srcMsgId + "");

            Object object = redisCacheUtil.getObject(refreshKey);
            if (object != null) {
                super.throwException(ResponseEnum.request_error.info("刷新太快了，请休息一下！"));
            }
            updateGoodsStatus(srcMsgId, refresh);

            TytTransportMain oldTransportMain = transportMainService.getTransportMainById(srcMsgId);
            publishVo = this.directPublishWithMq(srcMsgId, backendId, oldTransportMain, transportMainService);

            redisCacheUtil.setObject(refreshKey, srcMsgId, 5, TimeUnit.MINUTES);
        } finally {
            redisCacheUtil.expire(lockTsKey, 5, TimeUnit.SECONDS);
        }

        return publishVo;
    }

    @Override
    public TytTransportMain getTransportMainById(Long srcMsgId) {

        return tytTransportMainMapper.selectByPrimaryKey(srcMsgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePrice(Long srcMsgId, String price, BigDecimal tecServiceFee) {
        boolean needReleaseRedisLock = false;
        if (srcMsgId != null && (price != null || tecServiceFee != null)) {
            try {
                //添加获取锁
                if (lockTransportOpt(srcMsgId)) {
                    log.info("【改价】- 获取货源锁【{}】成功", srcMsgId);
                    needReleaseRedisLock = true;

                    TytTransportMain transportMainById = this.getTransportMainById(srcMsgId);
                    TytTransport tytTransport = new TytTransport();
                    BeanUtils.copyProperties(transportMainById, tytTransport);

                    if (StringUtils.isNotBlank(price)) {
                        tytTransport.setPrice(price);
                    }

                    if (tecServiceFee != null) {
                        tytTransport.setTecServiceFee(tecServiceFee);
                    }

                    MeetCommissionRulesResult meetCommissionRules = new MeetCommissionRulesResult();
                    boolean isCommissionTransport = false;
                    TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputeResult = null;
                    if (tytTransport.getSourceType().equals(SourceTypeEnum.ymm.getCode()) || (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 2)) {
                        tytTecServiceFeeConfigToComputeResult = makeTecServiceFeeData(tytTransport, transportMainById, meetCommissionRules);
                        if (tytTecServiceFeeConfigToComputeResult != null) {
                            isCommissionTransport = true;
                        }
                    }

                    tecServiceFee = tytTransport.getTecServiceFee();

                    tytTransportMainMapper.changeMainPrice(srcMsgId, price, tecServiceFee, tytTransport.getLabelJson(), new Date());
                    tytTransportMapper.changFollowPrice(srcMsgId, price, tecServiceFee, tytTransport.getLabelJson(), new Date());

                    // 重新计算技术服务费，并记录到五级地址表
                    String biDataJsonKey = getBiDataJsonKey(tytTransport.getSrcMsgId());
                    String biDataJsonString = stringRedisTemplate.opsForValue().get(biDataJsonKey);
                    biDataJsonString = saveTecServiceFee(isCommissionTransport, biDataJsonString, meetCommissionRules, tytTransport, tytTecServiceFeeConfigToComputeResult);

                    stringRedisTemplate.opsForValue().set(biDataJsonKey, biDataJsonString, 60 * 60 * 60, TimeUnit.SECONDS);
                }
            } catch (Exception e) {
                log.error("【改价】- 改价失败，请确认具体原因，参数：{},{},{}", srcMsgId, price, tecServiceFee);
            } finally {
                //释放锁
                if (needReleaseRedisLock) {
                    unlockTransportOpt(srcMsgId);
                    log.info("【改价】- 释放货源锁【{}】成功", srcMsgId);
                }
            }
        }
    }

    /**
     * 获取redis中要存入五级地址表的json字符串
     *
     * @param srcMsgId
     * @return
     */
    private String getBiDataJsonKey(Long srcMsgId) {
        return CommonUtil.joinRedisKey("tyt:cache:bi:data", srcMsgId.toString(), DateUtil.dateToString(new Date(), DateUtil.day_format_short));
    }


    @Override
    public WebResult editPublishInterceptor(Long scrMsgId) {
        WebResult webResult = new WebResult();
        List<TytTransportMain> orderAndGoodsByPaid = tytTransportMainMapper.findOrderAndGoodsByPaid(scrMsgId);
        log.info("[编辑再发布]拦截存在已经支付货源订单：{}", JSON.toJSONString(orderAndGoodsByPaid));
        if (CollectionUtils.isEmpty(orderAndGoodsByPaid)) {
            webResult.setData("0");
            return webResult;
        }
        webResult.setData("1");
        webResult.setMsg("订单已支付，当日不允许编辑再发布");
        // webResult.setCode("200");
        return webResult;
    }

    @Override
    public void youcheMakeNickName(TytTransport transport, TytUser tytUser) {
        //如果是优车并且该货主不在AB测试中才将昵称赋值为X老板
        if (tytUser == null || tytUser.getId() == null) {
            return;
        }
        ArrayList<String> codes = new ArrayList<>();
        codes.add(AbtestServiceImpl.YOUCHE_IS_SHOW_USERNAMEABTEST_CODE);
        List<TytAbtestConfigVo> userTypeList = abtestService.getUserTypeList(codes, tytUser.getId());
        boolean isShowUserName = false;
        TytAbtestConfigVo tytAbtestConfigVo = userTypeList.get(0);
        if (AbtestServiceImpl.YOUCHE_IS_SHOW_USERNAMEABTEST_CODE.equals(tytAbtestConfigVo.getCode()) && tytAbtestConfigVo.getType() == 1) {
            isShowUserName = true;
        }
        if (isShowUserName) {
            transport.setNickName(StringBaseUtils.formatUserName(tytUser.getUserName(), tytUser.getId()));
        } else {
            String surname = StringUtils.isBlank(tytUser.getTrueName()) ? "" : Character.toString(tytUser.getTrueName().charAt(0));
            transport.setNickName(surname + LB);
        }
    }


    @Override
    public List<Long> getSimilarTransport(String startCity, String destCity, Double minWeigh, Double maxWeight, Date date) {
        return tytTransportMainMapper.getSimilarTransport(startCity, destCity, minWeigh, maxWeight, date);
    }

    @Override
    public List<String> goodsTypeByUserId(List<Long> idList, String beginTime) {
        return tytTransportMainMapper.goodsTypeByUserId(idList, beginTime);
    }

    @Override
    public TransportPublishVo changeGoodsType(Long srcMsgId, Integer excellentGoods, Integer instantGrab) {
        String lockTsKey = CommonUtil.joinRedisKey(RedisConstant.Transport.CHANGE_GOODS_TYPE_LOCK_KEY, srcMsgId + "");
        Boolean lockResult = redisCacheUtil.getRedisTemplate().opsForValue().setIfAbsent(lockTsKey, srcMsgId, 5, TimeUnit.SECONDS);
        if (!lockResult) {
            super.throwException(ResponseEnum.request_error.info("操作太过频繁，请休息一下！"));
        }
        // 撤销货源之前，获取当前工单状态和结单时间，撤销完成以后需要还原回去
        TytSpecialCarDispatchFailure failure = tytSpecialCarDispatchFailureMapper.selectBySrcMsgId(srcMsgId);
        updateGoodsStatus(srcMsgId, change_goods_type);

        TytTransportMain oldTransportMain = getTransportMainById(srcMsgId);
        if (oldTransportMain == null) {
            super.throwException(ResponseEnum.request_error.info("货源不存在！"));
        }

        Short status = oldTransportMain.getStatus();
        if (!TransportStatusEnum.cancel.equalsCode(status.intValue())) {
            super.throwException(ResponseEnum.request_error.info("该货源未下架，不允许编辑！"));
        }

        TytTransport transport = new TytTransport();

        BeanUtils.copyProperties(oldTransportMain, transport);


        Date nowTime = new Date();
        transport.setId(null);
        transport.setMtime(nowTime);
        transport.setPubTime(DateUtil.dateToString(nowTime, DateUtil.hour_format));
        transport.setPubDate(nowTime);
        transport.setStatus((short) 1);
        transport.setSrcMsgId(oldTransportMain.getId());
        transport.setExcellentGoods(excellentGoods);

        JSONObject jsonObject = JSON.parseObject(oldTransportMain.getLabelJson());
        if (jsonObject != null) {
            jsonObject.put("instantGrab", 1);
            transport.setLabelJson(jsonObject.toJSONString());
        } else {
            transport.setLabelJson("{\"instantGrab\":1}");
        }


        // 判断抽佣标识
        MeetCommissionRulesResult meetCommissionRules = new MeetCommissionRulesResult();
        TytTecServiceFeeConfigToComputResult tytTecServiceFeeConfigToComputeResult = makeTecServiceFeeData(transport, oldTransportMain, meetCommissionRules);
        boolean isCommissionTransport = tytTecServiceFeeConfigToComputeResult != null;


        TytTransportMainExtend mainExtend = tytTransportMainExtendMapper.selectBySrcMsgId(srcMsgId);
        if (mainExtend == null) {
            mainExtend = new TytTransportMainExtend();
            mainExtend.setSrcMsgId(srcMsgId);
        }

        TytTransportExtend transportExtend = new TytTransportExtend();
        transportExtend.setSrcMsgId(srcMsgId);
        /* 保存数据到数据库，缓存 */
        TytTransport newTransport = this.addTransportBusiness(transport, oldTransportMain, TransportSaveModeEnum.modifySave, mainExtend, transportExtend);

        String todayStr = DateUtil.dateToString(nowTime, DateUtil.day_format);

        // 将发布成功的货源唯一值放到缓存中；
        this.addTransportHashCode(newTransport.getHashCode(), todayStr);

        this.sendNullifyMessage2MQ(newTransport);

        // 重新计算技术服务费，并记录到五级地址表
        String biDataJsonKey = getBiDataJsonKey(newTransport.getSrcMsgId());
        String biDataJsonString = stringRedisTemplate.opsForValue().get(biDataJsonKey);
        biDataJsonString = saveTecServiceFee(isCommissionTransport, biDataJsonString, meetCommissionRules, newTransport, tytTecServiceFeeConfigToComputeResult);

        stringRedisTemplate.opsForValue().set(biDataJsonKey, biDataJsonString, 60 * 60 * 60, TimeUnit.SECONDS);
        goodsAddressLevelRecordService.saveAddressLevel(newTransport);

        // 还原工单状态和结单时间
        tytSpecialCarDispatchFailureMapper.restoreWorkOrderStatus(failure.getId(), failure.getWorkOrderStatus(), failure.getEndTime());

        // 设置响应返回
        TransportPublishVo publishResult = new TransportPublishVo();
        publishResult.setTsId(newTransport.getId());
        publishResult.setSrcMsgId(newTransport.getSrcMsgId());
        publishResult.setTsOrderNo(newTransport.getTsOrderNo());
        return publishResult;

    }

    private TransportLabelJson getTransportLabelJson(String labelJsonText) {
        TransportLabelJson labelJson = null;
        if (StringUtils.isNotBlank(labelJsonText)) {
            labelJson = JSON.parseObject(labelJsonText, TransportLabelJson.class);
        }
        if (labelJson == null) {
            labelJson = new TransportLabelJson();
        }

        return labelJson;
    }

    /**
     * @param srcMsgId
     * @return
     */
    private Object updateGoodsStatus(Long srcMsgId, BackoutReasonEnum backoutReasonEnum) {
        GoodsStatusNewReq goodsStatusNewReq = new GoodsStatusNewReq();

        goodsStatusNewReq.setSrcMsgId(srcMsgId);
        goodsStatusNewReq.setOperateType("1");
        goodsStatusNewReq.setBackoutReasonKey(backoutReasonEnum.getMsg());
        goodsStatusNewReq.setBackoutReasonValue(backoutReasonEnum.getCode() + "");
        Object statusResult = reqPlatService.setPlatGoodsStatusNew(goodsStatusNewReq);

        //怀疑上方操作数据同步有延迟，因此等待1s，后续持续关注用户反馈
        CommonUtil.loopSleep(1000L, 1, "sleep_for_saveGoodsStatusNew");
        return statusResult;
    }


    @Override
    public void updateSigningCarUserNum(String phone) {
        //先查询未拉黑的司机
        List<SigningCarUser> users = tytTransportMainMapper.getSigningCarUser(phone);
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        //只留下白名单用户
        List<Long> idList = users.stream()
                .filter(obj -> !Objects.equals(2, obj.getStatus()))
                .map(SigningCarUser::getId)
                .collect(Collectors.toList());
        //白名单用户为空直接返回
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        tytTransportMainMapper.updateSigningCarUser(idList);

    }
}
