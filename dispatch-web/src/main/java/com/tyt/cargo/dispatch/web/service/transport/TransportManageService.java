package com.tyt.cargo.dispatch.web.service.transport;

import com.alibaba.fastjson.JSONObject;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.dispatch.client.form.UserBaseReq;
import com.tyt.cargo.dispatch.client.form.transport.*;
import com.tyt.cargo.dispatch.client.vo.orders.locus.CurrentLocationVO;
import com.tyt.cargo.dispatch.client.vo.transport.*;
import com.tyt.cargo.dispatch.client.vo.user.DispatchCompanyVo;
import com.tyt.cargo.dispatch.client.vo.user.UserBaseVO;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBindVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2022/11/11
 */
public interface TransportManageService {
    PageData<TransportManageVO> getMyPublish(TransportManageReq transportManageReq);

    /**
     * 专车货源列表
     * @param transportManageReq
     * @return
     */
    PageData<TransportManageVO> specialCarList(TransportManageReq transportManageReq);

    /**
     * 价值货源列表
     * @param transportManageReq
     * @return
     */
    PageData<TransportManageVO> valuableList(TransportManageReq transportManageReq);

    /**
     * 专车货源导出列表
     *
     * @param req
     * @return
     */
    List<TransportExportData> getSpecialCarExportList(TransportManageReq req);

    /**
     * 价值货源导出列表
     *
     * @param req
     * @return
     */
    List<ValuableTransportExportData> getValuableExportList(TransportManageReq req);

    /**
     * 专车指派记录
     * @param transportManageReq
     * @return
     */
    AssignListVo assignList(TransportManageReq transportManageReq);

    /**
     * 指派调度处理人
     * @param req
     */
    void assignDispatcher(AssignDispatcherReq req);

    /**
     * 查询调度列表
     * @param req
     * @return
     */
    PageData<DispatchCompanyVo> dispatcherList(AssignDispatcherReq req);

    PageData<TransportViewListVO> transportViewList(TransportViewReq req);

    List<String> transportViewDetailList(TransportViewDetailReq req);

    void editRemark(TransportViewDetailReq req);

    /**
     * 指派车辆
     *
     * @param saveWayBillReq
     */
    void designateCar(SaveWayBillReq saveWayBillReq);

    /**
     * 推送货源
     *
     * @param pushGoodsReq
     * @return
     */
    int pushGoods(PushGoodsReq pushGoodsReq);

    List<RecommendedCarsVo> recommendedCars(RecommendedCarsReq req);

    TransportCountVO transportListCount(Long srcMsgId);

    void saveGiveGoodsPhone(Long srcMsgId, String giveGoodsPhone);

    /**
     * 修改货主出价
     *
     * @param srcMsgId     货源id
     * @param ownerFreight 修改后的货主出价
     * @return
     */
    void updateOwnerFreight(Long srcMsgId, BigDecimal ownerFreight);

    /**
     * 货源来源字段的填充单独处理
     *
     * @param transportManagePage transportManagePage
     */
    void makeSourceTypeFieldForPublishingList(PageData<TransportManageVO> transportManagePage, boolean isSpecialCar);

    void saveDispatcherIdentity(String cellPhone, Integer dispatcherIdentityCode);

    /**
     * 保存/修改信息费差值
     *
     * @param srcMsgId    main表ID
     * @param infoFeeDiff 信息费差值
     * @return WebResult WebResult
     */
    WebResult saveInfoFeeDiff(Long srcMsgId, BigDecimal infoFeeDiff);

    /**
     * 相似货源列表
     *
     * @param req
     * @return
     */
    PageData<SimilarGoodsVo> similarGoodsList(SimilarGoodsReq req);

    /**
     * 相似货源查看或拨打列表
     *
     * @param srcMsgIdList
     * @return
     */
    List<SimilarViewContactVo> similarViewContactList(List<Long> srcMsgIdList);

    /**
     * 获取运力列表
     * @param vehicleSourceReq
     * @return
     */
    PageData<VehicleSourceVo> vehicleSourceList(VehicleSourceReq vehicleSourceReq);

    /**
     * 货源推送
     * @param pushGoodsTipReq
     */
    void pushGoodsTips(PushGoodsTipReq pushGoodsTipReq);

    /**
     * 查询当前车辆位置
     * @param carHeadNo
     * @return
     */
    List<CurrentLocationVO> getCarLocus(String carHeadNo);

    /**
     * 运力车主报价
     * @param carQuotationReq
     */
    void carQuotation(CarQuotationReq carQuotationReq);

    /**
     * 获取运力报价规则
     * @param transportRateRuleReq
     * @return
     */
    TransportRateRuleVO transportRateRule(TransportRateRuleReq transportRateRuleReq);

    /**
     * 获取货源总数
     * @return
     */
    TransportNumVO getTransportNum(Long dispatcherId);

    /**
     * 查询沟通记录列表
     *
     * @param req
     * @return
     */
    List<ContactRecordVo> contactRecordList(TransportManageReq req);

    /**
     * 查询沟通记录列表
     *
     * @param req
     * @return
     */
    List<ContactRecordVo> valuableContactList(TransportManageReq req);

    /**
     * 添加货源沟通记录
     *
     * @param req
     */
    void addContactRecord(ContactRecordReq req);

    /**
     * 专车沟通记录列表
     *
     * @param req
     * @return
     */
    PageData<ContactRecordVo> contactList(ContactRecordReq req);

    /**
     * 专车货源操作记录列表
     *
     * @param req
     * @return
     */
    PageData<SpecialCarOpLogVo> opLogList(SpecialCarOpLogReq req);

    GoodsStatusVo currentGoodsStatus(SpecialCarOpLogReq req);

    void addValuableContactRecord(ContactRecordReq req);

    PageData<UserBaseVO> followUpList(UserBaseReq req);

    JSONObject followupNum();

    ValuableTransportDetailVO valuableDetail(TransportManageReq req);

    void changeValuableCsUser(List<Long> srcMsgIds, Long csUserId, String csUserName);

    List<CsBusinessUserBindVO> getAllTransportCsUser(String csUserName);

    List<CsBusinessUserBindVO> getValuableTransportCsUser();

    void updateValuableTransportCsUserStatus(Long csUserId, Integer status);

    ValuableDispatchConfigVO valuableDispatchConfig();

    void updateValuableDispatchConfig(ValuableDispatchConfigVO configVO);

    void updateValuableTransportCsUserDesignateStatus(Long csUserId, Integer designateStatus);

}
