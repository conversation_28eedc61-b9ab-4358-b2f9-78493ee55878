package com.tyt.cargo.dispatch.web.mybatis.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tyt_keyword_matches_new_unstandard")
public class TytKeywordMatchesNewUnstandard {
    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关键字
     */
    @Column(name = "key_words")
    private String keyWords;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 二级型号
     */
    @Column(name = "second_type")
    private String secondType;

    /**
     * 二级分类，如挖掘机，起重机等
     */
    @Column(name = "second_class")
    private String secondClass;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal width;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 未知
     */
    private Long tag;

    private Date ctime;
}