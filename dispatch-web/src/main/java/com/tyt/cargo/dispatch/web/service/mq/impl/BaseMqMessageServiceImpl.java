package com.tyt.cargo.dispatch.web.service.mq.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.SendResult;
import com.tyt.cargo.core.util.CommonUtil;
import com.tyt.cargo.core.vo.base.MqMessageBaseVo;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytMqMessage;
import com.tyt.cargo.dispatch.web.mybatis.mapper.base.TytMqMessageMapper;
import com.tyt.cargo.dispatch.web.service.mq.BaseMqMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 消息中心生产者
 */
@Slf4j
@Service
public class BaseMqMessageServiceImpl extends RocketMqMessageServiceImpl implements BaseMqMessageService {

    @Autowired
    private TytMqMessageMapper tytMqMessageMapper;

    /**
     * 保存mq消息
     * @param mqMessageBaseVo
     */
    private void saveMqMessage(MqMessageBaseVo mqMessageBaseVo) {
        TytMqMessage tytMqMessage = TytMqMessage.builder()
                .messageSerialNum(mqMessageBaseVo.getMessageSerailNum())
                .createTime(new Date())
                .messageType(mqMessageBaseVo.getMessageType())
                .messageContent(JSON.toJSONString(mqMessageBaseVo))
                .build();

        tytMqMessageMapper.insertSelective(tytMqMessage);
    }

    private SendResult producerMessage(MqMessageBaseVo mqMessageBaseVo, String topic, Long delayTime){

        String messageSerialNum = mqMessageBaseVo.getMessageSerailNum();
        String tag = mqMessageBaseVo.getTag();

        if(CommonUtil.hasNull(topic, messageSerialNum, tag)){
            return null;
        }

        String mqDataJson = JSON.toJSONString(mqMessageBaseVo);
//        this.saveMqMessage(mqMessageBaseVo);

        SendResult sendResult = super.produceMqMsg(topic, tag, messageSerialNum, mqDataJson, delayTime);

        return sendResult;
    }

    @Override
    public SendResult sendMessage(MqMessageBaseVo mqMessageBaseVo, Long delayTime) {
        String messageSerialNum = mqMessageBaseVo.getMessageSerailNum();
        String tag = mqMessageBaseVo.getTag();
        String topic = mqMessageBaseVo.getTopic();

        if(mqMessageBaseVo == null || StringUtils.isBlank(messageSerialNum) || StringUtils.isBlank(tag)){
            log.error("sendMessage_null_values_error");
            return null;
        }

        log.info("sendMessage_start. sn : {}, tag : {}", messageSerialNum, tag);

        SendResult sendResult = this.producerMessage(mqMessageBaseVo, topic, delayTime);
        return sendResult;
    }

}
