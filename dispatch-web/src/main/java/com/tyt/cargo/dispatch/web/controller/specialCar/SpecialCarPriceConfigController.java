package com.tyt.cargo.dispatch.web.controller.specialCar;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tyt.cargo.core.enums.ResponseEnum;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.dispatch.client.form.price.SpecialCarPriceConfigReq;
import com.tyt.cargo.dispatch.client.vo.price.CargoOwnerInfoVo;
import com.tyt.cargo.dispatch.client.vo.price.SpecialCarPriceConfigExportData;
import com.tyt.cargo.dispatch.client.vo.price.SpecialCarPriceConfigVo;
import com.tyt.cargo.dispatch.web.enums.PriceConfigTypeEnum;
import com.tyt.cargo.dispatch.web.service.price.SpecialCarPriceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 专车运费管理
 * <AUTHOR>
 * @since 2024-06-22 10:20
 */
@Slf4j
@RestController
@RequestMapping("/specialCar")
public class SpecialCarPriceConfigController {

    @Autowired
    private SpecialCarPriceConfigService specialCarPriceConfigService;

    /**
     * 专车运费配置列表
     * <AUTHOR>
     * @date 2024/6/22 10:50
     * @param priceConfigReq
     * @return WebResult<PageData<SpecialCarPriceConfigVo>>
     */
    @PostMapping("/priceConfigList")
    public WebResult<PageData<SpecialCarPriceConfigVo>> priceConfigList(@RequestBody SpecialCarPriceConfigReq priceConfigReq) {
        return WebResult.successResponse(specialCarPriceConfigService.priceConfigList(priceConfigReq));
    }

    @RequestMapping("/priceConfigExport")
    public void priceConfigExport(@RequestBody SpecialCarPriceConfigReq req, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("专车运费列表", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        req.setPageNum(1);
        req.setPageSize(10000);
        List<SpecialCarPriceConfigVo> priceConfigList = specialCarPriceConfigService.priceConfigList(req).getList();
        List<SpecialCarPriceConfigExportData> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(priceConfigList)) {
            list = priceConfigList.stream().map(t -> {
                SpecialCarPriceConfigExportData data = new SpecialCarPriceConfigExportData();
                BeanUtils.copyProperties(t, data);
                data.setPriceRuleStr(processPriceRule(t.getPriceRule(), t.getDrivingFee()));
                data.setLessPriceRuleStr(processPriceRule(t.getLessPriceRule(), t.getDrivingFee()));
                return data;
            }).collect(Collectors.toList());
        }
        EasyExcel.write(response.getOutputStream(), SpecialCarPriceConfigExportData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("专车运费列表").doWrite(list);
    }

    private String processPriceRule(String priceRule, BigDecimal drivingFee) {
        StringBuilder priceRuleBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(priceRule)) {
            // [{"start":0,"end":10,"type":1,"price":410},{"start":10,"end":44,"type":2,"price":"12"}]
            JSONArray priceArray = JSONObject.parseArray(priceRule);
            for (int i = 0; i < priceArray.size(); i++) {
                JSONObject priceJson = priceArray.getJSONObject(i);
                priceRuleBuilder.append("【").append(priceJson.getIntValue("start")).append(" - ");
                int end = priceJson.getIntValue("end");
                if (end != 999999) {
                    priceRuleBuilder.append(end);
                }
                priceRuleBuilder.append("】");
                priceRuleBuilder.append(priceJson.getString("price"));
                int type = priceJson.getIntValue("type");
                if (Objects.equals(type, PriceConfigTypeEnum.FIXED_PRICE.getCode())) {
                    priceRuleBuilder.append("元");
                } else {
                    priceRuleBuilder.append("元/公里");
                }
                if (i == priceArray.size() - 1) {
                    if (Objects.nonNull(drivingFee) && drivingFee.intValue() > 0) {
                        priceRuleBuilder.append(" | 驾驶货物费：").append(drivingFee.intValue()).append("元");
                    }
                } else {
                    priceRuleBuilder.append(" | ");
                }
            }
        }
        return priceRuleBuilder.toString();
    }

    /**
     * 添加或编辑运费规则
     * <AUTHOR>
     * @date 2024/6/22 13:03
     * @param priceConfigReq
     * @return WebResult
     */
    @PostMapping("/addOrUpdatePriceConfig")
    public WebResult addOrUpdatePriceConfig(@RequestBody @Valid SpecialCarPriceConfigReq priceConfigReq,
                                            BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return WebResult.failResponse(ResponseEnum.request_error.info(bindingResult.getAllErrors().get(0).getDefaultMessage()));
        }

        return specialCarPriceConfigService.addOrUpdatePriceConfig(priceConfigReq);
    }

    /**
     * 导入专车运费配置
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importPriceConfig")
    public WebResult importPriceConfig(@RequestParam("file") MultipartFile file) {
        specialCarPriceConfigService.importPriceConfig(file);
        return WebResult.successResponse();
    }

    /**
     * 获取运费配置详情
     * <AUTHOR>
     * @date 2024/6/22 18:33
     * @param req
     * @return WebResult<SpecialCarPriceConfigVo>
     */
    @PostMapping("/getDetail")
    public WebResult<SpecialCarPriceConfigVo> getDetail(@RequestBody SpecialCarPriceConfigReq req) {
        if (Objects.isNull(req.getId())) {
            return WebResult.failResponse(ResponseEnum.request_error.info("记录ID不能为空"));
        }
        SpecialCarPriceConfigVo detail = specialCarPriceConfigService.getDetail(req.getId());
        if (Objects.nonNull(detail)) {
            return WebResult.successResponse(detail);
        } else {
            return WebResult.failResponse(ResponseEnum.request_error.info("记录不存在"));
        }
    }

    /**
     * 修改记录状态
     * <AUTHOR>
     * @date 2024/6/24 10:13
     * @param req
     * @return WebResult
     */
    @PostMapping("/updateStatus")
    public WebResult updateStatus(@RequestBody SpecialCarPriceConfigReq req) {
        return specialCarPriceConfigService.updateStatus(req);
    }

    /**
     * 删除
     * <AUTHOR>
     * @date 2024/6/24 10:13
     * @param req
     * @return WebResult
     */
    @PostMapping("/delete")
    public WebResult delete(@RequestBody SpecialCarPriceConfigReq req) {
        return specialCarPriceConfigService.delete(req);
    }

    /**
     * 获取专车运费所属企业
     * <AUTHOR>
     * @date 2024/6/24 10:13
     * @param req
     * @return WebResult
     */
    @PostMapping("/cargoOwnerList")
    public WebResult<PageData<CargoOwnerInfoVo>> cargoOwnerList(@RequestBody SpecialCarPriceConfigReq req) {
        return WebResult.successResponse(specialCarPriceConfigService.cargoOwnerList(req));
    }
}
