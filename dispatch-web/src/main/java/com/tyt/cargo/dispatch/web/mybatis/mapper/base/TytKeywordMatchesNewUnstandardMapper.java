package com.tyt.cargo.dispatch.web.mybatis.mapper.base;

import com.tyt.cargo.db.tool.CustomBaseMapper;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.TytKeywordMatchesNewUnstandard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytKeywordMatchesNewUnstandardMapper extends CustomBaseMapper<TytKeywordMatchesNewUnstandard> {
    /**
     * 根据关键字列表查询匹配的记录
     * @param keywords
     * @return
     */
    List<TytKeywordMatchesNewUnstandard> selectByKeywords(@Param("keywords") List<String> keywords);
}