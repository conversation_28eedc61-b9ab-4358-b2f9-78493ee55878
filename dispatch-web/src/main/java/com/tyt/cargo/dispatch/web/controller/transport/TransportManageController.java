package com.tyt.cargo.dispatch.web.controller.transport;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.tyt.cargo.core.model.ResponseCode;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.core.util.DateUtil;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.dispatch.client.enums.PublishTypeEnum;
import com.tyt.cargo.dispatch.client.enums.SourceTypeEnum;
import com.tyt.cargo.dispatch.client.enums.TransportStatusEnum;
import com.tyt.cargo.dispatch.client.form.UserBaseReq;
import com.tyt.cargo.dispatch.client.form.transport.*;
import com.tyt.cargo.dispatch.client.vo.orders.locus.CurrentLocationVO;
import com.tyt.cargo.dispatch.client.vo.plat.GoodsSingleDetailResultBean;
import com.tyt.cargo.dispatch.client.vo.transport.*;
import com.tyt.cargo.dispatch.client.vo.user.DispatchCompanyVo;
import com.tyt.cargo.dispatch.client.vo.user.UserBaseVO;
import com.tyt.cargo.dispatch.web.commons.annotation.NoRepeatSubmit;
import com.tyt.cargo.dispatch.web.commons.annotation.RecordOpLog;
import com.tyt.cargo.dispatch.web.enums.AssignTypeEnum;
import com.tyt.cargo.dispatch.web.enums.ExcellentEnum;
import com.tyt.cargo.dispatch.web.enums.OperationTypeEnum;
import com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBindVO;
import com.tyt.cargo.dispatch.web.service.base.ReqPlatService;
import com.tyt.cargo.dispatch.web.service.transport.SeckillGoodsTransportService;
import com.tyt.cargo.dispatch.web.service.transport.TransportManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 货源管理相关
 *
 * <AUTHOR>
 * @Date 2022/11/11
 */
@Slf4j
@Api(tags = "货源管理相关")
@RestController
@RequestMapping("/transport")
public class TransportManageController {

    @Autowired
    private TransportManageService transportManageService;
    @Autowired
    private ReqPlatService reqPlatService;

    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;

    /**
     * 个人货源列表
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "我的货源列表")
    @RequestMapping("/getMyPublish")
    public WebResult<PageData<TransportManageVO>> getMyPublish(@RequestBody(required = false) TransportManageReq req) {
        req = req != null ? req : new TransportManageReq();
        return WebResult.successResponse(transportManageService.getMyPublish(req));
    }

    /**
     * 专车货源列表
     * @param req
     * @return
     */
    @RequestMapping("/specialCarList")
    public WebResult<PageData<TransportManageVO>> specialCarList(@RequestBody(required = false) TransportManageReq req) {
        return WebResult.successResponse(transportManageService.specialCarList(req));
    }

    /**
     * 价值货源列表
     * @param req
     * @return
     */
    @RequestMapping("/valuableList")
    public WebResult<PageData<TransportManageVO>> valuableList(@RequestBody(required = false) TransportManageReq req) {
        return WebResult.successResponse(transportManageService.valuableList(req));
    }

    /**
     * 价值货源列表派单配置信息
     *
     * @return
     */
    @RequestMapping("/valuableDispatchConfig")
    public WebResult<ValuableDispatchConfigVO> valuableDispatchConfig() {
        return WebResult.successResponse(transportManageService.valuableDispatchConfig());
    }

    /**
     * 更新价值货源列表派单配置信息
     *
     * @return
     */
    @RequestMapping("/updateValuableDispatchConfig")
    public WebResult updateValuableDispatchConfig(@RequestBody ValuableDispatchConfigVO configVO) {
        transportManageService.updateValuableDispatchConfig(configVO);
        return WebResult.successResponse();
    }

    @PostMapping("/changeValuableCsUser")
    public WebResult changeValuableCsUser(@RequestBody ChangeValuableCsUserDTO changeValuableCsUserDTO) {
        transportManageService.changeValuableCsUser(changeValuableCsUserDTO.getSrcMsgIds(), changeValuableCsUserDTO.getCsUserId(), changeValuableCsUserDTO.getCsUserTrueName());
        return WebResult.successResponse();
    }

    @GetMapping("/getAllTransportCsUser")
    public WebResult<List<CsBusinessUserBindVO>> getAllTransportCsUser(@RequestParam("csUserName") String csUserName) {
        return WebResult.successResponse(transportManageService.getAllTransportCsUser(csUserName));
    }

    @GetMapping("/getValuableTransportCsUser")
    public WebResult<List<CsBusinessUserBindVO>> getValuableTransportCsUser() {
        return WebResult.successResponse(transportManageService.getValuableTransportCsUser());
    }

    @GetMapping("/updateValuableTransportCsUserStatus")
    public WebResult updateValuableTransportCsUserStatus(@RequestParam("csUserId") Long csUserId, @RequestParam("status") Integer status) {
        transportManageService.updateValuableTransportCsUserStatus(csUserId, status);
        return WebResult.successResponse();
    }

    @GetMapping("/updateValuableTransportCsUserDesignateStatus")
    public WebResult updateValuableTransportCsUserDesignateStatus(@RequestParam("csUserId") Long csUserId, @RequestParam("designateStatus") Integer designateStatus) {
        transportManageService.updateValuableTransportCsUserDesignateStatus(csUserId, designateStatus);
        return WebResult.successResponse();
    }



    /**
     * 专车货源列表导出
     *
     * @param req
     * @param response
     * @throws IOException
     */
    @RequestMapping("/specialCarExport")
    public void specialCarExport(@RequestBody(required = false) TransportManageReq req, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("专车货源列表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        req = req != null ? req : new TransportManageReq();
        List<TransportExportData> list = transportManageService.getSpecialCarExportList(req);

        EasyExcel.write(response.getOutputStream(), TransportExportData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("专车货源列表").doWrite(list);
    }

    /**
     * 价值货源列表导出
     *
     * @param req
     * @param response
     * @throws IOException
     */
    @RequestMapping("/exportValuableList")
    public void exportValuableList(@RequestBody(required = false) TransportManageReq req, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("价值源列表", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        req = req != null ? req : new TransportManageReq();
        List<ValuableTransportExportData> list = transportManageService.getValuableExportList(req);
        EasyExcel.write(response.getOutputStream(), ValuableTransportExportData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("价值货源列表").doWrite(list);
    }

    /**
     * 沟通记录导出
     *
     * @param req
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping("/contactRecordExport")
    public void contactRecordExport(@RequestBody(required = false) TransportManageReq req, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("专车货源沟通记录列表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        req = req != null ? req : new TransportManageReq();
        req.setPageNum(1);
        req.setPageSize(10000);
        List<ContactRecordVo> contactRecordVOS = transportManageService.contactRecordList(req);
        List<ContactRecordExportData> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(contactRecordVOS)) {
            list = contactRecordVOS.stream().map(e -> {
                ContactRecordExportData data = new ContactRecordExportData();
                BeanUtil.copyProperties(e, data);
                data.setCreateTimeStr(DateUtil.dateToString(e.getCreateTime(), DateUtil.date_time_format));
                data.setAssignType(AssignTypeEnum.getNameByCode(e.getAssignType()));
                data.setLimGoodModelScore(Objects.nonNull(e.getLimGoodModelScore()) ? e.getLimGoodModelScore().toString() : "");
                return data;
            }).collect(Collectors.toList());
        }
        EasyExcel.write(response.getOutputStream(), ContactRecordExportData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("专车货源沟通记录列表").doWrite(list);
    }

    /**
     * 价值货源沟通记录导出
     *
     * @param req
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping("/exportValuableContactList")
    public void exportValuableContactList(@RequestBody(required = false) TransportManageReq req, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("价值货源沟通记录列表", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        req = req != null ? req : new TransportManageReq();
        req.setPageNum(1);
        req.setPageSize(10000);
        List<ContactRecordVo> contactRecordVOS = transportManageService.valuableContactList(req);
        List<ValuableContactRecordExportData> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(contactRecordVOS)) {
            list = contactRecordVOS.stream().map(e -> {
                ValuableContactRecordExportData data = new ValuableContactRecordExportData();
                BeanUtil.copyProperties(e, data);
                data.setCreateTimeStr(DateUtil.dateToString(e.getCreateTime(), DateUtil.date_time_format));
                return data;
            }).collect(Collectors.toList());
        }
        EasyExcel.write(response.getOutputStream(), ValuableContactRecordExportData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("价值货源沟通记录列表").doWrite(list);
    }

    /**
     * 添加货源沟通记录
     *
     * @param req
     * @return
     */
    @RequestMapping("/addContactRecord")
    @NoRepeatSubmit
    public WebResult addContactRecord(@RequestBody ContactRecordReq req) {
        transportManageService.addContactRecord(req);
        return WebResult.successResponse();
    }

    /**
     * 添加价值货源货源沟通记录
     *
     * @param req
     * @return
     */
    @RequestMapping("/addValuableContactRecord")
    @NoRepeatSubmit
    public WebResult addValuableContactRecord(@RequestBody ContactRecordReq req) {
        transportManageService.addValuableContactRecord(req);
        return WebResult.successResponse();
    }

    /**
     * 专车货源沟通记录列表
     *
     * @param req
     * @return
     */
    @RequestMapping("/contactList")
    public WebResult<PageData<ContactRecordVo>> contactList(@RequestBody ContactRecordReq req) {
        return WebResult.successResponse(transportManageService.contactList(req));
    }

    /**
     * 价值货源跟进人筛选获取全部跟进人
     *
     * @param req
     * @return
     */
    @PostMapping("/followUpList")
    public WebResult<PageData<UserBaseVO>> followUpList(@RequestBody UserBaseReq req) {
        return WebResult.successResponse(transportManageService.followUpList(req));
    }

    /**
     * 价值货源需要跟进的货源数量
     *
     * @return
     */
    @PostMapping("/valuable/followupNum")
    public WebResult<JSONObject> followupNum() {
        return WebResult.successResponse(transportManageService.followupNum());
    }

    /**
     * 价值货源详情
     *
     * @return
     */
    @PostMapping("/valuable/detail")
    public WebResult<ValuableTransportDetailVO> valuableDetail(@RequestBody TransportManageReq req) {
        return WebResult.successResponse(transportManageService.valuableDetail(req));
    }

    /**
     * 专车货源操作记录列表
     *
     * @param req
     * @return
     */
    @RequestMapping("/opLogList")
    public WebResult<PageData<SpecialCarOpLogVo>> opLogList(@RequestBody SpecialCarOpLogReq req) {
        return WebResult.successResponse(transportManageService.opLogList(req));
    }

    /**
     * 查询货源当前状态集合
     *
     * @return
     */
    @RequestMapping("/currentGoodsStatus")
    public WebResult<GoodsStatusVo> currentGoodsStatus(@RequestBody SpecialCarOpLogReq req) {
        return WebResult.successResponse(transportManageService.currentGoodsStatus(req));
    }

    /**
     * 指派记录列表
     * @param req
     * @return
     */
    @RequestMapping("/assignList")
    @RecordOpLog(logType = OperationTypeEnum.VIEW_ASSIGN_RECORD)
    public WebResult<AssignListVo> assignList(@RequestBody(required = false) TransportManageReq req) {
        return WebResult.successResponse(transportManageService.assignList(req));
    }

    /**
     * 指派调度处理人
     * @param req
     * @return
     */
    @RequestMapping("/assignDispatcher")
    @RecordOpLog(logType = OperationTypeEnum.ASSIGN_DISPATCHER)
    public WebResult assignDispatcher(@RequestBody(required = false) AssignDispatcherReq req) {
        transportManageService.assignDispatcher(req);
        return WebResult.successResponse();
    }

    /**
     * 查询代调列表
     * @param req
     * @return
     */
    @RequestMapping("/dispatcherList")
    public WebResult<PageData<DispatchCompanyVo>> dispatcherList(@RequestBody(required = false) AssignDispatcherReq req) {
        return WebResult.successResponse(transportManageService.dispatcherList(req));
    }

    /**
     * 货源查看/联系列表
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "货源查看/联系列表")
    @PostMapping("/transportViewList")
    public WebResult<PageData<TransportViewListVO>> transportViewList(@Validated @RequestBody TransportViewReq req) {
        return WebResult.successResponse(transportManageService.transportViewList(req));
    }

    /**
     * 货源查看/联系列表/推送车辆数量
     *
     * @param srcMsgId
     * @return
     */
    @ApiOperation(value = "获取查看、联系、推送车辆列表数量")
    @RequestMapping("/transportListCount")
    public WebResult<TransportCountVO> transportListCount(Long srcMsgId) {
        return WebResult.successResponse(transportManageService.transportListCount(srcMsgId));
    }


    /**
     * @param req
     * @return
     */
    @ApiOperation(value = "获取车主查看详情")
    @PostMapping("/transportViewDetailList")
    public WebResult<List<String>> transportViewDetailList(@Validated @RequestBody TransportViewDetailReq req) {

        return WebResult.successResponse(transportManageService.transportViewDetailList(req));
    }

    /**
     * 查看详情
     *
     * @param srcMsgId
     * @param type
     * @return
     */
    @ApiOperation(value = "查看详情")
    @RequestMapping(value = "/getSingleDetail", method = RequestMethod.POST)
    public WebResult<GoodsSingleDetailResultBean> getSingleDetail(@RequestParam(value = "srcMsgId") Long srcMsgId,
                                                                  @RequestParam(value = "type", required = false) String type,
                                                                  @RequestParam(value = "sourceType", required = false) String sourceType) {

        return WebResult.successResponse(reqPlatService.getPlatSingleDetail(srcMsgId, type, sourceType));
    }


    /**
     * @return com.tyt.cargo.web.model.plat.TytResultMsgBean
     * <AUTHOR> Lion
     * @Description 撤销/设置成交
     * @Param [goodsId, operateType, carryCellPhone, carryName, backoutReasonKey, backoutReasonValue]
     * @Date 2022/11/15 11:14
     */
    @ApiOperation(value = "撤销/设置成交")
    @RequestMapping(value = "/saveGoodsStatusNew", method = RequestMethod.POST)
    public WebResult<Object> saveGoodsStatusNew(@RequestBody @Validated GoodsStatusNewReq goodsStatusNewReq) {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(goodsStatusNewReq.getSrcMsgId())) {
            return WebResult.failResponse(new ResponseCode("8899010", "已有多个司机抢单，正在匹配最优司机，请耐心等待"));
        }

        return WebResult.successResponse(reqPlatService.setPlatGoodsStatusNew(goodsStatusNewReq));
    }

    /**
     * @return com.tyt.cargo.web.model.plat.TytResultMsgBean
     * <AUTHOR> Lion
     * @Description 填写/修改承运车辆
     * @Param [goodsId, headCity, headNo, tailCity, tailNo, dealPrice]
     * @Date 2022/11/15 11:14
     */
    @ApiOperation(value = "填写/修改承运车辆")
    @RequestMapping(value = "/getCarPosition", method = RequestMethod.POST)
    public WebResult<Object> getCarPosition(@RequestBody @Validated CarPositionReq carPositionReq) {

        return WebResult.successResponse(reqPlatService.getPlatCarPosition(carPositionReq));
    }

    /**
     * 编辑备注
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "编辑备注")
    @PostMapping(value = "/editRemark")
    public WebResult editRemark(@Validated @RequestBody TransportViewDetailReq req) {
        transportManageService.editRemark(req);
        return WebResult.successResponse();
    }

    /**
     * 编辑备注
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "货源列表导出")
    @RequestMapping("/export")
    public void export(@RequestBody(required = false) TransportManageReq req, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("列表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        req = req != null ? req : new TransportManageReq();
        req.setPageNum(1);
        req.setPageSize(Integer.MAX_VALUE);
        List<TransportManageVO> transportManageVOList = transportManageService.getMyPublish(req).getList();
        List<TransportExportData> list = new ArrayList<>();
        String excelName = "（优货）";
        if (CollUtil.isNotEmpty(transportManageVOList)) {
            list = transportManageVOList.stream().map(t -> {
                TransportExportData transportExportData = new TransportExportData();
                BeanUtil.copyProperties(t, transportExportData);
                transportExportData.setAmountAndIsRefund(t.getInfoFee() + (t.getRefundFlag() == null || t.getRefundFlag() == 0 ? "(不退还)" : "(退还)"));
                // 是否有货逻辑
                if (t.getExcellentGoods() != null && t.getExcellentGoods().equals(ExcellentEnum.Y.getCode())) {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName() + excelName);
                } else if (t.getExcellentGoods() != null && ExcellentEnum.SPECIAL.getCode().equals(t.getExcellentGoods())) {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName() + "（专车）");
                } else {
                    transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName());
                }
                transportExportData.setSourceType(SourceTypeEnum.hong_xin.getCode() == t.getSourceType() ? SourceTypeEnum.hong_xin.getZhName() : SourceTypeEnum.ymm.getCode() == t.getSourceType() ? SourceTypeEnum.ymm.getZhName() : SourceTypeEnum.dispatch.getZhName());
                //transportExportData.setPublishTypeName(PublishTypeEnum.getEnum(t.getPublishType()).getZhName());
                transportExportData.setStatusName(TransportStatusEnum.getEnum(t.getStatus()) == null ? ""
                        : TransportStatusEnum.getEnum(t.getStatus()).getZhName());
                return transportExportData;
            }).collect(Collectors.toList());
        }
        EasyExcel.write(response.getOutputStream(), TransportExportData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("货源列表").doWrite(list);
    }


    /**
     * 编辑备注
     * 指派
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "推荐车辆")
    @RequestMapping("/recommendedCars")
    public WebResult<List<RecommendedCarsVo>> recommendedCars(@Validated @RequestBody RecommendedCarsReq req) {
        return WebResult.successResponse(transportManageService.recommendedCars(req));
    }

    /**
     * 指派车辆
     *
     * @param saveWayBillReq
     * @return
     */
    @RequestMapping(value = "/designateCar")
    public WebResult designateCar(@RequestBody @Validated SaveWayBillReq saveWayBillReq) {
        //好货抢单锁定判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(saveWayBillReq.getGoodsId())) {
            return WebResult.failResponse(new ResponseCode("8899010", "已有多个司机抢单，正在匹配最优司机，请耐心等待"));
        }
        transportManageService.designateCar(saveWayBillReq);
        return WebResult.successResponse();
    }

    /**
     * 推送货源
     *
     * @param pushGoodsReq
     * @return
     */
    @RequestMapping(value = "pushGoods")
    public WebResult pushGoods(@RequestBody @Validated PushGoodsReq pushGoodsReq) {
        return WebResult.successResponse(transportManageService.pushGoods(pushGoodsReq));
    }


    /**
     * 保存/修改信息费差值
     *
     * @param srcMsgId    main表ID
     * @param infoFeeDiff 信息费差值
     * @return WebResult
     */
    @PostMapping(value = "/saveInfoFeeDiff")
    public WebResult saveInfoFeeDiff(@RequestParam Long srcMsgId, @RequestParam BigDecimal infoFeeDiff) {
        return transportManageService.saveInfoFeeDiff(srcMsgId, infoFeeDiff);
    }

    /**
     * 修改货主出价
     *
     * @param srcMsgId     货源id
     * @param ownerFreight 修改后的货主出价
     * @return
     */
    @PostMapping("/updateOwnerFreight")
    public WebResult updateOwnerFreight(@RequestParam Long srcMsgId, @RequestParam BigDecimal ownerFreight) {
        transportManageService.updateOwnerFreight(srcMsgId, ownerFreight);
        return WebResult.successResponse();
    }

    /**
     * 保存/修改给货货主手机号
     *
     * @param srcMsgId
     * @param giveGoodsPhone
     * @return
     */
    @PostMapping(value = "/saveGiveGoodsPhone")
    public WebResult saveGiveGoodsPhone(@RequestParam Long srcMsgId, @RequestParam String giveGoodsPhone) {
        transportManageService.saveGiveGoodsPhone(srcMsgId, giveGoodsPhone);
        return WebResult.successResponse();
    }

    /**
     * 保存调度审核身份
     *
     * @param cellPhone
     * @param dispatcherIdentityCode
     * @return
     */
    @PostMapping("/saveDispatcherIdentity")
    public WebResult saveDispatcherIdentity(@RequestParam String cellPhone, @RequestParam(required = false) Integer dispatcherIdentityCode) {
        transportManageService.saveDispatcherIdentity(cellPhone, dispatcherIdentityCode);
        return WebResult.successResponse();
    }

    /**
     * 相似货源列表
     *
     * @param req
     * @return
     */
    @GetMapping("/similarGoodsList")
    public WebResult similarGoodsList(SimilarGoodsReq req) {
        return WebResult.successResponse(transportManageService.similarGoodsList(req));
    }

    /**
     * 相似货源查看或拨打列表
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/similarViewContactList")
    public WebResult similarViewContactList(Long srcMsgId) {
        return WebResult.successResponse(transportManageService.similarViewContactList(Collections.singletonList(srcMsgId)));
    }

    /**
     * 车源运力列表
     * @param vehicleSourceReq
     * @return
     */
    @PostMapping("/vehicleSourceList")
    public WebResult vehicleSourceList(@Valid @RequestBody VehicleSourceReq vehicleSourceReq) {
        return WebResult.successResponse(transportManageService.vehicleSourceList(vehicleSourceReq));
    }

    /**
     * pushGoodsTips
     * @param pushGoodsTipReq
     * @return
     */
    @PostMapping("/pushGoodsTips")
    public WebResult pushGoodsTips(@Valid @RequestBody PushGoodsTipReq pushGoodsTipReq) {
        transportManageService.pushGoodsTips(pushGoodsTipReq);
        return WebResult.successResponse();
    }


    /**
     * 获取车辆位置
     * @param carHeadNo
     * @return
     */

    @GetMapping("/getCarLocus")
    public WebResult getCarLocus(@RequestParam String carHeadNo) {
        List<CurrentLocationVO> currentLocation = transportManageService.getCarLocus(carHeadNo);
        return WebResult.successResponse(currentLocation);
    }

    /**
     * 添加/修改车主报价
     * @param carQuotationReq
     * @return
     */
    @PostMapping("/carQuotation")
    public WebResult carQuotation(@Valid @RequestBody CarQuotationReq carQuotationReq) {
        transportManageService.carQuotation(carQuotationReq);
        return WebResult.successResponse();
    }


    /**
     * 货主出价占比&出价等级
     * @param transportRateRuleReq
     * @return
     */
    @GetMapping("/transportRateRule")
    public WebResult transportRateRule(@Valid TransportRateRuleReq transportRateRuleReq) {
        TransportRateRuleVO transportRateRuleVO = transportManageService.transportRateRule(transportRateRuleReq);
        return WebResult.successResponse(transportRateRuleVO);
    }


    @GetMapping("/getTransportNum")
    public WebResult getTransportNum(@RequestParam Long dispatcherId) {
        TransportNumVO transportNumVO = transportManageService.getTransportNum(dispatcherId);
        return WebResult.successResponse(transportNumVO);
    }




}
