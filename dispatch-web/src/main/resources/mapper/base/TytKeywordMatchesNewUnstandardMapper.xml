<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.cargo.dispatch.web.mybatis.mapper.base.TytKeywordMatchesNewUnstandardMapper">
  <resultMap id="BaseResultMap" type="com.tyt.cargo.dispatch.web.mybatis.entity.base.TytKeywordMatchesNewUnstandard">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="key_words" jdbcType="VARCHAR" property="keyWords" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="second_type" jdbcType="VARCHAR" property="secondType" />
    <result column="second_class" jdbcType="VARCHAR" property="secondClass" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="length" jdbcType="DECIMAL" property="length" />
    <result column="width" jdbcType="DECIMAL" property="width" />
    <result column="height" jdbcType="DECIMAL" property="height" />
    <result column="tag" jdbcType="BIGINT" property="tag" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>
  <select id="selectByKeywords"
          resultMap="BaseResultMap">
      select * from tyt_keyword_matches_new_unstandard
      where key_words in
      <foreach collection="keywords" item="keyword" open="(" separator="," close=")">
          #{keyword}
      </foreach>
  </select>
</mapper>