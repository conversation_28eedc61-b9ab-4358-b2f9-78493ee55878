<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.cargo.dispatch.web.mybatis.mapper.base.CsBusinessUserBindMapper">
  <resultMap id="BaseResultMap" type="com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBind">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cs_account" jdbcType="VARCHAR" property="csAccount" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="login_phone_no" jdbcType="VARCHAR" property="loginPhoneNo" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="leader_id" jdbcType="BIGINT" property="leaderId" />
    <result column="subordinate_id" jdbcType="BIGINT" property="subordinateId" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_updater" jdbcType="BIGINT" property="lastUpdater" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="cs_department_id" jdbcType="VARCHAR" property="csDepartmentId" />
    <result column="last_updater_name" jdbcType="VARCHAR" property="lastUpdaterName" />
    <result column="small_phone" jdbcType="VARCHAR" property="smallPhone" />
    <result column="dept_top_id" jdbcType="BIGINT" property="deptTopId" />
  </resultMap>
    <select id="selectByLoginPhone"
            resultMap="BaseResultMap">
        select * from cs_business_user_bind where login_phone_no = #{loginPhone} and is_valid = 1
      <if test="roleIds != null and roleIds.size() > 0">
        and role_id in
        <foreach collection="roleIds" item="roleId" index="index" open="(" close=")" separator=",">
          #{roleId}
        </foreach>
      </if>
    </select>

  <select id="selectByNames" resultMap="BaseResultMap">
    select * from cs_business_user_bind where is_valid = 1
    and name in ( <foreach collection="names" item="i" separator=","> #{i} </foreach> )
  </select>

  <select id="selectByDispatcherId"
          resultMap="BaseResultMap">
    select cub.* from cs_business_user_bind cub
        join tyt_internal_employee tie on cub.login_phone_no = tie.login_phone_no
    where tie.id = #{dispatcherId} and cub.is_valid = 1
    <if test="roleIds != null and roleIds.size() > 0">
      and cub.role_id in
      <foreach collection="roleIds" item="roleId" index="index" open="(" close=")" separator=",">
        #{roleId}
      </foreach>
    </if>
  </select>

  <select id="getAllTransportCsUser" resultType="com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBindVO">
    select cbub.id, cbub.login_phone_no as loginPhoneNo, cbub.real_name as realName, tcuvs.status, tcuvs.designate_status as designateStatus, cbub.cs_department_id as departmentId
    from cs_business_user_bind cbub left join tyt_cs_user_valuable_status tcuvs on cbub.id = tcuvs.cs_business_user_id
    where cbub.is_valid = 1 and (cbub.cs_department_id like '%3%' or cbub.cs_department_id like '%4%') and cbub.real_name like CONCAT('%', #{csUserName}, '%')
  </select>

  <select id="getCsUserOrderNum" resultType="com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBindVO">
    select count(1) as orderNum, market_follow_up_user_id as id
    from tyt_transport_valuable where market_follow_up_user_id in
    <foreach collection="csUserIdList" open="(" close=")" item="csUserId" separator=",">
      #{csUserId}
    </foreach>
    and create_time &lt;= #{endDate} and create_time &gt;= #{startDate}
    and market_follow_up_user_id is not null group by market_follow_up_user_id
  </select>

  <select id="getValuableTransportCsUser" resultType="com.tyt.cargo.dispatch.web.mybatis.entity.base.CsBusinessUserBindVO">
    select cbub.id, cbub.login_phone_no as loginPhoneNo, cbub.real_name as realName, tcuvs.status, cbub.cs_department_id as departmentId
    from cs_business_user_bind cbub inner join tyt_cs_user_valuable_status tcuvs on cbub.id = tcuvs.cs_business_user_id
    where
      tcuvs.status = 1 and
      cbub.is_valid = 1 and (cbub.cs_department_id like '%3%' or cbub.cs_department_id like '%4%')
  </select>

  <select id="getAllCsDepartment" resultType="com.tyt.cargo.dispatch.web.mybatis.entity.base.CsDepartmentVO">
    select id as departmentId, role_name as departmentName
    from cs_department where id in (3, 4)
  </select>
</mapper>