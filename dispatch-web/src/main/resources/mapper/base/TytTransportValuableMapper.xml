<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.cargo.dispatch.web.mybatis.mapper.base.TytTransportValuableMapper">
	<resultMap id="BaseResultMap" type="com.tyt.cargo.dispatch.web.mybatis.entity.base.TytTransportValuable">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
		<result column="publish_user_id" jdbcType="VARCHAR" property="publishUserId" />
		<result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
		<result column="give_goods_phone" jdbcType="VARCHAR" property="giveGoodsPhone" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="owner_freight" jdbcType="DECIMAL" property="ownerFreight" />
		<result column="value_type" jdbcType="INTEGER" property="valueType" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
		<result column="follow_up" jdbcType="INTEGER" property="followUp" />
		<result column="first_order" jdbcType="INTEGER" property="firstOrder" />
		<result column="top_three_order" jdbcType="INTEGER" property="topThreeOrder" />
		<result column="follow_up_user_id" jdbcType="BIGINT" property="followUpUserId" />
		<result column="follow_up_user_name" jdbcType="VARCHAR" property="followUpUserName" />
		<result column="follow_up_time" jdbcType="TIMESTAMP" property="followUpTime" />
		<result column="market_follow_up_user_id" jdbcType="BIGINT" property="marketFollowUpUserId" />
		<result column="market_follow_up_user_name" jdbcType="VARCHAR" property="marketFollowUpUserName" />
		<result column="driver_give_price" jdbcType="INTEGER" property="driverGivePrice" />
		<result column="driver_latest_price" jdbcType="DECIMAL" property="driverLatestPrice" />
		<result column="direct_customer" jdbcType="INTEGER" property="directCustomer" />
		<result column="start_city" jdbcType="VARCHAR" property="startCity" />
	</resultMap>

	<select id="getValuableAllStatusList"
	        resultType="com.tyt.cargo.dispatch.client.vo.transport.TransportManageVO">
		select d.src_msg_id as srcMsgId,
		d.owner_freight as ownerFreight,
		d.publish_user_name as publishUserName,
		d.value_type as valueType,
		case d.value_type when 1 then '新客货源' when 2 then '优车2.0货源' when 3 then '新注册用户首月货源' when 4 then '历史用户首履货源' else '' end as valueTypeName,
		d.first_order as firstOrder,
		d.follow_up as followUp,
		d.top_three_order as topThreeOrder,
		d.follow_up_time as followUpTime,
		d.market_follow_up_user_name as marketFollowUpUser,
		d.follow_up_user_name as followUpUser,
		d.direct_customer as directCustomer,
		d.driver_give_price as driverGivePrice,
		d.driver_latest_price as driverLatestPrice,
		cmc.goods_maintainer_name as maintainerName,
		t.ctime as publishTime,
		t.mtime,
		t.start_point as startPoint,
		t.dest_point as destPoint,
		t.task_content as taskContent,
		t.machine_remark as machineRemark,
		t.good_type_name as goodTypeName,
		t.weight,
		t.length,
		t.wide,
		t.high,
		t.loading_time as loadingTime,
		t.unload_time as unloadTime,
		t.shunting_quantity as shuntingQuantity,
		t.excellent_goods as excellentGoods,
		t.info_fee as infoFee,
		t.price,
		t.distance as distance,
		t.publish_type as publishType,
		t.upload_cellphone as uploadCellphone,
		t.refund_flag as refundFlag,
		t.dest_detail_add as destDetailAdd,
		t.user_id as userId,
		t.start_detail_add as startDetailAdd,
		case when t.status = 1 and DATE_FORMAT(t.ctime, '%Y-%m-%d') = DATE_FORMAT(now(), '%Y-%m-%d') then 1
		when t.status = 1 and DATE_FORMAT(t.ctime, '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d') then 0
		else t.status end AS status,
		d.give_goods_phone as giveGoodsPhone,
		t.source_type as sourceType,
		t.tec_service_fee as tecServiceFee,
		t.label_json as labelJson,
		t.invoice_transport as invoiceTransport,
		t.additional_price as additionalPrice,
		t.enterprise_tax_rate as enterpriseTaxRate,
		ccr.not_deal_reason as notDealReason,
		ccr.follow_priority as followPriority,
		e.carry_user_id as doneUserId,
		e.ctime as doneTime
		from tyt_transport_valuable d
		inner join tyt_transport_main t on d.src_msg_id = t.src_msg_id
		left join tyt_transport_done e ON e.ts_id = t.id
		left join cs_maintained_custom  cmc on d.user_id = cmc.custom_id
		<!-- 一个货源有多个沟通记录，只看最新一条 -->
		LEFT JOIN tyt_special_car_contact_record ccr
		ON ccr.id = ( SELECT id FROM tyt_special_car_contact_record WHERE src_msg_id = t.src_msg_id ORDER BY id DESC LIMIT 1 )
		where t.status in (1, 4, 5)
		<if test="transportManageReq.followPriority != null">
			AND ccr.follow_priority = #{transportManageReq.followPriority}
		</if>
		<if test="transportManageReq.maintainerName != null and transportManageReq.maintainerName !=''">
			and cmc.goods_maintainer_name = #{transportManageReq.maintainerName}
		</if>
		<if test="transportManageReq.valueType != null">
			and d.value_type = #{transportManageReq.valueType}
		</if>
		<if test="transportManageReq.followUp != null">
			and d.follow_up = #{transportManageReq.followUp}
		</if>
		<if test="transportManageReq.firstOrder != null">
			and d.first_order = #{transportManageReq.firstOrder}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId != -1">
			and d.follow_up_user_id = #{transportManageReq.followUpUserId}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId == -1">
			and d.follow_up_user_id is null
		</if>
		<if test="transportManageReq.driverGivePrice != null">
			and d.driver_give_price = #{transportManageReq.driverGivePrice}
		</if>
		<if test="transportManageReq.directCustomer != null">
			and d.direct_customer = #{transportManageReq.directCustomer}
		</if>
		<choose>
			<when test="transportManageReq.publishStartTime != null and transportManageReq.publishStartTime != ''">
				and t.ctime &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
				and d.create_time &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
			</when>
			<otherwise>
				and t.ctime &gt;= CONCAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY),' 00:00:00')
				and d.create_time &gt;= CONCAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY),' 00:00:00')
			</otherwise>
		</choose>
		<choose>
			<when test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0 and transportManageReq.sourceType != 2">
				and t.source_type = #{transportManageReq.sourceType}
			</when>
			<otherwise>
				<if test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0">
					and t.source_type != 4 and t.source_type != 5
				</if>
			</otherwise>
		</choose>
		<!--优车 筛选 -->
		<if test="transportManageReq.excellentGoods != null">
			and t.excellent_goods = #{transportManageReq.excellentGoods}
		</if>
		<if test="transportManageReq.publishEndTime != null and transportManageReq.publishEndTime != ''">
			and t.ctime &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
			and d.create_time &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
		</if>
		<if test="transportManageReq.srcMsgId != null and transportManageReq.srcMsgId != ''">
			and d.src_msg_id = #{transportManageReq.srcMsgId}
		</if>
		<if test="transportManageReq.userId != null">
			and t.user_id = #{transportManageReq.userId}
		</if>
		<if test="transportManageReq.publishUserName != null and transportManageReq.publishUserName != ''">
			and d.publish_user_name = #{transportManageReq.publishUserName}
		</if>
		<if test="transportManageReq.startPoint != null and transportManageReq.startPoint != ''">
			and t.start_point like CONCAT('%', #{transportManageReq.startPoint}, '%')
		</if>
		<if test="transportManageReq.destPoint != null and transportManageReq.destPoint != ''">
			and t.dest_point like CONCAT('%', #{transportManageReq.destPoint}, '%')
		</if>
		<if test="transportManageReq.taskContent != null and transportManageReq.taskContent != ''">
			and t.task_content like CONCAT('%', #{transportManageReq.taskContent}, '%')
		</if>
		<if test="transportManageReq.publishType != null and transportManageReq.publishType != ''">
			and t.publish_type = #{transportManageReq.publishType}
		</if>
		<if test="transportManageReq.uploadCellphone != null and transportManageReq.uploadCellphone != ''">
			and t.upload_cellphone = #{transportManageReq.uploadCellphone}
		</if>
		<if test="transportManageReq.giveGoodsPhone != null and transportManageReq.giveGoodsPhone != ''">
			and d.give_goods_phone = #{transportManageReq.giveGoodsPhone}
		</if>
		<if test="transportManageReq.giveGoodsPhoneType !=null and transportManageReq.giveGoodsPhoneType == 1">
			and (d.give_goods_phone is null or d.give_goods_phone = '')
		</if>
		<if test="transportManageReq.csUserIdList != null and !transportManageReq.csUserIdList.isEmpty()">
			and (d.market_follow_up_user_id in
			<foreach collection="transportManageReq.csUserIdList" item="csUserId" open="(" separator="," close=")">
				#{csUserId}
			</foreach>
			<if test="transportManageReq.includeNull">
				or d.market_follow_up_user_id is null
			</if>
			)
		</if>
		<if test="transportManageReq.invoiceTransport !=null">
			and t.invoice_transport = #{transportManageReq.invoiceTransport}
		</if>
		<if test="transportManageReq.refundFlag !=null">
			and t.refund_flag = #{transportManageReq.refundFlag}
		</if>
		order by t.ctime desc
	</select>

	<select id="getValuablePublishingList"
	        resultType="com.tyt.cargo.dispatch.client.vo.transport.TransportManageVO">
		select d.src_msg_id as srcMsgId,
		d.owner_freight as ownerFreight,
		d.publish_user_name as publishUserName,
		d.value_type as valueType,
		case d.value_type when 1 then '新客货源' when 2 then '优车2.0货源' else '' end as valueTypeName,
		d.first_order as firstOrder,
		d.follow_up as followUp,
		d.top_three_order as topThreeOrder,
		d.follow_up_time as followUpTime,
		d.market_follow_up_user_name as marketFollowUpUser,
		d.follow_up_user_name as followUpUser,
		d.direct_customer as directCustomer,
		d.driver_give_price as driverGivePrice,
		d.driver_latest_price as driverLatestPrice,
		cmc.goods_maintainer_name as maintainerName,
		t.ctime as publishTime,
		t.mtime,
		t.start_point as startPoint,
		t.dest_point as destPoint,
		t.task_content as taskContent,
		t.good_type_name as goodTypeName,
		t.weight,
		t.length,
		t.wide,
		t.high,
		t.loading_time as loadingTime,
		t.unload_time as unloadTime,
		t.shunting_quantity as shuntingQuantity,
		t.excellent_goods as excellentGoods,
		t.info_fee as infoFee,
		t.price,
		t.distance,
		t.publish_type as publishType,
		t.upload_cellphone as uploadCellphone,
		t.refund_flag as refundFlag,
		t.dest_detail_add as destDetailAdd,
		t.user_id as userId,
		t.start_detail_add as startDetailAdd,
		t.status,
		d.give_goods_phone as giveGoodsPhone,
		t.source_type as sourceType,
		t.tec_service_fee tecServiceFee,
		t.label_json as labelJson,
		t.invoice_transport as invoiceTransport,
		t.additional_price as additionalPrice,
		t.enterprise_tax_rate as enterpriseTaxRate,
		ccr.not_deal_reason as notDealReason,
		ccr.follow_priority as followPriority
		from tyt_transport_valuable d
		left join tyt_transport_main t on d.src_msg_id = t.src_msg_id
		left join cs_maintained_custom  cmc on d.user_id = cmc.custom_id
		LEFT JOIN tyt_special_car_contact_record ccr
		ON ccr.id = ( SELECT id FROM tyt_special_car_contact_record WHERE src_msg_id = t.src_msg_id ORDER BY id DESC LIMIT 1 )
		where t.display_type= 1
		and t.status = 1
		and d.create_time &gt;= #{transportManageReq.todayZero}
		and t.ctime &gt;= #{transportManageReq.todayZero}
		<if test="transportManageReq.followPriority != null">
			AND ccr.follow_priority = #{transportManageReq.followPriority}
		</if>
		<if test="transportManageReq.maintainerName != null and transportManageReq.maintainerName !=''">
			and cmc.goods_maintainer_name = #{transportManageReq.maintainerName}
		</if>
		<if test="transportManageReq.valueType != null">
			and d.value_type = #{transportManageReq.valueType}
		</if>
		<if test="transportManageReq.followUp != null">
			and d.follow_up = #{transportManageReq.followUp}
		</if>
		<if test="transportManageReq.firstOrder != null">
			and d.first_order = #{transportManageReq.firstOrder}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId != -1">
			and d.follow_up_user_id = #{transportManageReq.followUpUserId}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId == -1">
			and d.follow_up_user_id is null
		</if>
		<if test="transportManageReq.driverGivePrice != null">
			and d.driver_give_price = #{transportManageReq.driverGivePrice}
		</if>
		<if test="transportManageReq.directCustomer != null">
			and d.direct_customer = #{transportManageReq.directCustomer}
		</if>
		<choose>
			<when test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0 and transportManageReq.sourceType != 2">
				and t.source_type = #{transportManageReq.sourceType}
			</when>
			<otherwise>
				<if test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0">
					and t.source_type != 4 and t.source_type != 5
				</if>
			</otherwise>
		</choose>
		<!--优车 筛选 -->
		<if test="transportManageReq.excellentGoods != null">
			and t.excellent_goods = #{transportManageReq.excellentGoods}
		</if>
		<if test="transportManageReq.publishStartTime != null and transportManageReq.publishStartTime != ''">
			and t.ctime &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
		</if>
		<if test="transportManageReq.publishEndTime != null and transportManageReq.publishEndTime != ''">
			and t.ctime &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
		</if>
		<if test="transportManageReq.srcMsgId != null and transportManageReq.srcMsgId != ''">
			and d.src_msg_id = #{transportManageReq.srcMsgId}
		</if>
		<if test="transportManageReq.userId != null">
			and t.user_id = #{transportManageReq.userId}
		</if>
		<if test="transportManageReq.publishUserName != null and transportManageReq.publishUserName != ''">
			and d.publish_user_name = #{transportManageReq.publishUserName}
		</if>
		<if test="transportManageReq.startPoint != null and transportManageReq.startPoint != ''">
			and t.start_point like CONCAT('%', #{transportManageReq.startPoint}, '%')
		</if>
		<if test="transportManageReq.destPoint != null and transportManageReq.destPoint != ''">
			and t.dest_point like CONCAT('%', #{transportManageReq.destPoint}, '%')
		</if>
		<if test="transportManageReq.taskContent != null and transportManageReq.taskContent != ''">
			and t.task_content like CONCAT('%', #{transportManageReq.taskContent}, '%')
		</if>
		<if test="transportManageReq.publishType != null and transportManageReq.publishType != ''">
			and t.publish_type = #{transportManageReq.publishType}
		</if>
		<if test="transportManageReq.uploadCellphone != null and transportManageReq.uploadCellphone != ''">
			and t.upload_cellphone = #{transportManageReq.uploadCellphone}
		</if>
		<if test="transportManageReq.giveGoodsPhone != null and transportManageReq.giveGoodsPhone != ''">
			and d.give_goods_phone = #{transportManageReq.giveGoodsPhone}
		</if>
		<if test="transportManageReq.giveGoodsPhoneType !=null and transportManageReq.giveGoodsPhoneType == 1">
			and (d.give_goods_phone is null or d.give_goods_phone = '')
		</if>
		<if test="transportManageReq.csUserIdList != null and !transportManageReq.csUserIdList.isEmpty()">
			and (d.market_follow_up_user_id in
			<foreach collection="transportManageReq.csUserIdList" item="csUserId" open="(" separator="," close=")">
				#{csUserId}
			</foreach>
			<if test="transportManageReq.includeNull">
				or d.market_follow_up_user_id is null
			</if>
			)
		</if>
		<if test="transportManageReq.refundFlag !=null">
			and t.refund_flag = #{transportManageReq.refundFlag}
		</if>
		order by t.ctime desc
	</select>

	<select id="getValuableCanceledList"
	        resultType="com.tyt.cargo.dispatch.client.vo.transport.TransportManageVO">
		select  d.src_msg_id as srcMsgId,
		d.owner_freight as ownerFreight,
		d.publish_user_name as publishUserName,
		d.value_type as valueType,
		case d.value_type when 1 then '新客货源' when 2 then '优车2.0货源' else '' end as valueTypeName,
		d.first_order as firstOrder,
		d.follow_up as followUp,
		d.top_three_order as topThreeOrder,
		d.follow_up_time as followUpTime,
		d.market_follow_up_user_name as marketFollowUpUser,
		d.follow_up_user_name as followUpUser,
		d.direct_customer as directCustomer,
		d.driver_give_price as driverGivePrice,
		d.driver_latest_price as driverLatestPrice,
		cmc.goods_maintainer_name as maintainerName,
		t.ctime as publishTime,
		t.mtime as cancelTime,
		t.start_point as startPoint,
		t.dest_point as destPoint,
		t.task_content as taskContent,
		t.good_type_name as goodTypeName,
		t.weight,
		t.length,
		t.wide,
		t.high,
		t.loading_time as loadingTime,
		t.unload_time as unloadTime,
		t.shunting_quantity as shuntingQuantity,
		t.refund_flag as refundFlag,
		t.excellent_goods as excellentGoods,
		t.info_fee as infoFee,
		t.price,
		t.distance,
		t.publish_type as publishType,
		t.upload_cellphone as uploadCellphone,
		t.dest_detail_add as destDetailAdd,
		t.user_id as userId,
		t.start_detail_add as startDetailAdd,
		t.status,
		d.give_goods_phone as giveGoodsPhone,
		t.source_type as sourceType,
		t.tec_service_fee tecServiceFee,
		t.label_json as labelJson,
		t.invoice_transport as invoiceTransport,
		t.additional_price as additionalPrice,
		t.enterprise_tax_rate as enterpriseTaxRate,
		ccr.not_deal_reason as notDealReason,
		ccr.follow_priority as followPriority
		from tyt_transport_valuable d
		left join tyt_transport_main t on d.src_msg_id = t.src_msg_id
		left join cs_maintained_custom  cmc on d.user_id = cmc.custom_id
		LEFT JOIN tyt_special_car_contact_record ccr
		ON ccr.id = ( SELECT id FROM tyt_special_car_contact_record WHERE src_msg_id = t.src_msg_id ORDER BY id DESC LIMIT 1 )
		where t.display_type= 1
		and t.status = 5
		<if test="transportManageReq.followPriority != null">
			AND ccr.follow_priority = #{transportManageReq.followPriority}
		</if>
		<if test="transportManageReq.maintainerName != null and transportManageReq.maintainerName !=''">
			and cmc.goods_maintainer_name = #{transportManageReq.maintainerName}
		</if>
		<if test="transportManageReq.valueType != null">
			and d.value_type = #{transportManageReq.valueType}
		</if>
		<if test="transportManageReq.followUp != null">
			and d.follow_up = #{transportManageReq.followUp}
		</if>
		<if test="transportManageReq.firstOrder != null">
			and d.first_order = #{transportManageReq.firstOrder}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId != -1">
			and d.follow_up_user_id = #{transportManageReq.followUpUserId}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId == -1">
			and d.follow_up_user_id is null
		</if>
		<if test="transportManageReq.driverGivePrice != null">
			and d.driver_give_price = #{transportManageReq.driverGivePrice}
		</if>
		<if test="transportManageReq.directCustomer != null">
			and d.direct_customer = #{transportManageReq.directCustomer}
		</if>
		<choose>
			<when test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0 and transportManageReq.sourceType != 2">
				and t.source_type = #{transportManageReq.sourceType}
			</when>
			<otherwise>
				<if test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0">
					and t.source_type != 4 and t.source_type != 5
				</if>
			</otherwise>
		</choose>
		<!--优车 筛选 -->
		<if test="transportManageReq.excellentGoods != null">
			and t.excellent_goods = #{transportManageReq.excellentGoods}
		</if>
		<if test="transportManageReq.publishStartTime != null and transportManageReq.publishStartTime != ''">
			and t.ctime &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
			and d.create_time &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
		</if>
		<if test="transportManageReq.publishEndTime != null and transportManageReq.publishEndTime != ''">
			and t.ctime &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
			and d.create_time &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
		</if>
		<if test="transportManageReq.srcMsgId != null and transportManageReq.srcMsgId != ''">
			and d.src_msg_id = #{transportManageReq.srcMsgId}
		</if>
		<if test="transportManageReq.userId != null">
			and t.user_id = #{transportManageReq.userId}
		</if>
		<if test="transportManageReq.publishUserName != null and transportManageReq.publishUserName != ''">
			and d.publish_user_name = #{transportManageReq.publishUserName}
		</if>
		<if test="transportManageReq.startPoint != null and transportManageReq.startPoint != ''">
			and t.start_point like CONCAT('%', #{transportManageReq.startPoint}, '%')
		</if>
		<if test="transportManageReq.destPoint != null and transportManageReq.destPoint != ''">
			and t.dest_point like CONCAT('%', #{transportManageReq.destPoint}, '%')
		</if>
		<if test="transportManageReq.taskContent != null and transportManageReq.taskContent != ''">
			and t.task_content like CONCAT('%', #{transportManageReq.taskContent}, '%')
		</if>
		<if test="transportManageReq.publishType != null and transportManageReq.publishType != ''">
			and t.publish_type = #{transportManageReq.publishType}
		</if>
		<if test="transportManageReq.uploadCellphone != null and transportManageReq.uploadCellphone != ''">
			and t.upload_cellphone = #{transportManageReq.uploadCellphone}
		</if>
		<if test="transportManageReq.giveGoodsPhone != null and transportManageReq.giveGoodsPhone != ''">
			and d.give_goods_phone = #{transportManageReq.giveGoodsPhone}
		</if>
		<if test="transportManageReq.giveGoodsPhoneType !=null and transportManageReq.giveGoodsPhoneType == 1">
			and (d.give_goods_phone is null and d.give_goods_phone = '')
		</if>
		<if test="transportManageReq.csUserIdList != null and !transportManageReq.csUserIdList.isEmpty()">
			and (d.market_follow_up_user_id in
			<foreach collection="transportManageReq.csUserIdList" item="csUserId" open="(" separator="," close=")">
				#{csUserId}
			</foreach>
			<if test="transportManageReq.includeNull">
				or d.market_follow_up_user_id is null
			</if>
			)
		</if>
		<if test="transportManageReq.refundFlag !=null">
			and t.refund_flag = #{transportManageReq.refundFlag}
		</if>
		order by t.mtime desc
	</select>

	<select id="getValuableExpiredList"
	        resultType="com.tyt.cargo.dispatch.client.vo.transport.TransportManageVO">
		select  d.src_msg_id as srcMsgId,
		d.owner_freight as ownerFreight,
		d.publish_user_name as publishUserName,
		d.value_type as valueType,
		case d.value_type when 1 then '新客货源' when 2 then '优车2.0货源' else '' end as valueTypeName,
		d.first_order as firstOrder,
		d.follow_up as followUp,
		d.top_three_order as topThreeOrder,
		d.follow_up_time as followUpTime,
		d.market_follow_up_user_name as marketFollowUpUser,
		d.follow_up_user_name as followUpUser,
		d.direct_customer as directCustomer,
		d.driver_give_price as driverGivePrice,
		d.driver_latest_price as driverLatestPrice,
		cmc.goods_maintainer_name as maintainerName,
		t.ctime as publishTime,
		t.start_point as startPoint,
		t.dest_point as destPoint,
		t.task_content as taskContent,
		t.good_type_name as goodTypeName,
		t.weight,
		t.length,
		t.wide,
		t.high,
		t.loading_time as loadingTime,
		t.unload_time as unloadTime,
		t.shunting_quantity as shuntingQuantity,
		t.refund_flag as refundFlag,
		t.excellent_goods as excellentGoods,
		t.info_fee as infoFee,
		t.price,
		t.distance,
		t.publish_type as publishType,
		t.upload_cellphone as uploadCellphone,
		t.dest_detail_add as destDetailAdd,
		t.user_id as userId,
		t.start_detail_add as startDetailAdd,
		0 as status,
		d.give_goods_phone as giveGoodsPhone,
		t.source_type as sourceType,
		t.tec_service_fee as tecServiceFee,
		t.label_json as labelJson,
		t.invoice_transport as invoiceTransport,
		t.additional_price as additionalPrice,
		t.enterprise_tax_rate as enterpriseTaxRate,
		ccr.not_deal_reason as notDealReason,
		ccr.follow_priority as followPriority
		from tyt_transport_valuable d
		left join tyt_transport_main t on d.src_msg_id = t.src_msg_id
		left join cs_maintained_custom  cmc on d.user_id = cmc.custom_id
		LEFT JOIN tyt_special_car_contact_record ccr
		ON ccr.id = ( SELECT id FROM tyt_special_car_contact_record WHERE src_msg_id = t.src_msg_id ORDER BY id DESC LIMIT 1 )
		where t.status = 1
		and t.ctime &lt; #{transportManageReq.todayZero}
		<if test="transportManageReq.followPriority != null">
			AND ccr.follow_priority = #{transportManageReq.followPriority}
		</if>
		<if test="transportManageReq.maintainerName != null and transportManageReq.maintainerName !=''">
			and cmc.goods_maintainer_name = #{transportManageReq.maintainerName}
		</if>
		<if test="transportManageReq.valueType != null">
			and d.value_type = #{transportManageReq.valueType}
		</if>
		<if test="transportManageReq.followUp != null">
			and d.follow_up = #{transportManageReq.followUp}
		</if>
		<if test="transportManageReq.firstOrder != null">
			and d.first_order = #{transportManageReq.firstOrder}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId != -1">
			and d.follow_up_user_id = #{transportManageReq.followUpUserId}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId == -1">
			and d.follow_up_user_id is null
		</if>
		<if test="transportManageReq.driverGivePrice != null">
			and d.driver_give_price = #{transportManageReq.driverGivePrice}
		</if>
		<if test="transportManageReq.directCustomer != null">
			and d.direct_customer = #{transportManageReq.directCustomer}
		</if>
		<choose>
			<when test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0 and transportManageReq.sourceType != 2">
				and t.source_type = #{transportManageReq.sourceType}
			</when>
			<otherwise>
				<if test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0">
					and t.source_type != 4 and t.source_type != 5
				</if>
			</otherwise>
		</choose>
		<!--优车 筛选 -->
		<if test="transportManageReq.excellentGoods != null">
			and t.excellent_goods = #{transportManageReq.excellentGoods}
		</if>
		<if test="transportManageReq.publishStartTime != null and transportManageReq.publishStartTime != ''">
			and t.ctime &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
			and d.create_time &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
		</if>
		<if test="transportManageReq.publishEndTime != null and transportManageReq.publishEndTime != ''">
			and t.ctime &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
			and d.create_time &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
		</if>
		<if test="transportManageReq.srcMsgId != null and transportManageReq.srcMsgId != ''">
			and d.src_msg_id = #{transportManageReq.srcMsgId}
		</if>
		<if test="transportManageReq.userId != null">
			and t.user_id = #{transportManageReq.userId}
		</if>
		<if test="transportManageReq.publishUserName != null and transportManageReq.publishUserName != ''">
			and d.publish_user_name = #{transportManageReq.publishUserName}
		</if>
		<if test="transportManageReq.startPoint != null and transportManageReq.startPoint != ''">
			and t.start_point like CONCAT('%', #{transportManageReq.startPoint}, '%')
		</if>
		<if test="transportManageReq.destPoint != null and transportManageReq.destPoint != ''">
			and t.dest_point like CONCAT('%', #{transportManageReq.destPoint}, '%')
		</if>
		<if test="transportManageReq.taskContent != null and transportManageReq.taskContent != ''">
			and t.task_content like CONCAT('%', #{transportManageReq.taskContent}, '%')
		</if>
		<if test="transportManageReq.publishType != null and transportManageReq.publishType != ''">
			and t.publish_type = #{transportManageReq.publishType}
		</if>
		<if test="transportManageReq.uploadCellphone != null and transportManageReq.uploadCellphone != ''">
			and t.upload_cellphone = #{transportManageReq.uploadCellphone}
		</if>
		<if test="transportManageReq.giveGoodsPhone != null and transportManageReq.giveGoodsPhone != ''">
			and d.give_goods_phone = #{transportManageReq.giveGoodsPhone}
		</if>
		<if test="transportManageReq.giveGoodsPhoneType !=null and transportManageReq.giveGoodsPhoneType == 1">
			and (d.give_goods_phone is null or d.give_goods_phone = '')
		</if>
		<if test="transportManageReq.csUserIdList != null and !transportManageReq.csUserIdList.isEmpty()">
			and (d.market_follow_up_user_id in
			<foreach collection="transportManageReq.csUserIdList" item="csUserId" open="(" separator="," close=")">
				#{csUserId}
			</foreach>
			<if test="transportManageReq.includeNull">
				or d.market_follow_up_user_id is null
			</if>
			)
		</if>
		<if test="transportManageReq.refundFlag !=null">
			and t.refund_flag = #{transportManageReq.refundFlag}
		</if>
		order by t.ctime desc
	</select>

	<select id="getValuableDoneList"
	        resultType="com.tyt.cargo.dispatch.client.vo.transport.TransportManageVO">
		select  d.src_msg_id as srcMsgId,
		d.owner_freight as ownerFreight,
		d.publish_user_name as publishUserName,
		d.value_type as valueType,
		case d.value_type when 1 then '新客货源' when 2 then '优车2.0货源' else '' end as valueTypeName,
		d.first_order as firstOrder,
		d.follow_up as followUp,
		d.top_three_order as topThreeOrder,
		d.follow_up_time as followUpTime,
		d.market_follow_up_user_name as marketFollowUpUser,
		d.follow_up_user_name as followUpUser,
		d.direct_customer as directCustomer,
		d.driver_give_price as driverGivePrice,
		d.driver_latest_price as driverLatestPrice,
		cmc.goods_maintainer_name as maintainerName,
		t.ctime as publishTime,
		t.start_point as startPoint,
		t.dest_point as destPoint,
		t.task_content as taskContent,
		t.good_type_name as goodTypeName,
		t.weight,
		t.length,
		t.wide,
		t.high,
		t.loading_time as loadingTime,
		t.unload_time as unloadTime,
		t.shunting_quantity as shuntingQuantity,
		t.refund_flag as refundFlag,
		t.excellent_goods as excellentGoods,
		t.info_fee as infoFee,
		t.price,
		t.distance,
		t.publish_type as publishType,
		t.upload_cellphone as uploadCellphone,
		t.dest_detail_add as destDetailAdd,
		t.user_id as userId,
		t.start_detail_add as startDetailAdd,
		4 as status,
		d.give_goods_phone as giveGoodsPhone,
		t.source_type as sourceType,
		t.tec_service_fee as tecServiceFee,
		t.label_json as labelJson,
		t.invoice_transport as invoiceTransport,
		t.additional_price as additionalPrice,
		t.enterprise_tax_rate as enterpriseTaxRate,
		ccr.not_deal_reason as notDealReason,
		ccr.follow_priority as followPriority,
		e.carry_user_id as doneUserId,
		e.ctime as doneTime
		from tyt_transport_valuable d
		left join tyt_transport_main t on d.src_msg_id = t.src_msg_id
		inner join tyt_transport_done e ON e.ts_id = t.id
		left join cs_maintained_custom  cmc on d.user_id = cmc.custom_id
		LEFT JOIN tyt_special_car_contact_record ccr
		ON ccr.id = ( SELECT id FROM tyt_special_car_contact_record WHERE src_msg_id = t.src_msg_id ORDER BY id DESC LIMIT 1 )
		where t.display_type= 1
		<if test="transportManageReq.followPriority != null">
			AND ccr.follow_priority = #{transportManageReq.followPriority}
		</if>
		<if test="transportManageReq.maintainerName != null and transportManageReq.maintainerName !=''">
			and cmc.goods_maintainer_name = #{transportManageReq.maintainerName}
		</if>
		<if test="transportManageReq.valueType != null">
			and d.value_type = #{transportManageReq.valueType}
		</if>
		<if test="transportManageReq.followUp != null">
			and d.follow_up = #{transportManageReq.followUp}
		</if>
		<if test="transportManageReq.firstOrder != null">
			and d.first_order = #{transportManageReq.firstOrder}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId != -1">
			and d.follow_up_user_id = #{transportManageReq.followUpUserId}
		</if>
		<if test="transportManageReq.followUpUserId != null and transportManageReq.followUpUserId == -1">
			and d.follow_up_user_id is null
		</if>
		<if test="transportManageReq.driverGivePrice != null">
			and d.driver_give_price = #{transportManageReq.driverGivePrice}
		</if>
		<if test="transportManageReq.directCustomer != null">
			and d.direct_customer = #{transportManageReq.directCustomer}
		</if>
		<choose>
			<when test="transportManageReq.publishStartTime != null and transportManageReq.publishStartTime != ''">
				and t.ctime &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
				and d.create_time &gt;= CONCAT(#{transportManageReq.publishStartTime},' 00:00:00')
			</when>
			<otherwise>
				and t.ctime &gt;= CONCAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY),' 00:00:00')
				and d.create_time &gt;= CONCAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY),' 00:00:00')
			</otherwise>
		</choose>
		<choose>
			<when test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0 and transportManageReq.sourceType != 2">
				and t.source_type = #{transportManageReq.sourceType}
			</when>
			<otherwise>
				<if test="transportManageReq.sourceType != null and transportManageReq.sourceType != 0">
					and t.source_type != 4 and t.source_type != 5
				</if>
			</otherwise>
		</choose>
		<!--优车 筛选 -->
		<if test="transportManageReq.excellentGoods != null">
			and t.excellent_goods = #{transportManageReq.excellentGoods}
		</if>
		<if test="transportManageReq.publishEndTime != null and transportManageReq.publishEndTime != ''">
			and t.ctime &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
			and d.create_time &lt;= CONCAT(#{transportManageReq.publishEndTime},' 23:59:59')
		</if>
		<if test="transportManageReq.srcMsgId != null and transportManageReq.srcMsgId != ''">
			and t.user_id = #{transportManageReq.srcMsgId}
		</if>
		<if test="transportManageReq.userId != null">
			and d.user_id = #{transportManageReq.userId}
		</if>
		<if test="transportManageReq.publishUserName != null and transportManageReq.publishUserName != ''">
			and d.publish_user_name = #{transportManageReq.publishUserName}
		</if>
		<if test="transportManageReq.startPoint != null and transportManageReq.startPoint != ''">
			and t.start_point like CONCAT('%', #{transportManageReq.startPoint}, '%')
		</if>
		<if test="transportManageReq.destPoint != null and transportManageReq.destPoint != ''">
			and t.dest_point like CONCAT('%', #{transportManageReq.destPoint}, '%')
		</if>
		<if test="transportManageReq.taskContent != null and transportManageReq.taskContent != ''">
			and t.task_content like CONCAT('%', #{transportManageReq.taskContent}, '%')
		</if>
		<if test="transportManageReq.publishType != null and transportManageReq.publishType != ''">
			and t.publish_type = #{transportManageReq.publishType}
		</if>
		<if test="transportManageReq.uploadCellphone != null and transportManageReq.uploadCellphone != ''">
			and t.upload_cellphone = #{transportManageReq.uploadCellphone}
		</if>
		<if test="transportManageReq.giveGoodsPhone != null and transportManageReq.giveGoodsPhone != ''">
			and d.give_goods_phone = #{transportManageReq.giveGoodsPhone}
		</if>
		<if test="transportManageReq.giveGoodsPhoneType !=null and transportManageReq.giveGoodsPhoneType == 1">
			and (d.give_goods_phone is null or d.give_goods_phone = '')
		</if>
		<if test="transportManageReq.csUserIdList != null and !transportManageReq.csUserIdList.isEmpty()">
			and (d.market_follow_up_user_id in
			<foreach collection="transportManageReq.csUserIdList" item="csUserId" open="(" separator="," close=")">
				#{csUserId}
			</foreach>
			<if test="transportManageReq.includeNull">
				or d.market_follow_up_user_id is null
			</if>
			)
		</if>
		<if test="transportManageReq.refundFlag !=null">
			and t.refund_flag = #{transportManageReq.refundFlag}
		</if>
		order by t.ctime desc
	</select>

	<select id="countByMarketFollowupUser" resultType="java.lang.Integer">
		select count(*)
		from tyt_transport_valuable
		where create_time &lt; #{createTime} and create_time >= CURDATE() and follow_up = 0
		  and market_follow_up_user_id = #{marketFollowupUserId}
	</select>

	<select id="countByCsFollowupUser" resultType="java.lang.Integer">
		select count(*)
		from tyt_transport_valuable
		where create_time &lt; #{createTime} and create_time >= CURDATE() and follow_up = 0
		  and market_follow_up_user_id is null
	</select>

	<select id="getValuableDetailVOBySrcMsgId"
	        resultType="com.tyt.cargo.dispatch.client.vo.transport.ValuableTransportDetailVO">
		select first_order as firstOrder,
		       top_three_order as topThreeOrder,
		       direct_customer as directCustomer,
		       driver_give_price as driverGivePrice,
		       driver_latest_price as driverLatestPrice
		from tyt_transport_valuable
		where src_msg_id = #{srcMsgId}
		limit 1
	</select>

	<select id="getValuableBySrcMsgId" resultMap="BaseResultMap">
		select *
		from tyt_transport_valuable
		where src_msg_id = #{srcMsgId}
		limit 1
	</select>

	<update id="changeValuableCsUser">
		update tyt_transport_valuable set market_follow_up_user_id = #{csUserId}, market_follow_up_user_name = #{csUserName}
		where src_msg_id in
		<foreach collection="srcMsgIds" open="(" close=")" item="srcMsgId" separator=",">
			#{srcMsgId}
		</foreach>
	</update>

	<update id="updateValuableTransportCsUserStatus">
		replace into tyt_cs_user_valuable_status (cs_business_user_id, status, designate_status, create_time, modify_time) VALUE (#{csUserId}, #{status}, #{designateStatus}, now(), now())
	</update>

	<insert id="insertValuableLog">
		insert into tyt_cs_user_valuable_assign_log (cs_business_user_id, src_msg_id, type, create_time) VALUE (#{csUserId}, #{srcMsgId}, 2, now())
	</insert>

	<select id="getValuableTransportCsUserDesignateStatusByCsUserId" resultType="java.lang.Integer">
		select designate_status
		from tyt_cs_user_valuable_status
		where cs_business_user_id = #{csUserId}
	</select>

	<select id="getValuableTransportCsUserStatusByCsUserId" resultType="java.lang.Integer">
		select status
		from tyt_cs_user_valuable_status
		where cs_business_user_id = #{csUserId}
	</select>
</mapper>